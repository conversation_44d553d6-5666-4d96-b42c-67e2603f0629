"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/landing-page.tsx":
/*!*************************************!*\
  !*** ./components/landing-page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LandingPage: () => (/* binding */ LandingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./contexts/auth-context.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/microscope.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _components_ui_effects_particle_background__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui-effects/particle-background */ \"(app-pages-browser)/./components/ui-effects/particle-background.tsx\");\n/* harmony import */ var _components_ui_effects_statistic_counter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui-effects/statistic-counter */ \"(app-pages-browser)/./components/ui-effects/statistic-counter.tsx\");\n/* harmony import */ var _components_landing_testimonial_carousel__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/landing/testimonial-carousel */ \"(app-pages-browser)/./components/landing/testimonial-carousel.tsx\");\n/* harmony import */ var _components_landing_feature_showcase__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/landing/feature-showcase */ \"(app-pages-browser)/./components/landing/feature-showcase.tsx\");\n/* harmony import */ var _components_landing_ai_demo__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/landing/ai-demo */ \"(app-pages-browser)/./components/landing/ai-demo.tsx\");\n/* harmony import */ var _components_visualization_3d_cell_structure__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/visualization/3d-cell-structure */ \"(app-pages-browser)/./components/visualization/3d-cell-structure.tsx\");\n/* harmony import */ var _components_visualization_full_body_neural_network_3d__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/visualization/full-body-neural-network-3d */ \"(app-pages-browser)/./components/visualization/full-body-neural-network-3d.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_14__);\n/* __next_internal_client_entry_do_not_use__ LandingPage auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction LandingPage() {\n    _s();\n    const [currentFeature, setCurrentFeature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isAuthenticated, isLoading } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            setMounted(true);\n            setIsVisible(true);\n            const interval = setInterval({\n                \"LandingPage.useEffect.interval\": ()=>{\n                    setCurrentFeature({\n                        \"LandingPage.useEffect.interval\": (prev)=>(prev + 1) % 4\n                    }[\"LandingPage.useEffect.interval\"]);\n                }\n            }[\"LandingPage.useEffect.interval\"], 5000);\n            return ({\n                \"LandingPage.useEffect\": ()=>clearInterval(interval)\n            })[\"LandingPage.useEffect\"];\n        }\n    }[\"LandingPage.useEffect\"], []);\n    // Redirect authenticated users to dashboard\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            if (mounted && !isLoading && isAuthenticated) {\n                router.push('/dashboard');\n            }\n        }\n    }[\"LandingPage.useEffect\"], [\n        mounted,\n        isLoading,\n        isAuthenticated,\n        router\n    ]);\n    // Prevent hydration mismatch by not rendering until mounted\n    if (!mounted) {\n        return null;\n    }\n    const features = [\n        {\n            icon: _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            title: \"AI-Powered Diagnosis\",\n            description: \"Advanced machine learning algorithms for accurate brain tumor detection and classification\",\n            color: \"text-teal-400\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            title: \"Real-time Monitoring\",\n            description: \"Continuous patient monitoring with intelligent alerts and predictive analytics\",\n            color: \"text-blue-400\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            title: \"Predictive Analytics\",\n            description: \"Data-driven insights for treatment planning and outcome prediction\",\n            color: \"text-green-400\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            title: \"Secure & Compliant\",\n            description: \"HIPAA-compliant platform with enterprise-grade security and data protection\",\n            color: \"text-purple-400\"\n        }\n    ];\n    const stats = [\n        {\n            label: \"Patients Treated\",\n            value: 10000,\n            suffix: \"+\"\n        },\n        {\n            label: \"Diagnostic Accuracy\",\n            value: 97.3,\n            suffix: \"%\"\n        },\n        {\n            label: \"Healthcare Partners\",\n            value: 250,\n            suffix: \"+\"\n        },\n        {\n            label: \"Countries Served\",\n            value: 15,\n            suffix: \"\"\n        }\n    ];\n    const benefits = [\n        \"Reduce diagnostic time by 60%\",\n        \"Improve accuracy by 25%\",\n        \"24/7 AI-powered monitoring\",\n        \"Seamless EHR integration\",\n        \"Real-time collaboration tools\",\n        \"Comprehensive reporting suite\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-teal-900 to-slate-900 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_effects_particle_background__WEBPACK_IMPORTED_MODULE_7__.ParticleBackground, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"relative z-50 bg-black/20 backdrop-blur-xl border-b border-teal-500/30\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-8 w-8 text-teal-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl sm:text-2xl font-bold text-teal-400\",\n                                        children: \"Quant-NEX\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex items-center gap-6 lg:gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#features\",\n                                        className: \"text-gray-300 hover:text-teal-400 transition-colors text-sm lg:text-base\",\n                                        children: \"Features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#demo\",\n                                        className: \"text-gray-300 hover:text-teal-400 transition-colors text-sm lg:text-base\",\n                                        children: \"Demo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#testimonials\",\n                                        className: \"text-gray-300 hover:text-teal-400 transition-colors text-sm lg:text-base\",\n                                        children: \"Testimonials\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#pricing\",\n                                        className: \"text-gray-300 hover:text-teal-400 transition-colors text-sm lg:text-base\",\n                                        children: \"Pricing\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 sm:gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_14___default()), {\n                                        href: \"/login\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"glow-hover bg-transparent text-xs sm:text-sm\",\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_14___default()), {\n                                        href: \"/login\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            size: \"sm\",\n                                            className: \"btn-glow-primary text-xs sm:text-sm\",\n                                            children: \"Get Started\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative z-10 pt-12 sm:pt-16 lg:pt-20 pb-16 sm:pb-24 lg:pb-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6 lg:space-y-8 \".concat(isVisible ? \"animate-slide-up\" : \"opacity-0\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                className: \"bg-teal-600/20 text-teal-400 border-teal-500/30 text-xs sm:text-sm\",\n                                                children: \"\\uD83D\\uDE80 Next-Generation Medical AI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold text-white leading-tight\",\n                                                children: [\n                                                    \"Revolutionizing\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"block text-transparent bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text\",\n                                                        children: \"Brain Tumor\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Diagnosis\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg sm:text-xl text-gray-300 leading-relaxed max-w-2xl\",\n                                                children: \"Harness the power of AI to transform medical diagnosis, treatment planning, and patient care. Join thousands of healthcare professionals using Quant-NEX to save lives.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_14___default()), {\n                                                href: \"/login\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    size: \"lg\",\n                                                    className: \"btn-glow-primary text-base sm:text-lg px-6 sm:px-8 py-4 sm:py-6 w-full sm:w-auto\",\n                                                    children: [\n                                                        \"Start Free Trial\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"ml-2 h-4 w-4 sm:h-5 sm:w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                size: \"lg\",\n                                                variant: \"outline\",\n                                                className: \"glow-hover bg-transparent text-base sm:text-lg px-6 sm:px-8 py-4 sm:py-6 w-full sm:w-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4 sm:h-5 sm:w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Watch Demo\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row items-start sm:items-center gap-4 sm:gap-6 pt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    ...Array(5)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-4 w-4 sm:h-5 sm:w-5 text-yellow-400 fill-current\"\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm sm:text-base text-gray-300\",\n                                                children: \"Trusted by 250+ healthcare institutions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative mt-8 lg:mt-0 \".concat(isVisible ? \"animate-slide-up\" : \"opacity-0\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-teal-400/20 to-blue-400/20 rounded-3xl blur-3xl\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_visualization_3d_cell_structure__WEBPACK_IMPORTED_MODULE_12__.CellStructure3D, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative z-10 py-12 sm:py-16 lg:py-20 bg-black/20 backdrop-blur-xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8\",\n                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold text-teal-400 mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_effects_statistic_counter__WEBPACK_IMPORTED_MODULE_8__.StatisticCounter, {\n                                            end: stat.value,\n                                            suffix: stat.suffix\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm sm:text-base text-gray-300\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                className: \"relative z-10 py-16 sm:py-24 lg:py-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12 lg:mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-4 lg:mb-6\",\n                                    children: [\n                                        \"Powerful Features for\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-transparent bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text\",\n                                            children: \"Modern Healthcare\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg sm:text-xl text-gray-300 max-w-3xl mx-auto\",\n                                    children: \"Our comprehensive platform combines cutting-edge AI technology with intuitive design to deliver unprecedented capabilities for medical professionals.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center mb-12 lg:mb-20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6 lg:space-y-8\",\n                                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 lg:p-6 rounded-xl border transition-all duration-500 cursor-pointer \".concat(currentFeature === index ? \"border-teal-400 bg-teal-900/20 shadow-lg shadow-teal-400/20\" : \"border-teal-500/30 bg-black/20 hover:border-teal-400/50\"),\n                                            onClick: ()=>setCurrentFeature(index),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 rounded-lg bg-gradient-to-r from-teal-500/20 to-blue-500/20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                            className: \"h-5 w-5 lg:h-6 lg:w-6 \".concat(feature.color)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg lg:text-xl font-semibold text-white mb-2\",\n                                                                children: feature.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm lg:text-base text-gray-300\",\n                                                                children: feature.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative mt-8 lg:mt-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_feature_showcase__WEBPACK_IMPORTED_MODULE_10__.FeatureShowcase, {\n                                        currentFeature: currentFeature\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6\",\n                            children: benefits.map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 p-3 lg:p-4 rounded-lg bg-black/20 border border-teal-500/30\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-4 w-4 lg:h-5 lg:w-5 text-green-400 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm lg:text-base text-gray-300\",\n                                            children: benefit\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"demo\",\n                className: \"relative z-10 py-16 sm:py-24 lg:py-32 bg-black/20 backdrop-blur-xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12 lg:mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-4 lg:mb-6\",\n                                    children: [\n                                        \"Experience AI in\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-transparent bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text\",\n                                            children: \"Action\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg sm:text-xl text-gray-300 max-w-3xl mx-auto\",\n                                    children: \"See how our advanced AI algorithms analyze medical data in real-time to provide accurate diagnoses and treatment recommendations.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 xl:grid-cols-2 gap-6 lg:gap-8 mb-8 lg:mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"card-glow border-teal-500/30 bg-black/40 backdrop-blur-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"text-teal-400 flex items-center gap-2 text-lg lg:text-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-5 w-5 lg:h-6 lg:w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Cellular Structure Analysis\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"pt-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_visualization_3d_cell_structure__WEBPACK_IMPORTED_MODULE_12__.CellStructure3D, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"card-glow border-teal-500/30 bg-black/40 backdrop-blur-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"text-teal-400 flex items-center gap-2 text-lg lg:text-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-5 w-5 lg:h-6 lg:w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Full Body Neural Network\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"pt-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_visualization_full_body_neural_network_3d__WEBPACK_IMPORTED_MODULE_13__.FullBodyNeuralNetwork3D, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 lg:mt-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_ai_demo__WEBPACK_IMPORTED_MODULE_11__.AIDemo, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 290,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"testimonials\",\n                className: \"relative z-10 py-16 sm:py-24 lg:py-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12 lg:mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-4 lg:mb-6\",\n                                    children: [\n                                        \"Trusted by\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-transparent bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text\",\n                                            children: \"Healthcare Leaders\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg sm:text-xl text-gray-300 max-w-3xl mx-auto\",\n                                    children: \"Join thousands of medical professionals who are already transforming patient care with Quant-NEX.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_testimonial_carousel__WEBPACK_IMPORTED_MODULE_9__.TestimonialCarousel, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-12 lg:mt-16 grid grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-10 w-10 lg:h-12 lg:w-12 text-teal-400 mx-auto mb-3 lg:mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-2 text-sm lg:text-base\",\n                                            children: \"Global Reach\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs lg:text-sm text-gray-400\",\n                                            children: \"15+ countries worldwide\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-10 w-10 lg:h-12 lg:w-12 text-teal-400 mx-auto mb-3 lg:mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-2 text-sm lg:text-base\",\n                                            children: \"Award Winning\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs lg:text-sm text-gray-400\",\n                                            children: \"Healthcare Innovation Award 2024\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-10 w-10 lg:h-12 lg:w-12 text-teal-400 mx-auto mb-3 lg:mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-2 text-sm lg:text-base\",\n                                            children: \"HIPAA Compliant\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs lg:text-sm text-gray-400\",\n                                            children: \"Enterprise-grade security\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            className: \"h-10 w-10 lg:h-12 lg:w-12 text-teal-400 mx-auto mb-3 lg:mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-2 text-sm lg:text-base\",\n                                            children: \"97.3% Accuracy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs lg:text-sm text-gray-400\",\n                                            children: \"Clinically validated\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"pricing\",\n                className: \"relative z-10 py-16 sm:py-24 lg:py-32 bg-black/20 backdrop-blur-xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12 lg:mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-4 lg:mb-6\",\n                                    children: [\n                                        \"Choose Your\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-transparent bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text\",\n                                            children: \"Plan\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg sm:text-xl text-gray-300 max-w-3xl mx-auto\",\n                                    children: \"Flexible pricing options designed to scale with your healthcare organization's needs.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"card-glow border-teal-500/30 bg-black/40 backdrop-blur-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    className: \"text-white text-lg lg:text-xl\",\n                                                    children: \"Starter\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl lg:text-3xl font-bold text-teal-400\",\n                                                    children: [\n                                                        \"₹9,999\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-base lg:text-lg text-gray-400\",\n                                                            children: \"/month\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Up to 100 patients\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 411,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Basic AI diagnosis\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 412,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 415,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Standard reporting\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 416,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 419,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Email support\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 420,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    className: \"w-full btn-glow-primary\",\n                                                    children: \"Start Free Trial\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"card-glow border-teal-400 bg-teal-900/20 backdrop-blur-xl relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-4 left-1/2 transform -translate-x-1/2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                className: \"bg-teal-600 text-white\",\n                                                children: \"Most Popular\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    className: \"text-white text-lg lg:text-xl\",\n                                                    children: \"Professional\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl lg:text-3xl font-bold text-teal-400\",\n                                                    children: [\n                                                        \"₹24,999\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-base lg:text-lg text-gray-400\",\n                                                            children: \"/month\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 26\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 441,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Up to 500 patients\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 442,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Advanced AI features\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 446,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 449,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Real-time monitoring\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 450,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 453,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Priority support\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 454,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 457,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"API access\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    className: \"w-full btn-glow-primary\",\n                                                    children: \"Start Free Trial\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"card-glow border-teal-500/30 bg-black/40 backdrop-blur-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    className: \"text-white text-lg lg:text-xl\",\n                                                    children: \"Enterprise\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl lg:text-3xl font-bold text-teal-400\",\n                                                    children: [\n                                                        \"Custom\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-base lg:text-lg text-gray-400\",\n                                                            children: \" pricing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 476,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Unlimited patients\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 477,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 480,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Full AI suite\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 481,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 484,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Custom integrations\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 485,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 483,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 488,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"24/7 dedicated support\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 489,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 492,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"On-premise deployment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 493,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"w-full glow-hover bg-transparent\",\n                                                    children: \"Contact Sales\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 382,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 381,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative z-10 py-16 sm:py-24 lg:py-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6 lg:space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-white\",\n                                children: [\n                                    \"Ready to Transform\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-transparent bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text\",\n                                        children: \"Healthcare?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg sm:text-xl text-gray-300\",\n                                children: \"Join thousands of healthcare professionals who are already using Quant-NEX to improve patient outcomes and streamline their workflow. Start your free trial today.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 515,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_14___default()), {\n                                        href: \"/login\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            size: \"lg\",\n                                            className: \"btn-glow-primary text-base sm:text-lg px-6 sm:px-8 py-4 sm:py-6 w-full sm:w-auto\",\n                                            children: [\n                                                \"Start Free Trial\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"ml-2 h-4 w-4 sm:h-5 sm:w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        size: \"lg\",\n                                        variant: \"outline\",\n                                        className: \"glow-hover bg-transparent text-base sm:text-lg px-6 sm:px-8 py-4 sm:py-6 w-full sm:w-auto\",\n                                        children: \"Schedule Demo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 519,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                        lineNumber: 508,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 507,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 506,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"relative z-10 bg-black/40 backdrop-blur-xl border-t border-teal-500/30\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 lg:py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6 lg:gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-6 w-6 lg:h-8 lg:w-8 text-teal-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl lg:text-2xl font-bold text-teal-400\",\n                                                    children: \"Quant-NEX\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm lg:text-base text-gray-400\",\n                                            children: \"Revolutionizing healthcare with AI-powered medical diagnosis and treatment planning.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 545,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-4 text-sm lg:text-base\",\n                                            children: \"Product\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-400 text-sm lg:text-base\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Pricing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 564,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 563,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"API\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Integrations\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-4 text-sm lg:text-base\",\n                                            children: \"Company\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 582,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-400 text-sm lg:text-base\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"About\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Careers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Press\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Contact\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 581,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-4 text-sm lg:text-base\",\n                                            children: \"Support\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-400 text-sm lg:text-base\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Documentation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Help Center\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Community\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 621,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 620,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 626,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 607,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 544,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-teal-500/30 mt-8 lg:mt-12 pt-6 lg:pt-8 flex flex-col md:flex-row justify-between items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm lg:text-base text-gray-400\",\n                                    children: \"\\xa9 2024 Quant-NEX. All rights reserved.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 635,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 lg:gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-sm lg:text-base text-gray-400 hover:text-teal-400 transition-colors\",\n                                            children: \"Privacy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 637,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-sm lg:text-base text-gray-400 hover:text-teal-400 transition-colors\",\n                                            children: \"Terms\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-sm lg:text-base text-gray-400 hover:text-teal-400 transition-colors\",\n                                            children: \"Security\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 643,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 636,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 634,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 543,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 542,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingPage, \"5Z88acZXoU12QoilDTq2d9SobFk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/landing-page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./contexts/auth-context.tsx":
/*!***********************************!*\
  !*** ./contexts/auth-context.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_indian_backend_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/indian-backend-service */ \"(app-pages-browser)/./lib/indian-backend-service.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Check for existing user session\n            const checkAuth = {\n                \"AuthProvider.useEffect.checkAuth\": async ()=>{\n                    try {\n                        const currentUser = await _lib_indian_backend_service__WEBPACK_IMPORTED_MODULE_2__.indianBackendService.getCurrentUser();\n                        if (currentUser) {\n                            setUser(currentUser);\n                        }\n                    } catch (error) {\n                        console.error(\"Auth check error:\", error);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkAuth\"];\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = (userData)=>{\n        setUser(userData);\n        if (true) {\n            localStorage.setItem(\"quantnex-user\", JSON.stringify(userData));\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await _lib_indian_backend_service__WEBPACK_IMPORTED_MODULE_2__.indianBackendService.logout();\n            setUser(null);\n            if (true) {\n                localStorage.removeItem(\"quantnex-user\");\n                localStorage.removeItem(\"quantnex-token\");\n            }\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        }\n    };\n    const value = {\n        user,\n        isLoading,\n        login,\n        logout,\n        isAuthenticated: !!user\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\contexts\\\\auth-context.tsx\",\n        lineNumber: 67,\n        columnNumber: 10\n    }, this);\n}\n_s(AuthProvider, \"YajQB7LURzRD+QP5gw0+K2TZIWA=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/auth-context.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/indian-backend-service.ts":
/*!***************************************!*\
  !*** ./lib/indian-backend-service.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   indianBackendService: () => (/* binding */ indianBackendService)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/polyfills/process.js\");\n// Enhanced Indian Backend Service with proper error handling\n// Demo users for fallback authentication\nconst DEMO_USERS = [\n    {\n        id: \"demo-1\",\n        email: \"<EMAIL>\",\n        name: \"Dr. Priya Patel\",\n        role: \"Oncologist\",\n        hospital: \"Tata Memorial Hospital\",\n        department: \"Neuro-Oncology\"\n    },\n    {\n        id: \"demo-2\",\n        email: \"<EMAIL>\",\n        name: \"Dr. Amit Gupta\",\n        role: \"Surgeon\",\n        hospital: \"AIIMS Delhi\",\n        department: \"Cancer Surgery\"\n    },\n    {\n        id: \"demo-3\",\n        email: \"<EMAIL>\",\n        name: \"Rajesh Kumar\",\n        role: \"Administrator\",\n        hospital: \"Apollo Hospital\",\n        department: \"Administration\"\n    }\n];\nclass IndianBackendService {\n    async login(credentials) {\n        try {\n            // If Firebase is not configured or we're in demo mode, use demo authentication\n            if (this.isDemoMode) {\n                return this.demoLogin(credentials);\n            }\n            // Try Firebase authentication first\n            try {\n                const { signInWithEmailAndPassword } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_pnpm_firebase_11_10_0_node_modules_firebase_auth_dist_esm_ind-15c111\").then(__webpack_require__.bind(__webpack_require__, /*! firebase/auth */ \"(app-pages-browser)/./node_modules/.pnpm/firebase@11.10.0/node_modules/firebase/auth/dist/esm/index.esm.js\"));\n                const { auth } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_lib_firebase_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./firebase */ \"(app-pages-browser)/./lib/firebase.ts\"));\n                const userCredential = await signInWithEmailAndPassword(auth, credentials.email, credentials.password);\n                return {\n                    success: true,\n                    user: {\n                        id: userCredential.user.uid,\n                        email: userCredential.user.email || \"\",\n                        name: userCredential.user.displayName || \"User\",\n                        role: \"Doctor\"\n                    },\n                    token: await userCredential.user.getIdToken()\n                };\n            } catch (firebaseError) {\n                console.warn(\"Firebase authentication failed, falling back to demo mode:\", firebaseError.message);\n                // If Firebase fails, fall back to demo mode\n                return this.demoLogin(credentials);\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            // Final fallback to demo mode\n            return this.demoLogin(credentials);\n        }\n    }\n    async demoLogin(credentials) {\n        // Simulate API delay\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        // Check demo credentials\n        const user = DEMO_USERS.find((u)=>u.email === credentials.email);\n        if (user && (credentials.password === \"demo123\" || credentials.password === \"password\")) {\n            return {\n                success: true,\n                user,\n                token: \"demo-token-\".concat(user.id, \"-\").concat(Date.now())\n            };\n        }\n        // If no match found, provide helpful demo credentials\n        return {\n            success: false,\n            error: \"Invalid credentials. Demo accounts available:\\n      \\n      • <EMAIL> / demo123\\n      • <EMAIL> / demo123  \\n      • <EMAIL> / demo123\\n      \\n      Or use any email with password: demo123\"\n        };\n    }\n    async googleSignIn() {\n        try {\n            if (this.isDemoMode) {\n                // Demo Google sign-in\n                await new Promise((resolve)=>setTimeout(resolve, 1500));\n                return {\n                    success: true,\n                    user: {\n                        id: \"google-demo-1\",\n                        email: \"<EMAIL>\",\n                        name: \"Dr. Kavya Sharma\",\n                        role: \"Radiologist\",\n                        hospital: \"Apollo Hospital\",\n                        department: \"Medical Imaging\"\n                    },\n                    token: \"google-demo-token-\".concat(Date.now())\n                };\n            }\n            // Try Firebase Google authentication\n            try {\n                const { signInWithPopup, GoogleAuthProvider } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_pnpm_firebase_11_10_0_node_modules_firebase_auth_dist_esm_ind-15c111\").then(__webpack_require__.bind(__webpack_require__, /*! firebase/auth */ \"(app-pages-browser)/./node_modules/.pnpm/firebase@11.10.0/node_modules/firebase/auth/dist/esm/index.esm.js\"));\n                const { auth } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_lib_firebase_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./firebase */ \"(app-pages-browser)/./lib/firebase.ts\"));\n                const provider = new GoogleAuthProvider();\n                provider.addScope(\"email\");\n                provider.addScope(\"profile\");\n                const result = await signInWithPopup(auth, provider);\n                return {\n                    success: true,\n                    user: {\n                        id: result.user.uid,\n                        email: result.user.email || \"\",\n                        name: result.user.displayName || \"User\",\n                        role: \"Doctor\"\n                    },\n                    token: await result.user.getIdToken()\n                };\n            } catch (firebaseError) {\n                console.warn(\"Firebase Google sign-in failed, using demo mode:\", firebaseError.message);\n                // Fallback to demo Google sign-in\n                return {\n                    success: true,\n                    user: {\n                        id: \"google-demo-fallback\",\n                        email: \"<EMAIL>\",\n                        name: \"Dr. Arjun Singh\",\n                        role: \"Oncologist\",\n                        hospital: \"Fortis Hospital\",\n                        department: \"Medical Oncology\"\n                    },\n                    token: \"google-demo-fallback-\".concat(Date.now())\n                };\n            }\n        } catch (error) {\n            console.error(\"Google sign-in error:\", error);\n            return {\n                success: false,\n                error: \"Google sign-in failed. Please try email/password login or contact support.\"\n            };\n        }\n    }\n    async logout() {\n        try {\n            if (!this.isDemoMode) {\n                const { signOut } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_pnpm_firebase_11_10_0_node_modules_firebase_auth_dist_esm_ind-15c111\").then(__webpack_require__.bind(__webpack_require__, /*! firebase/auth */ \"(app-pages-browser)/./node_modules/.pnpm/firebase@11.10.0/node_modules/firebase/auth/dist/esm/index.esm.js\"));\n                const { auth } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_lib_firebase_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./firebase */ \"(app-pages-browser)/./lib/firebase.ts\"));\n                await signOut(auth);\n            }\n            // Clear any stored tokens\n            if (true) {\n                localStorage.removeItem(\"quantnex-token\");\n                localStorage.removeItem(\"quantnex-user\");\n            }\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n            return {\n                success: true\n            } // Always succeed for logout\n            ;\n        }\n    }\n    async getCurrentUser() {\n        try {\n            if (this.isDemoMode) {\n                // Return demo user if token exists\n                const token =  true ? localStorage.getItem(\"quantnex-token\") : 0;\n                if (token && token.startsWith(\"demo-token\")) {\n                    return DEMO_USERS[0] // Return first demo user\n                    ;\n                }\n                return null;\n            }\n            const { auth } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_lib_firebase_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./firebase */ \"(app-pages-browser)/./lib/firebase.ts\"));\n            const { onAuthStateChanged } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_pnpm_firebase_11_10_0_node_modules_firebase_auth_dist_esm_ind-15c111\").then(__webpack_require__.bind(__webpack_require__, /*! firebase/auth */ \"(app-pages-browser)/./node_modules/.pnpm/firebase@11.10.0/node_modules/firebase/auth/dist/esm/index.esm.js\"));\n            return new Promise((resolve)=>{\n                const unsubscribe = onAuthStateChanged(auth, (user)=>{\n                    unsubscribe();\n                    if (user) {\n                        resolve({\n                            id: user.uid,\n                            email: user.email || \"\",\n                            name: user.displayName || \"User\",\n                            role: \"Doctor\"\n                        });\n                    } else {\n                        resolve(null);\n                    }\n                });\n            });\n        } catch (error) {\n            console.error(\"Get current user error:\", error);\n            return null;\n        }\n    }\n    // Patient management methods\n    async getPatients() {\n        // Simulate API call\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        return {\n            success: true,\n            data: [\n                {\n                    id: \"P001\",\n                    name: \"Rajesh Kumar Sharma\",\n                    age: 45,\n                    diagnosis: \"Glioblastoma\",\n                    status: \"active\"\n                },\n                {\n                    id: \"P002\",\n                    name: \"Sunita Devi Gupta\",\n                    age: 52,\n                    diagnosis: \"Breast Cancer\",\n                    status: \"stable\"\n                }\n            ]\n        };\n    }\n    async getDiagnosisData() {\n        await new Promise((resolve)=>setTimeout(resolve, 300));\n        return {\n            success: true,\n            data: {\n                totalScans: 1247,\n                pendingReviews: 23,\n                aiAccuracy: 94.2,\n                criticalCases: 8\n            }\n        };\n    }\n    async getHospitalData() {\n        await new Promise((resolve)=>setTimeout(resolve, 400));\n        return {\n            success: true,\n            data: [\n                {\n                    name: \"Tata Memorial Hospital\",\n                    location: \"Mumbai, Maharashtra\",\n                    patients: 1250,\n                    capacity: 1500\n                },\n                {\n                    name: \"AIIMS Delhi\",\n                    location: \"New Delhi\",\n                    patients: 2100,\n                    capacity: 2500\n                }\n            ]\n        };\n    }\n    // Check if service is in demo mode\n    isDemoModeActive() {\n        return this.isDemoMode;\n    }\n    // Get demo credentials for UI display\n    getDemoCredentials() {\n        return {\n            email: \"<EMAIL>\",\n            password: \"demo123\",\n            alternatives: [\n                \"<EMAIL> / demo123\",\n                \"<EMAIL> / demo123\"\n            ]\n        };\n    }\n    constructor(){\n        this.baseUrl = process.env.NEXT_PUBLIC_API_URL || \"https://api.quantnex.in\";\n        this.isDemoMode = false;\n        // Check if we're in demo mode (development or Firebase not configured)\n        this.isDemoMode =  true || 0;\n    }\n}\n// Export singleton instance\nconst indianBackendService = new IndianBackendService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9pbmRpYW4tYmFja2VuZC1zZXJ2aWNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsNkRBQTZEO0FBc0I3RCx5Q0FBeUM7QUFDekMsTUFBTUEsYUFBcUI7SUFDekI7UUFDRUMsSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxVQUFVO1FBQ1ZDLFlBQVk7SUFDZDtJQUNBO1FBQ0VMLElBQUk7UUFDSkMsT0FBTztRQUNQQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsVUFBVTtRQUNWQyxZQUFZO0lBQ2Q7SUFDQTtRQUNFTCxJQUFJO1FBQ0pDLE9BQU87UUFDUEMsTUFBTTtRQUNOQyxNQUFNO1FBQ05DLFVBQVU7UUFDVkMsWUFBWTtJQUNkO0NBQ0Q7QUFFRCxNQUFNQztJQVNKLE1BQU1DLE1BQU1DLFdBQTZCLEVBQXlCO1FBQ2hFLElBQUk7WUFDRiwrRUFBK0U7WUFDL0UsSUFBSSxJQUFJLENBQUNDLFVBQVUsRUFBRTtnQkFDbkIsT0FBTyxJQUFJLENBQUNDLFNBQVMsQ0FBQ0Y7WUFDeEI7WUFFQSxvQ0FBb0M7WUFDcEMsSUFBSTtnQkFDRixNQUFNLEVBQUVHLDBCQUEwQixFQUFFLEdBQUcsTUFBTSxvVUFBdUI7Z0JBQ3BFLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEdBQUcsTUFBTSwwTEFBb0I7Z0JBRTNDLE1BQU1DLGlCQUFpQixNQUFNRiwyQkFBMkJDLE1BQU1KLFlBQVlQLEtBQUssRUFBRU8sWUFBWU0sUUFBUTtnQkFFckcsT0FBTztvQkFDTEMsU0FBUztvQkFDVEMsTUFBTTt3QkFDSmhCLElBQUlhLGVBQWVHLElBQUksQ0FBQ0MsR0FBRzt3QkFDM0JoQixPQUFPWSxlQUFlRyxJQUFJLENBQUNmLEtBQUssSUFBSTt3QkFDcENDLE1BQU1XLGVBQWVHLElBQUksQ0FBQ0UsV0FBVyxJQUFJO3dCQUN6Q2YsTUFBTTtvQkFDUjtvQkFDQWdCLE9BQU8sTUFBTU4sZUFBZUcsSUFBSSxDQUFDSSxVQUFVO2dCQUM3QztZQUNGLEVBQUUsT0FBT0MsZUFBb0I7Z0JBQzNCQyxRQUFRQyxJQUFJLENBQUMsOERBQThERixjQUFjRyxPQUFPO2dCQUVoRyw0Q0FBNEM7Z0JBQzVDLE9BQU8sSUFBSSxDQUFDZCxTQUFTLENBQUNGO1lBQ3hCO1FBQ0YsRUFBRSxPQUFPaUIsT0FBWTtZQUNuQkgsUUFBUUcsS0FBSyxDQUFDLGdCQUFnQkE7WUFFOUIsOEJBQThCO1lBQzlCLE9BQU8sSUFBSSxDQUFDZixTQUFTLENBQUNGO1FBQ3hCO0lBQ0Y7SUFFQSxNQUFjRSxVQUFVRixXQUE2QixFQUF5QjtRQUM1RSxxQkFBcUI7UUFDckIsTUFBTSxJQUFJa0IsUUFBUSxDQUFDQyxVQUFZQyxXQUFXRCxTQUFTO1FBRW5ELHlCQUF5QjtRQUN6QixNQUFNWCxPQUFPakIsV0FBVzhCLElBQUksQ0FBQyxDQUFDQyxJQUFNQSxFQUFFN0IsS0FBSyxLQUFLTyxZQUFZUCxLQUFLO1FBRWpFLElBQUllLFFBQVNSLENBQUFBLFlBQVlNLFFBQVEsS0FBSyxhQUFhTixZQUFZTSxRQUFRLEtBQUssVUFBUyxHQUFJO1lBQ3ZGLE9BQU87Z0JBQ0xDLFNBQVM7Z0JBQ1RDO2dCQUNBRyxPQUFPLGNBQXlCWSxPQUFYZixLQUFLaEIsRUFBRSxFQUFDLEtBQWMsT0FBWCtCLEtBQUtDLEdBQUc7WUFDMUM7UUFDRjtRQUVBLHNEQUFzRDtRQUN0RCxPQUFPO1lBQ0xqQixTQUFTO1lBQ1RVLE9BQVE7UUFPVjtJQUNGO0lBRUEsTUFBTVEsZUFBc0M7UUFDMUMsSUFBSTtZQUNGLElBQUksSUFBSSxDQUFDeEIsVUFBVSxFQUFFO2dCQUNuQixzQkFBc0I7Z0JBQ3RCLE1BQU0sSUFBSWlCLFFBQVEsQ0FBQ0MsVUFBWUMsV0FBV0QsU0FBUztnQkFFbkQsT0FBTztvQkFDTFosU0FBUztvQkFDVEMsTUFBTTt3QkFDSmhCLElBQUk7d0JBQ0pDLE9BQU87d0JBQ1BDLE1BQU07d0JBQ05DLE1BQU07d0JBQ05DLFVBQVU7d0JBQ1ZDLFlBQVk7b0JBQ2Q7b0JBQ0FjLE9BQU8scUJBQWdDLE9BQVhZLEtBQUtDLEdBQUc7Z0JBQ3RDO1lBQ0Y7WUFFQSxxQ0FBcUM7WUFDckMsSUFBSTtnQkFDRixNQUFNLEVBQUVFLGVBQWUsRUFBRUMsa0JBQWtCLEVBQUUsR0FBRyxNQUFNLG9VQUF1QjtnQkFDN0UsTUFBTSxFQUFFdkIsSUFBSSxFQUFFLEdBQUcsTUFBTSwwTEFBb0I7Z0JBRTNDLE1BQU13QixXQUFXLElBQUlEO2dCQUNyQkMsU0FBU0MsUUFBUSxDQUFDO2dCQUNsQkQsU0FBU0MsUUFBUSxDQUFDO2dCQUVsQixNQUFNQyxTQUFTLE1BQU1KLGdCQUFnQnRCLE1BQU13QjtnQkFFM0MsT0FBTztvQkFDTHJCLFNBQVM7b0JBQ1RDLE1BQU07d0JBQ0poQixJQUFJc0MsT0FBT3RCLElBQUksQ0FBQ0MsR0FBRzt3QkFDbkJoQixPQUFPcUMsT0FBT3RCLElBQUksQ0FBQ2YsS0FBSyxJQUFJO3dCQUM1QkMsTUFBTW9DLE9BQU90QixJQUFJLENBQUNFLFdBQVcsSUFBSTt3QkFDakNmLE1BQU07b0JBQ1I7b0JBQ0FnQixPQUFPLE1BQU1tQixPQUFPdEIsSUFBSSxDQUFDSSxVQUFVO2dCQUNyQztZQUNGLEVBQUUsT0FBT0MsZUFBb0I7Z0JBQzNCQyxRQUFRQyxJQUFJLENBQUMsb0RBQW9ERixjQUFjRyxPQUFPO2dCQUV0RixrQ0FBa0M7Z0JBQ2xDLE9BQU87b0JBQ0xULFNBQVM7b0JBQ1RDLE1BQU07d0JBQ0poQixJQUFJO3dCQUNKQyxPQUFPO3dCQUNQQyxNQUFNO3dCQUNOQyxNQUFNO3dCQUNOQyxVQUFVO3dCQUNWQyxZQUFZO29CQUNkO29CQUNBYyxPQUFPLHdCQUFtQyxPQUFYWSxLQUFLQyxHQUFHO2dCQUN6QztZQUNGO1FBQ0YsRUFBRSxPQUFPUCxPQUFZO1lBQ25CSCxRQUFRRyxLQUFLLENBQUMseUJBQXlCQTtZQUV2QyxPQUFPO2dCQUNMVixTQUFTO2dCQUNUVSxPQUFPO1lBQ1Q7UUFDRjtJQUNGO0lBRUEsTUFBTWMsU0FBd0M7UUFDNUMsSUFBSTtZQUNGLElBQUksQ0FBQyxJQUFJLENBQUM5QixVQUFVLEVBQUU7Z0JBQ3BCLE1BQU0sRUFBRStCLE9BQU8sRUFBRSxHQUFHLE1BQU0sb1VBQXVCO2dCQUNqRCxNQUFNLEVBQUU1QixJQUFJLEVBQUUsR0FBRyxNQUFNLDBMQUFvQjtnQkFDM0MsTUFBTTRCLFFBQVE1QjtZQUNoQjtZQUVBLDBCQUEwQjtZQUMxQixJQUFJLElBQTZCLEVBQUU7Z0JBQ2pDNkIsYUFBYUMsVUFBVSxDQUFDO2dCQUN4QkQsYUFBYUMsVUFBVSxDQUFDO1lBQzFCO1lBRUEsT0FBTztnQkFBRTNCLFNBQVM7WUFBSztRQUN6QixFQUFFLE9BQU9VLE9BQU87WUFDZEgsUUFBUUcsS0FBSyxDQUFDLGlCQUFpQkE7WUFDL0IsT0FBTztnQkFBRVYsU0FBUztZQUFLLEVBQUUsNEJBQTRCOztRQUN2RDtJQUNGO0lBRUEsTUFBTTRCLGlCQUF1QztRQUMzQyxJQUFJO1lBQ0YsSUFBSSxJQUFJLENBQUNsQyxVQUFVLEVBQUU7Z0JBQ25CLG1DQUFtQztnQkFDbkMsTUFBTVUsUUFBUSxLQUE2QixHQUFHc0IsYUFBYUcsT0FBTyxDQUFDLG9CQUFvQixDQUFJO2dCQUMzRixJQUFJekIsU0FBU0EsTUFBTTBCLFVBQVUsQ0FBQyxlQUFlO29CQUMzQyxPQUFPOUMsVUFBVSxDQUFDLEVBQUUsQ0FBQyx5QkFBeUI7O2dCQUNoRDtnQkFDQSxPQUFPO1lBQ1Q7WUFFQSxNQUFNLEVBQUVhLElBQUksRUFBRSxHQUFHLE1BQU0sMExBQW9CO1lBQzNDLE1BQU0sRUFBRWtDLGtCQUFrQixFQUFFLEdBQUcsTUFBTSxvVUFBdUI7WUFFNUQsT0FBTyxJQUFJcEIsUUFBUSxDQUFDQztnQkFDbEIsTUFBTW9CLGNBQWNELG1CQUFtQmxDLE1BQU0sQ0FBQ0k7b0JBQzVDK0I7b0JBQ0EsSUFBSS9CLE1BQU07d0JBQ1JXLFFBQVE7NEJBQ04zQixJQUFJZ0IsS0FBS0MsR0FBRzs0QkFDWmhCLE9BQU9lLEtBQUtmLEtBQUssSUFBSTs0QkFDckJDLE1BQU1jLEtBQUtFLFdBQVcsSUFBSTs0QkFDMUJmLE1BQU07d0JBQ1I7b0JBQ0YsT0FBTzt3QkFDTHdCLFFBQVE7b0JBQ1Y7Z0JBQ0Y7WUFDRjtRQUNGLEVBQUUsT0FBT0YsT0FBTztZQUNkSCxRQUFRRyxLQUFLLENBQUMsMkJBQTJCQTtZQUN6QyxPQUFPO1FBQ1Q7SUFDRjtJQUVBLDZCQUE2QjtJQUM3QixNQUFNdUIsY0FBYztRQUNsQixvQkFBb0I7UUFDcEIsTUFBTSxJQUFJdEIsUUFBUSxDQUFDQyxVQUFZQyxXQUFXRCxTQUFTO1FBRW5ELE9BQU87WUFDTFosU0FBUztZQUNUa0MsTUFBTTtnQkFDSjtvQkFDRWpELElBQUk7b0JBQ0pFLE1BQU07b0JBQ05nRCxLQUFLO29CQUNMQyxXQUFXO29CQUNYQyxRQUFRO2dCQUNWO2dCQUNBO29CQUNFcEQsSUFBSTtvQkFDSkUsTUFBTTtvQkFDTmdELEtBQUs7b0JBQ0xDLFdBQVc7b0JBQ1hDLFFBQVE7Z0JBQ1Y7YUFDRDtRQUNIO0lBQ0Y7SUFFQSxNQUFNQyxtQkFBbUI7UUFDdkIsTUFBTSxJQUFJM0IsUUFBUSxDQUFDQyxVQUFZQyxXQUFXRCxTQUFTO1FBRW5ELE9BQU87WUFDTFosU0FBUztZQUNUa0MsTUFBTTtnQkFDSkssWUFBWTtnQkFDWkMsZ0JBQWdCO2dCQUNoQkMsWUFBWTtnQkFDWkMsZUFBZTtZQUNqQjtRQUNGO0lBQ0Y7SUFFQSxNQUFNQyxrQkFBa0I7UUFDdEIsTUFBTSxJQUFJaEMsUUFBUSxDQUFDQyxVQUFZQyxXQUFXRCxTQUFTO1FBRW5ELE9BQU87WUFDTFosU0FBUztZQUNUa0MsTUFBTTtnQkFDSjtvQkFDRS9DLE1BQU07b0JBQ055RCxVQUFVO29CQUNWQyxVQUFVO29CQUNWQyxVQUFVO2dCQUNaO2dCQUNBO29CQUNFM0QsTUFBTTtvQkFDTnlELFVBQVU7b0JBQ1ZDLFVBQVU7b0JBQ1ZDLFVBQVU7Z0JBQ1o7YUFDRDtRQUNIO0lBQ0Y7SUFFQSxtQ0FBbUM7SUFDbkNDLG1CQUE0QjtRQUMxQixPQUFPLElBQUksQ0FBQ3JELFVBQVU7SUFDeEI7SUFFQSxzQ0FBc0M7SUFDdENzRCxxQkFBcUI7UUFDbkIsT0FBTztZQUNMOUQsT0FBTztZQUNQYSxVQUFVO1lBQ1ZrRCxjQUFjO2dCQUFDO2dCQUFrQzthQUErQjtRQUNsRjtJQUNGO0lBN1FBQyxhQUFjO2FBSE5DLFVBQVVDLE9BQU9BLENBQUNDLEdBQUcsQ0FBQ0MsbUJBQW1CLElBQUk7YUFDN0M1RCxhQUFhO1FBR25CLHVFQUF1RTtRQUN2RSxJQUFJLENBQUNBLFVBQVUsR0FBRzBELEtBQXNDLElBQUksQ0FBeUM7SUFDdkc7QUEyUUY7QUFFQSw0QkFBNEI7QUFDckIsTUFBTUksdUJBQXVCLElBQUlqRSx1QkFBc0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWJoaWpcXERvd25sb2Fkc1xcUXVhbnRuZXguYWlcXGxpYlxcaW5kaWFuLWJhY2tlbmQtc2VydmljZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFbmhhbmNlZCBJbmRpYW4gQmFja2VuZCBTZXJ2aWNlIHdpdGggcHJvcGVyIGVycm9yIGhhbmRsaW5nXHJcbmludGVyZmFjZSBMb2dpbkNyZWRlbnRpYWxzIHtcclxuICBlbWFpbDogc3RyaW5nXHJcbiAgcGFzc3dvcmQ6IHN0cmluZ1xyXG59XHJcblxyXG5pbnRlcmZhY2UgVXNlciB7XHJcbiAgaWQ6IHN0cmluZ1xyXG4gIGVtYWlsOiBzdHJpbmdcclxuICBuYW1lOiBzdHJpbmdcclxuICByb2xlOiBzdHJpbmdcclxuICBob3NwaXRhbD86IHN0cmluZ1xyXG4gIGRlcGFydG1lbnQ/OiBzdHJpbmdcclxufVxyXG5cclxuaW50ZXJmYWNlIEF1dGhSZXNwb25zZSB7XHJcbiAgc3VjY2VzczogYm9vbGVhblxyXG4gIHVzZXI/OiBVc2VyXHJcbiAgdG9rZW4/OiBzdHJpbmdcclxuICBlcnJvcj86IHN0cmluZ1xyXG59XHJcblxyXG4vLyBEZW1vIHVzZXJzIGZvciBmYWxsYmFjayBhdXRoZW50aWNhdGlvblxyXG5jb25zdCBERU1PX1VTRVJTOiBVc2VyW10gPSBbXHJcbiAge1xyXG4gICAgaWQ6IFwiZGVtby0xXCIsXHJcbiAgICBlbWFpbDogXCJkci5wcml5YUBxdWFudG5leC5jb21cIixcclxuICAgIG5hbWU6IFwiRHIuIFByaXlhIFBhdGVsXCIsXHJcbiAgICByb2xlOiBcIk9uY29sb2dpc3RcIixcclxuICAgIGhvc3BpdGFsOiBcIlRhdGEgTWVtb3JpYWwgSG9zcGl0YWxcIixcclxuICAgIGRlcGFydG1lbnQ6IFwiTmV1cm8tT25jb2xvZ3lcIixcclxuICB9LFxyXG4gIHtcclxuICAgIGlkOiBcImRlbW8tMlwiLFxyXG4gICAgZW1haWw6IFwiZHIuYW1pdEBxdWFudG5leC5jb21cIixcclxuICAgIG5hbWU6IFwiRHIuIEFtaXQgR3VwdGFcIixcclxuICAgIHJvbGU6IFwiU3VyZ2VvblwiLFxyXG4gICAgaG9zcGl0YWw6IFwiQUlJTVMgRGVsaGlcIixcclxuICAgIGRlcGFydG1lbnQ6IFwiQ2FuY2VyIFN1cmdlcnlcIixcclxuICB9LFxyXG4gIHtcclxuICAgIGlkOiBcImRlbW8tM1wiLFxyXG4gICAgZW1haWw6IFwiYWRtaW5AcXVhbnRuZXguY29tXCIsXHJcbiAgICBuYW1lOiBcIlJhamVzaCBLdW1hclwiLFxyXG4gICAgcm9sZTogXCJBZG1pbmlzdHJhdG9yXCIsXHJcbiAgICBob3NwaXRhbDogXCJBcG9sbG8gSG9zcGl0YWxcIixcclxuICAgIGRlcGFydG1lbnQ6IFwiQWRtaW5pc3RyYXRpb25cIixcclxuICB9LFxyXG5dXHJcblxyXG5jbGFzcyBJbmRpYW5CYWNrZW5kU2VydmljZSB7XHJcbiAgcHJpdmF0ZSBiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTCB8fCBcImh0dHBzOi8vYXBpLnF1YW50bmV4LmluXCJcclxuICBwcml2YXRlIGlzRGVtb01vZGUgPSBmYWxzZVxyXG5cclxuICBjb25zdHJ1Y3RvcigpIHtcclxuICAgIC8vIENoZWNrIGlmIHdlJ3JlIGluIGRlbW8gbW9kZSAoZGV2ZWxvcG1lbnQgb3IgRmlyZWJhc2Ugbm90IGNvbmZpZ3VyZWQpXHJcbiAgICB0aGlzLmlzRGVtb01vZGUgPSBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gXCJkZXZlbG9wbWVudFwiIHx8ICFwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19GSVJFQkFTRV9BUElfS0VZXHJcbiAgfVxyXG5cclxuICBhc3luYyBsb2dpbihjcmVkZW50aWFsczogTG9naW5DcmVkZW50aWFscyk6IFByb21pc2U8QXV0aFJlc3BvbnNlPiB7XHJcbiAgICB0cnkge1xyXG4gICAgICAvLyBJZiBGaXJlYmFzZSBpcyBub3QgY29uZmlndXJlZCBvciB3ZSdyZSBpbiBkZW1vIG1vZGUsIHVzZSBkZW1vIGF1dGhlbnRpY2F0aW9uXHJcbiAgICAgIGlmICh0aGlzLmlzRGVtb01vZGUpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5kZW1vTG9naW4oY3JlZGVudGlhbHMpXHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFRyeSBGaXJlYmFzZSBhdXRoZW50aWNhdGlvbiBmaXJzdFxyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IHsgc2lnbkluV2l0aEVtYWlsQW5kUGFzc3dvcmQgfSA9IGF3YWl0IGltcG9ydChcImZpcmViYXNlL2F1dGhcIilcclxuICAgICAgICBjb25zdCB7IGF1dGggfSA9IGF3YWl0IGltcG9ydChcIi4vZmlyZWJhc2VcIilcclxuXHJcbiAgICAgICAgY29uc3QgdXNlckNyZWRlbnRpYWwgPSBhd2FpdCBzaWduSW5XaXRoRW1haWxBbmRQYXNzd29yZChhdXRoLCBjcmVkZW50aWFscy5lbWFpbCwgY3JlZGVudGlhbHMucGFzc3dvcmQpXHJcblxyXG4gICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxyXG4gICAgICAgICAgdXNlcjoge1xyXG4gICAgICAgICAgICBpZDogdXNlckNyZWRlbnRpYWwudXNlci51aWQsXHJcbiAgICAgICAgICAgIGVtYWlsOiB1c2VyQ3JlZGVudGlhbC51c2VyLmVtYWlsIHx8IFwiXCIsXHJcbiAgICAgICAgICAgIG5hbWU6IHVzZXJDcmVkZW50aWFsLnVzZXIuZGlzcGxheU5hbWUgfHwgXCJVc2VyXCIsXHJcbiAgICAgICAgICAgIHJvbGU6IFwiRG9jdG9yXCIsXHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgICAgdG9rZW46IGF3YWl0IHVzZXJDcmVkZW50aWFsLnVzZXIuZ2V0SWRUb2tlbigpLFxyXG4gICAgICAgIH1cclxuICAgICAgfSBjYXRjaCAoZmlyZWJhc2VFcnJvcjogYW55KSB7XHJcbiAgICAgICAgY29uc29sZS53YXJuKFwiRmlyZWJhc2UgYXV0aGVudGljYXRpb24gZmFpbGVkLCBmYWxsaW5nIGJhY2sgdG8gZGVtbyBtb2RlOlwiLCBmaXJlYmFzZUVycm9yLm1lc3NhZ2UpXHJcblxyXG4gICAgICAgIC8vIElmIEZpcmViYXNlIGZhaWxzLCBmYWxsIGJhY2sgdG8gZGVtbyBtb2RlXHJcbiAgICAgICAgcmV0dXJuIHRoaXMuZGVtb0xvZ2luKGNyZWRlbnRpYWxzKVxyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJMb2dpbiBlcnJvcjpcIiwgZXJyb3IpXHJcblxyXG4gICAgICAvLyBGaW5hbCBmYWxsYmFjayB0byBkZW1vIG1vZGVcclxuICAgICAgcmV0dXJuIHRoaXMuZGVtb0xvZ2luKGNyZWRlbnRpYWxzKVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBhc3luYyBkZW1vTG9naW4oY3JlZGVudGlhbHM6IExvZ2luQ3JlZGVudGlhbHMpOiBQcm9taXNlPEF1dGhSZXNwb25zZT4ge1xyXG4gICAgLy8gU2ltdWxhdGUgQVBJIGRlbGF5XHJcbiAgICBhd2FpdCBuZXcgUHJvbWlzZSgocmVzb2x2ZSkgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDAwKSlcclxuXHJcbiAgICAvLyBDaGVjayBkZW1vIGNyZWRlbnRpYWxzXHJcbiAgICBjb25zdCB1c2VyID0gREVNT19VU0VSUy5maW5kKCh1KSA9PiB1LmVtYWlsID09PSBjcmVkZW50aWFscy5lbWFpbClcclxuXHJcbiAgICBpZiAodXNlciAmJiAoY3JlZGVudGlhbHMucGFzc3dvcmQgPT09IFwiZGVtbzEyM1wiIHx8IGNyZWRlbnRpYWxzLnBhc3N3b3JkID09PSBcInBhc3N3b3JkXCIpKSB7XHJcbiAgICAgIHJldHVybiB7XHJcbiAgICAgICAgc3VjY2VzczogdHJ1ZSxcclxuICAgICAgICB1c2VyLFxyXG4gICAgICAgIHRva2VuOiBgZGVtby10b2tlbi0ke3VzZXIuaWR9LSR7RGF0ZS5ub3coKX1gLFxyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLy8gSWYgbm8gbWF0Y2ggZm91bmQsIHByb3ZpZGUgaGVscGZ1bCBkZW1vIGNyZWRlbnRpYWxzXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBzdWNjZXNzOiBmYWxzZSxcclxuICAgICAgZXJyb3I6IGBJbnZhbGlkIGNyZWRlbnRpYWxzLiBEZW1vIGFjY291bnRzIGF2YWlsYWJsZTpcclxuICAgICAgXHJcbiAgICAgIOKAoiBkci5wcml5YUBxdWFudG5leC5jb20gLyBkZW1vMTIzXHJcbiAgICAgIOKAoiBkci5hbWl0QHF1YW50bmV4LmNvbSAvIGRlbW8xMjMgIFxyXG4gICAgICDigKIgYWRtaW5AcXVhbnRuZXguY29tIC8gZGVtbzEyM1xyXG4gICAgICBcclxuICAgICAgT3IgdXNlIGFueSBlbWFpbCB3aXRoIHBhc3N3b3JkOiBkZW1vMTIzYCxcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGFzeW5jIGdvb2dsZVNpZ25JbigpOiBQcm9taXNlPEF1dGhSZXNwb25zZT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgaWYgKHRoaXMuaXNEZW1vTW9kZSkge1xyXG4gICAgICAgIC8vIERlbW8gR29vZ2xlIHNpZ24taW5cclxuICAgICAgICBhd2FpdCBuZXcgUHJvbWlzZSgocmVzb2x2ZSkgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxNTAwKSlcclxuXHJcbiAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXHJcbiAgICAgICAgICB1c2VyOiB7XHJcbiAgICAgICAgICAgIGlkOiBcImdvb2dsZS1kZW1vLTFcIixcclxuICAgICAgICAgICAgZW1haWw6IFwiZGVtby51c2VyQGdtYWlsLmNvbVwiLFxyXG4gICAgICAgICAgICBuYW1lOiBcIkRyLiBLYXZ5YSBTaGFybWFcIixcclxuICAgICAgICAgICAgcm9sZTogXCJSYWRpb2xvZ2lzdFwiLFxyXG4gICAgICAgICAgICBob3NwaXRhbDogXCJBcG9sbG8gSG9zcGl0YWxcIixcclxuICAgICAgICAgICAgZGVwYXJ0bWVudDogXCJNZWRpY2FsIEltYWdpbmdcIixcclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgICB0b2tlbjogYGdvb2dsZS1kZW1vLXRva2VuLSR7RGF0ZS5ub3coKX1gLFxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gVHJ5IEZpcmViYXNlIEdvb2dsZSBhdXRoZW50aWNhdGlvblxyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IHsgc2lnbkluV2l0aFBvcHVwLCBHb29nbGVBdXRoUHJvdmlkZXIgfSA9IGF3YWl0IGltcG9ydChcImZpcmViYXNlL2F1dGhcIilcclxuICAgICAgICBjb25zdCB7IGF1dGggfSA9IGF3YWl0IGltcG9ydChcIi4vZmlyZWJhc2VcIilcclxuXHJcbiAgICAgICAgY29uc3QgcHJvdmlkZXIgPSBuZXcgR29vZ2xlQXV0aFByb3ZpZGVyKClcclxuICAgICAgICBwcm92aWRlci5hZGRTY29wZShcImVtYWlsXCIpXHJcbiAgICAgICAgcHJvdmlkZXIuYWRkU2NvcGUoXCJwcm9maWxlXCIpXHJcblxyXG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHNpZ25JbldpdGhQb3B1cChhdXRoLCBwcm92aWRlcilcclxuXHJcbiAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXHJcbiAgICAgICAgICB1c2VyOiB7XHJcbiAgICAgICAgICAgIGlkOiByZXN1bHQudXNlci51aWQsXHJcbiAgICAgICAgICAgIGVtYWlsOiByZXN1bHQudXNlci5lbWFpbCB8fCBcIlwiLFxyXG4gICAgICAgICAgICBuYW1lOiByZXN1bHQudXNlci5kaXNwbGF5TmFtZSB8fCBcIlVzZXJcIixcclxuICAgICAgICAgICAgcm9sZTogXCJEb2N0b3JcIixcclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgICB0b2tlbjogYXdhaXQgcmVzdWx0LnVzZXIuZ2V0SWRUb2tlbigpLFxyXG4gICAgICAgIH1cclxuICAgICAgfSBjYXRjaCAoZmlyZWJhc2VFcnJvcjogYW55KSB7XHJcbiAgICAgICAgY29uc29sZS53YXJuKFwiRmlyZWJhc2UgR29vZ2xlIHNpZ24taW4gZmFpbGVkLCB1c2luZyBkZW1vIG1vZGU6XCIsIGZpcmViYXNlRXJyb3IubWVzc2FnZSlcclxuXHJcbiAgICAgICAgLy8gRmFsbGJhY2sgdG8gZGVtbyBHb29nbGUgc2lnbi1pblxyXG4gICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxyXG4gICAgICAgICAgdXNlcjoge1xyXG4gICAgICAgICAgICBpZDogXCJnb29nbGUtZGVtby1mYWxsYmFja1wiLFxyXG4gICAgICAgICAgICBlbWFpbDogXCJkZW1vLmdvb2dsZUBxdWFudG5leC5jb21cIixcclxuICAgICAgICAgICAgbmFtZTogXCJEci4gQXJqdW4gU2luZ2hcIixcclxuICAgICAgICAgICAgcm9sZTogXCJPbmNvbG9naXN0XCIsXHJcbiAgICAgICAgICAgIGhvc3BpdGFsOiBcIkZvcnRpcyBIb3NwaXRhbFwiLFxyXG4gICAgICAgICAgICBkZXBhcnRtZW50OiBcIk1lZGljYWwgT25jb2xvZ3lcIixcclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgICB0b2tlbjogYGdvb2dsZS1kZW1vLWZhbGxiYWNrLSR7RGF0ZS5ub3coKX1gLFxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiR29vZ2xlIHNpZ24taW4gZXJyb3I6XCIsIGVycm9yKVxyXG5cclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcclxuICAgICAgICBlcnJvcjogXCJHb29nbGUgc2lnbi1pbiBmYWlsZWQuIFBsZWFzZSB0cnkgZW1haWwvcGFzc3dvcmQgbG9naW4gb3IgY29udGFjdCBzdXBwb3J0LlwiLFxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBhc3luYyBsb2dvdXQoKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW4gfT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgaWYgKCF0aGlzLmlzRGVtb01vZGUpIHtcclxuICAgICAgICBjb25zdCB7IHNpZ25PdXQgfSA9IGF3YWl0IGltcG9ydChcImZpcmViYXNlL2F1dGhcIilcclxuICAgICAgICBjb25zdCB7IGF1dGggfSA9IGF3YWl0IGltcG9ydChcIi4vZmlyZWJhc2VcIilcclxuICAgICAgICBhd2FpdCBzaWduT3V0KGF1dGgpXHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIENsZWFyIGFueSBzdG9yZWQgdG9rZW5zXHJcbiAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSBcInVuZGVmaW5lZFwiKSB7XHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oXCJxdWFudG5leC10b2tlblwiKVxyXG4gICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKFwicXVhbnRuZXgtdXNlclwiKVxyXG4gICAgICB9XHJcblxyXG4gICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJMb2dvdXQgZXJyb3I6XCIsIGVycm9yKVxyXG4gICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlIH0gLy8gQWx3YXlzIHN1Y2NlZWQgZm9yIGxvZ291dFxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgZ2V0Q3VycmVudFVzZXIoKTogUHJvbWlzZTxVc2VyIHwgbnVsbD4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgaWYgKHRoaXMuaXNEZW1vTW9kZSkge1xyXG4gICAgICAgIC8vIFJldHVybiBkZW1vIHVzZXIgaWYgdG9rZW4gZXhpc3RzXHJcbiAgICAgICAgY29uc3QgdG9rZW4gPSB0eXBlb2Ygd2luZG93ICE9PSBcInVuZGVmaW5lZFwiID8gbG9jYWxTdG9yYWdlLmdldEl0ZW0oXCJxdWFudG5leC10b2tlblwiKSA6IG51bGxcclxuICAgICAgICBpZiAodG9rZW4gJiYgdG9rZW4uc3RhcnRzV2l0aChcImRlbW8tdG9rZW5cIikpIHtcclxuICAgICAgICAgIHJldHVybiBERU1PX1VTRVJTWzBdIC8vIFJldHVybiBmaXJzdCBkZW1vIHVzZXJcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIG51bGxcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgeyBhdXRoIH0gPSBhd2FpdCBpbXBvcnQoXCIuL2ZpcmViYXNlXCIpXHJcbiAgICAgIGNvbnN0IHsgb25BdXRoU3RhdGVDaGFuZ2VkIH0gPSBhd2FpdCBpbXBvcnQoXCJmaXJlYmFzZS9hdXRoXCIpXHJcblxyXG4gICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHtcclxuICAgICAgICBjb25zdCB1bnN1YnNjcmliZSA9IG9uQXV0aFN0YXRlQ2hhbmdlZChhdXRoLCAodXNlcikgPT4ge1xyXG4gICAgICAgICAgdW5zdWJzY3JpYmUoKVxyXG4gICAgICAgICAgaWYgKHVzZXIpIHtcclxuICAgICAgICAgICAgcmVzb2x2ZSh7XHJcbiAgICAgICAgICAgICAgaWQ6IHVzZXIudWlkLFxyXG4gICAgICAgICAgICAgIGVtYWlsOiB1c2VyLmVtYWlsIHx8IFwiXCIsXHJcbiAgICAgICAgICAgICAgbmFtZTogdXNlci5kaXNwbGF5TmFtZSB8fCBcIlVzZXJcIixcclxuICAgICAgICAgICAgICByb2xlOiBcIkRvY3RvclwiLFxyXG4gICAgICAgICAgICB9KVxyXG4gICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgcmVzb2x2ZShudWxsKVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0pXHJcbiAgICAgIH0pXHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiR2V0IGN1cnJlbnQgdXNlciBlcnJvcjpcIiwgZXJyb3IpXHJcbiAgICAgIHJldHVybiBudWxsXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvLyBQYXRpZW50IG1hbmFnZW1lbnQgbWV0aG9kc1xyXG4gIGFzeW5jIGdldFBhdGllbnRzKCkge1xyXG4gICAgLy8gU2ltdWxhdGUgQVBJIGNhbGxcclxuICAgIGF3YWl0IG5ldyBQcm9taXNlKChyZXNvbHZlKSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDUwMCkpXHJcblxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgc3VjY2VzczogdHJ1ZSxcclxuICAgICAgZGF0YTogW1xyXG4gICAgICAgIHtcclxuICAgICAgICAgIGlkOiBcIlAwMDFcIixcclxuICAgICAgICAgIG5hbWU6IFwiUmFqZXNoIEt1bWFyIFNoYXJtYVwiLFxyXG4gICAgICAgICAgYWdlOiA0NSxcclxuICAgICAgICAgIGRpYWdub3NpczogXCJHbGlvYmxhc3RvbWFcIixcclxuICAgICAgICAgIHN0YXR1czogXCJhY3RpdmVcIixcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgIGlkOiBcIlAwMDJcIixcclxuICAgICAgICAgIG5hbWU6IFwiU3VuaXRhIERldmkgR3VwdGFcIixcclxuICAgICAgICAgIGFnZTogNTIsXHJcbiAgICAgICAgICBkaWFnbm9zaXM6IFwiQnJlYXN0IENhbmNlclwiLFxyXG4gICAgICAgICAgc3RhdHVzOiBcInN0YWJsZVwiLFxyXG4gICAgICAgIH0sXHJcbiAgICAgIF0sXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBhc3luYyBnZXREaWFnbm9zaXNEYXRhKCkge1xyXG4gICAgYXdhaXQgbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMzAwKSlcclxuXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBzdWNjZXNzOiB0cnVlLFxyXG4gICAgICBkYXRhOiB7XHJcbiAgICAgICAgdG90YWxTY2FuczogMTI0NyxcclxuICAgICAgICBwZW5kaW5nUmV2aWV3czogMjMsXHJcbiAgICAgICAgYWlBY2N1cmFjeTogOTQuMixcclxuICAgICAgICBjcml0aWNhbENhc2VzOiA4LFxyXG4gICAgICB9LFxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgZ2V0SG9zcGl0YWxEYXRhKCkge1xyXG4gICAgYXdhaXQgbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgNDAwKSlcclxuXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBzdWNjZXNzOiB0cnVlLFxyXG4gICAgICBkYXRhOiBbXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgbmFtZTogXCJUYXRhIE1lbW9yaWFsIEhvc3BpdGFsXCIsXHJcbiAgICAgICAgICBsb2NhdGlvbjogXCJNdW1iYWksIE1haGFyYXNodHJhXCIsXHJcbiAgICAgICAgICBwYXRpZW50czogMTI1MCxcclxuICAgICAgICAgIGNhcGFjaXR5OiAxNTAwLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgbmFtZTogXCJBSUlNUyBEZWxoaVwiLFxyXG4gICAgICAgICAgbG9jYXRpb246IFwiTmV3IERlbGhpXCIsXHJcbiAgICAgICAgICBwYXRpZW50czogMjEwMCxcclxuICAgICAgICAgIGNhcGFjaXR5OiAyNTAwLFxyXG4gICAgICAgIH0sXHJcbiAgICAgIF0sXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvLyBDaGVjayBpZiBzZXJ2aWNlIGlzIGluIGRlbW8gbW9kZVxyXG4gIGlzRGVtb01vZGVBY3RpdmUoKTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gdGhpcy5pc0RlbW9Nb2RlXHJcbiAgfVxyXG5cclxuICAvLyBHZXQgZGVtbyBjcmVkZW50aWFscyBmb3IgVUkgZGlzcGxheVxyXG4gIGdldERlbW9DcmVkZW50aWFscygpIHtcclxuICAgIHJldHVybiB7XHJcbiAgICAgIGVtYWlsOiBcImRyLnByaXlhQHF1YW50bmV4LmNvbVwiLFxyXG4gICAgICBwYXNzd29yZDogXCJkZW1vMTIzXCIsXHJcbiAgICAgIGFsdGVybmF0aXZlczogW1wiZHIuYW1pdEBxdWFudG5leC5jb20gLyBkZW1vMTIzXCIsIFwiYWRtaW5AcXVhbnRuZXguY29tIC8gZGVtbzEyM1wiXSxcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8vIEV4cG9ydCBzaW5nbGV0b24gaW5zdGFuY2VcclxuZXhwb3J0IGNvbnN0IGluZGlhbkJhY2tlbmRTZXJ2aWNlID0gbmV3IEluZGlhbkJhY2tlbmRTZXJ2aWNlKClcclxuXHJcbi8vIEV4cG9ydCB0eXBlc1xyXG5leHBvcnQgdHlwZSB7IExvZ2luQ3JlZGVudGlhbHMsIFVzZXIsIEF1dGhSZXNwb25zZSB9XHJcbiJdLCJuYW1lcyI6WyJERU1PX1VTRVJTIiwiaWQiLCJlbWFpbCIsIm5hbWUiLCJyb2xlIiwiaG9zcGl0YWwiLCJkZXBhcnRtZW50IiwiSW5kaWFuQmFja2VuZFNlcnZpY2UiLCJsb2dpbiIsImNyZWRlbnRpYWxzIiwiaXNEZW1vTW9kZSIsImRlbW9Mb2dpbiIsInNpZ25JbldpdGhFbWFpbEFuZFBhc3N3b3JkIiwiYXV0aCIsInVzZXJDcmVkZW50aWFsIiwicGFzc3dvcmQiLCJzdWNjZXNzIiwidXNlciIsInVpZCIsImRpc3BsYXlOYW1lIiwidG9rZW4iLCJnZXRJZFRva2VuIiwiZmlyZWJhc2VFcnJvciIsImNvbnNvbGUiLCJ3YXJuIiwibWVzc2FnZSIsImVycm9yIiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwiZmluZCIsInUiLCJEYXRlIiwibm93IiwiZ29vZ2xlU2lnbkluIiwic2lnbkluV2l0aFBvcHVwIiwiR29vZ2xlQXV0aFByb3ZpZGVyIiwicHJvdmlkZXIiLCJhZGRTY29wZSIsInJlc3VsdCIsImxvZ291dCIsInNpZ25PdXQiLCJsb2NhbFN0b3JhZ2UiLCJyZW1vdmVJdGVtIiwiZ2V0Q3VycmVudFVzZXIiLCJnZXRJdGVtIiwic3RhcnRzV2l0aCIsIm9uQXV0aFN0YXRlQ2hhbmdlZCIsInVuc3Vic2NyaWJlIiwiZ2V0UGF0aWVudHMiLCJkYXRhIiwiYWdlIiwiZGlhZ25vc2lzIiwic3RhdHVzIiwiZ2V0RGlhZ25vc2lzRGF0YSIsInRvdGFsU2NhbnMiLCJwZW5kaW5nUmV2aWV3cyIsImFpQWNjdXJhY3kiLCJjcml0aWNhbENhc2VzIiwiZ2V0SG9zcGl0YWxEYXRhIiwibG9jYXRpb24iLCJwYXRpZW50cyIsImNhcGFjaXR5IiwiaXNEZW1vTW9kZUFjdGl2ZSIsImdldERlbW9DcmVkZW50aWFscyIsImFsdGVybmF0aXZlcyIsImNvbnN0cnVjdG9yIiwiYmFzZVVybCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BUElfVVJMIiwiTkVYVF9QVUJMSUNfRklSRUJBU0VfQVBJX0tFWSIsImluZGlhbkJhY2tlbmRTZXJ2aWNlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/indian-backend-service.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/api/navigation.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/api/navigation.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjIuNF9AYmFiZWwrY29yZUA3LjJfYWY0NjJkYTBiNDRhNjRlZTRhYzdjNjJjYjZmMjI2ZDgvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9hcGkvbmF2aWdhdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0Q7O0FBRWhEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFiaGlqXFxEb3dubG9hZHNcXFF1YW50bmV4LmFpXFxub2RlX21vZHVsZXNcXC5wbnBtXFxuZXh0QDE1LjIuNF9AYmFiZWwrY29yZUA3LjJfYWY0NjJkYTBiNDRhNjRlZTRhYzdjNjJjYjZmMjI2ZDhcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcYXBpXFxuYXZpZ2F0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4uL2NsaWVudC9jb21wb25lbnRzL25hdmlnYXRpb24nO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1uYXZpZ2F0aW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/api/navigation.js\n"));

/***/ })

});