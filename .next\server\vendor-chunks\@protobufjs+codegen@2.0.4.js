"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@protobufjs+codegen@2.0.4";
exports.ids = ["vendor-chunks/@protobufjs+codegen@2.0.4"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@protobufjs+codegen@2.0.4/node_modules/@protobufjs/codegen/index.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@protobufjs+codegen@2.0.4/node_modules/@protobufjs/codegen/index.js ***!
  \************************************************************************************************/
/***/ ((module) => {

eval("\r\nmodule.exports = codegen;\r\n\r\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @param {string[]} functionParams Function parameter names\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n */\r\nfunction codegen(functionParams, functionName) {\r\n\r\n    /* istanbul ignore if */\r\n    if (typeof functionParams === \"string\") {\r\n        functionName = functionParams;\r\n        functionParams = undefined;\r\n    }\r\n\r\n    var body = [];\r\n\r\n    /**\r\n     * Appends code to the function's body or finishes generation.\r\n     * @typedef Codegen\r\n     * @type {function}\r\n     * @param {string|Object.<string,*>} [formatStringOrScope] Format string or, to finish the function, an object of additional scope variables, if any\r\n     * @param {...*} [formatParams] Format parameters\r\n     * @returns {Codegen|Function} Itself or the generated function if finished\r\n     * @throws {Error} If format parameter counts do not match\r\n     */\r\n\r\n    function Codegen(formatStringOrScope) {\r\n        // note that explicit array handling below makes this ~50% faster\r\n\r\n        // finish the function\r\n        if (typeof formatStringOrScope !== \"string\") {\r\n            var source = toString();\r\n            if (codegen.verbose)\r\n                console.log(\"codegen: \" + source); // eslint-disable-line no-console\r\n            source = \"return \" + source;\r\n            if (formatStringOrScope) {\r\n                var scopeKeys   = Object.keys(formatStringOrScope),\r\n                    scopeParams = new Array(scopeKeys.length + 1),\r\n                    scopeValues = new Array(scopeKeys.length),\r\n                    scopeOffset = 0;\r\n                while (scopeOffset < scopeKeys.length) {\r\n                    scopeParams[scopeOffset] = scopeKeys[scopeOffset];\r\n                    scopeValues[scopeOffset] = formatStringOrScope[scopeKeys[scopeOffset++]];\r\n                }\r\n                scopeParams[scopeOffset] = source;\r\n                return Function.apply(null, scopeParams).apply(null, scopeValues); // eslint-disable-line no-new-func\r\n            }\r\n            return Function(source)(); // eslint-disable-line no-new-func\r\n        }\r\n\r\n        // otherwise append to body\r\n        var formatParams = new Array(arguments.length - 1),\r\n            formatOffset = 0;\r\n        while (formatOffset < formatParams.length)\r\n            formatParams[formatOffset] = arguments[++formatOffset];\r\n        formatOffset = 0;\r\n        formatStringOrScope = formatStringOrScope.replace(/%([%dfijs])/g, function replace($0, $1) {\r\n            var value = formatParams[formatOffset++];\r\n            switch ($1) {\r\n                case \"d\": case \"f\": return String(Number(value));\r\n                case \"i\": return String(Math.floor(value));\r\n                case \"j\": return JSON.stringify(value);\r\n                case \"s\": return String(value);\r\n            }\r\n            return \"%\";\r\n        });\r\n        if (formatOffset !== formatParams.length)\r\n            throw Error(\"parameter count mismatch\");\r\n        body.push(formatStringOrScope);\r\n        return Codegen;\r\n    }\r\n\r\n    function toString(functionNameOverride) {\r\n        return \"function \" + (functionNameOverride || functionName || \"\") + \"(\" + (functionParams && functionParams.join(\",\") || \"\") + \"){\\n  \" + body.join(\"\\n  \") + \"\\n}\";\r\n    }\r\n\r\n    Codegen.toString = toString;\r\n    return Codegen;\r\n}\r\n\r\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @function codegen\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n * @variation 2\r\n */\r\n\r\n/**\r\n * When set to `true`, codegen will log generated code to console. Useful for debugging.\r\n * @name util.codegen.verbose\r\n * @type {boolean}\r\n */\r\ncodegen.verbose = false;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@protobufjs+codegen@2.0.4/node_modules/@protobufjs/codegen/index.js\n");

/***/ })

};
;