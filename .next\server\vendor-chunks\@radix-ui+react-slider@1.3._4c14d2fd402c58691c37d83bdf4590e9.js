"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-slider@1.3._4c14d2fd402c58691c37d83bdf4590e9";
exports.ids = ["vendor-chunks/@radix-ui+react-slider@1.3._4c14d2fd402c58691c37d83bdf4590e9"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-slider@1.3._4c14d2fd402c58691c37d83bdf4590e9/node_modules/@radix-ui/react-slider/dist/index.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-slider@1.3._4c14d2fd402c58691c37d83bdf4590e9/node_modules/@radix-ui/react-slider/dist/index.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Range: () => (/* binding */ Range),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Slider: () => (/* binding */ Slider),\n/* harmony export */   SliderRange: () => (/* binding */ SliderRange),\n/* harmony export */   SliderThumb: () => (/* binding */ SliderThumb),\n/* harmony export */   SliderTrack: () => (/* binding */ SliderTrack),\n/* harmony export */   Thumb: () => (/* binding */ Thumb),\n/* harmony export */   Track: () => (/* binding */ Track),\n/* harmony export */   createSliderScope: () => (/* binding */ createSliderScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/number */ \"(ssr)/./node_modules/.pnpm/@radix-ui+number@1.1.1/node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_a1d2853b5188b26995ccf8fe08b4ac18/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_b6776874cf08148c72027fb114cc7f90/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_a1c3931d508de372008f1d96de06e51b/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1_1117bdf9bd5118f735c90c37f510b6ee/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-previou_4e3d26ea98d9d37282b8f33289a63ee2/node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-size@1._e661ade751b88464b6c0f932b6b94565/node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_f1559ddf590287c33aa65e2eac7c63be/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@_651f60b83f43ee655942b219a5b6e6e2/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Range,Root,Slider,SliderRange,SliderThumb,SliderTrack,Thumb,Track,createSliderScope auto */ // src/slider.tsx\n\n\n\n\n\n\n\n\n\n\n\n\nvar PAGE_KEYS = [\n    \"PageUp\",\n    \"PageDown\"\n];\nvar ARROW_KEYS = [\n    \"ArrowUp\",\n    \"ArrowDown\",\n    \"ArrowLeft\",\n    \"ArrowRight\"\n];\nvar BACK_KEYS = {\n    \"from-left\": [\n        \"Home\",\n        \"PageDown\",\n        \"ArrowDown\",\n        \"ArrowLeft\"\n    ],\n    \"from-right\": [\n        \"Home\",\n        \"PageDown\",\n        \"ArrowDown\",\n        \"ArrowRight\"\n    ],\n    \"from-bottom\": [\n        \"Home\",\n        \"PageDown\",\n        \"ArrowDown\",\n        \"ArrowLeft\"\n    ],\n    \"from-top\": [\n        \"Home\",\n        \"PageDown\",\n        \"ArrowUp\",\n        \"ArrowLeft\"\n    ]\n};\nvar SLIDER_NAME = \"Slider\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__.createCollection)(SLIDER_NAME);\nvar [createSliderContext, createSliderScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__.createContextScope)(SLIDER_NAME, [\n    createCollectionScope\n]);\nvar [SliderProvider, useSliderContext] = createSliderContext(SLIDER_NAME);\nvar Slider = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { name, min = 0, max = 100, step = 1, orientation = \"horizontal\", disabled = false, minStepsBetweenThumbs = 0, defaultValue = [\n        min\n    ], value, onValueChange = ()=>{}, onValueCommit = ()=>{}, inverted = false, form, ...sliderProps } = props;\n    const thumbRefs = react__WEBPACK_IMPORTED_MODULE_0__.useRef(/* @__PURE__ */ new Set());\n    const valueIndexToChangeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const isHorizontal = orientation === \"horizontal\";\n    const SliderOrientation = isHorizontal ? SliderHorizontal : SliderVertical;\n    const [values = [], setValues] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__.useControllableState)({\n        prop: value,\n        defaultProp: defaultValue,\n        onChange: {\n            \"Slider.useControllableState\": (value2)=>{\n                const thumbs = [\n                    ...thumbRefs.current\n                ];\n                thumbs[valueIndexToChangeRef.current]?.focus();\n                onValueChange(value2);\n            }\n        }[\"Slider.useControllableState\"]\n    });\n    const valuesBeforeSlideStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(values);\n    function handleSlideStart(value2) {\n        const closestIndex = getClosestValueIndex(values, value2);\n        updateValues(value2, closestIndex);\n    }\n    function handleSlideMove(value2) {\n        updateValues(value2, valueIndexToChangeRef.current);\n    }\n    function handleSlideEnd() {\n        const prevValue = valuesBeforeSlideStartRef.current[valueIndexToChangeRef.current];\n        const nextValue = values[valueIndexToChangeRef.current];\n        const hasChanged = nextValue !== prevValue;\n        if (hasChanged) onValueCommit(values);\n    }\n    function updateValues(value2, atIndex, { commit } = {\n        commit: false\n    }) {\n        const decimalCount = getDecimalCount(step);\n        const snapToStep = roundValue(Math.round((value2 - min) / step) * step + min, decimalCount);\n        const nextValue = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_5__.clamp)(snapToStep, [\n            min,\n            max\n        ]);\n        setValues((prevValues = [])=>{\n            const nextValues = getNextSortedValues(prevValues, nextValue, atIndex);\n            if (hasMinStepsBetweenValues(nextValues, minStepsBetweenThumbs * step)) {\n                valueIndexToChangeRef.current = nextValues.indexOf(nextValue);\n                const hasChanged = String(nextValues) !== String(prevValues);\n                if (hasChanged && commit) onValueCommit(nextValues);\n                return hasChanged ? nextValues : prevValues;\n            } else {\n                return prevValues;\n            }\n        });\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderProvider, {\n        scope: props.__scopeSlider,\n        name,\n        disabled,\n        min,\n        max,\n        valueIndexToChangeRef,\n        thumbs: thumbRefs.current,\n        values,\n        orientation,\n        form,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n            scope: props.__scopeSlider,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n                scope: props.__scopeSlider,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderOrientation, {\n                    \"aria-disabled\": disabled,\n                    \"data-disabled\": disabled ? \"\" : void 0,\n                    ...sliderProps,\n                    ref: forwardedRef,\n                    onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(sliderProps.onPointerDown, ()=>{\n                        if (!disabled) valuesBeforeSlideStartRef.current = values;\n                    }),\n                    min,\n                    max,\n                    inverted,\n                    onSlideStart: disabled ? void 0 : handleSlideStart,\n                    onSlideMove: disabled ? void 0 : handleSlideMove,\n                    onSlideEnd: disabled ? void 0 : handleSlideEnd,\n                    onHomeKeyDown: ()=>!disabled && updateValues(min, 0, {\n                            commit: true\n                        }),\n                    onEndKeyDown: ()=>!disabled && updateValues(max, values.length - 1, {\n                            commit: true\n                        }),\n                    onStepKeyDown: ({ event, direction: stepDirection })=>{\n                        if (!disabled) {\n                            const isPageKey = PAGE_KEYS.includes(event.key);\n                            const isSkipKey = isPageKey || event.shiftKey && ARROW_KEYS.includes(event.key);\n                            const multiplier = isSkipKey ? 10 : 1;\n                            const atIndex = valueIndexToChangeRef.current;\n                            const value2 = values[atIndex];\n                            const stepInDirection = step * multiplier * stepDirection;\n                            updateValues(value2 + stepInDirection, atIndex, {\n                                commit: true\n                            });\n                        }\n                    }\n                })\n            })\n        })\n    });\n});\nSlider.displayName = SLIDER_NAME;\nvar [SliderOrientationProvider, useSliderOrientationContext] = createSliderContext(SLIDER_NAME, {\n    startEdge: \"left\",\n    endEdge: \"right\",\n    size: \"width\",\n    direction: 1\n});\nvar SliderHorizontal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { min, max, dir, inverted, onSlideStart, onSlideMove, onSlideEnd, onStepKeyDown, ...sliderProps } = props;\n    const [slider, setSlider] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.useComposedRefs)(forwardedRef, {\n        \"SliderHorizontal.useComposedRefs[composedRefs]\": (node)=>setSlider(node)\n    }[\"SliderHorizontal.useComposedRefs[composedRefs]\"]);\n    const rectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_8__.useDirection)(dir);\n    const isDirectionLTR = direction === \"ltr\";\n    const isSlidingFromLeft = isDirectionLTR && !inverted || !isDirectionLTR && inverted;\n    function getValueFromPointer(pointerPosition) {\n        const rect = rectRef.current || slider.getBoundingClientRect();\n        const input = [\n            0,\n            rect.width\n        ];\n        const output = isSlidingFromLeft ? [\n            min,\n            max\n        ] : [\n            max,\n            min\n        ];\n        const value = linearScale(input, output);\n        rectRef.current = rect;\n        return value(pointerPosition - rect.left);\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderOrientationProvider, {\n        scope: props.__scopeSlider,\n        startEdge: isSlidingFromLeft ? \"left\" : \"right\",\n        endEdge: isSlidingFromLeft ? \"right\" : \"left\",\n        direction: isSlidingFromLeft ? 1 : -1,\n        size: \"width\",\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderImpl, {\n            dir: direction,\n            \"data-orientation\": \"horizontal\",\n            ...sliderProps,\n            ref: composedRefs,\n            style: {\n                ...sliderProps.style,\n                [\"--radix-slider-thumb-transform\"]: \"translateX(-50%)\"\n            },\n            onSlideStart: (event)=>{\n                const value = getValueFromPointer(event.clientX);\n                onSlideStart?.(value);\n            },\n            onSlideMove: (event)=>{\n                const value = getValueFromPointer(event.clientX);\n                onSlideMove?.(value);\n            },\n            onSlideEnd: ()=>{\n                rectRef.current = void 0;\n                onSlideEnd?.();\n            },\n            onStepKeyDown: (event)=>{\n                const slideDirection = isSlidingFromLeft ? \"from-left\" : \"from-right\";\n                const isBackKey = BACK_KEYS[slideDirection].includes(event.key);\n                onStepKeyDown?.({\n                    event,\n                    direction: isBackKey ? -1 : 1\n                });\n            }\n        })\n    });\n});\nvar SliderVertical = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { min, max, inverted, onSlideStart, onSlideMove, onSlideEnd, onStepKeyDown, ...sliderProps } = props;\n    const sliderRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.useComposedRefs)(forwardedRef, sliderRef);\n    const rectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    const isSlidingFromBottom = !inverted;\n    function getValueFromPointer(pointerPosition) {\n        const rect = rectRef.current || sliderRef.current.getBoundingClientRect();\n        const input = [\n            0,\n            rect.height\n        ];\n        const output = isSlidingFromBottom ? [\n            max,\n            min\n        ] : [\n            min,\n            max\n        ];\n        const value = linearScale(input, output);\n        rectRef.current = rect;\n        return value(pointerPosition - rect.top);\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderOrientationProvider, {\n        scope: props.__scopeSlider,\n        startEdge: isSlidingFromBottom ? \"bottom\" : \"top\",\n        endEdge: isSlidingFromBottom ? \"top\" : \"bottom\",\n        size: \"height\",\n        direction: isSlidingFromBottom ? 1 : -1,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderImpl, {\n            \"data-orientation\": \"vertical\",\n            ...sliderProps,\n            ref,\n            style: {\n                ...sliderProps.style,\n                [\"--radix-slider-thumb-transform\"]: \"translateY(50%)\"\n            },\n            onSlideStart: (event)=>{\n                const value = getValueFromPointer(event.clientY);\n                onSlideStart?.(value);\n            },\n            onSlideMove: (event)=>{\n                const value = getValueFromPointer(event.clientY);\n                onSlideMove?.(value);\n            },\n            onSlideEnd: ()=>{\n                rectRef.current = void 0;\n                onSlideEnd?.();\n            },\n            onStepKeyDown: (event)=>{\n                const slideDirection = isSlidingFromBottom ? \"from-bottom\" : \"from-top\";\n                const isBackKey = BACK_KEYS[slideDirection].includes(event.key);\n                onStepKeyDown?.({\n                    event,\n                    direction: isBackKey ? -1 : 1\n                });\n            }\n        })\n    });\n});\nvar SliderImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSlider, onSlideStart, onSlideMove, onSlideEnd, onHomeKeyDown, onEndKeyDown, onStepKeyDown, ...sliderProps } = props;\n    const context = useSliderContext(SLIDER_NAME, __scopeSlider);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.span, {\n        ...sliderProps,\n        ref: forwardedRef,\n        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onKeyDown, (event)=>{\n            if (event.key === \"Home\") {\n                onHomeKeyDown(event);\n                event.preventDefault();\n            } else if (event.key === \"End\") {\n                onEndKeyDown(event);\n                event.preventDefault();\n            } else if (PAGE_KEYS.concat(ARROW_KEYS).includes(event.key)) {\n                onStepKeyDown(event);\n                event.preventDefault();\n            }\n        }),\n        onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onPointerDown, (event)=>{\n            const target = event.target;\n            target.setPointerCapture(event.pointerId);\n            event.preventDefault();\n            if (context.thumbs.has(target)) {\n                target.focus();\n            } else {\n                onSlideStart(event);\n            }\n        }),\n        onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onPointerMove, (event)=>{\n            const target = event.target;\n            if (target.hasPointerCapture(event.pointerId)) onSlideMove(event);\n        }),\n        onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onPointerUp, (event)=>{\n            const target = event.target;\n            if (target.hasPointerCapture(event.pointerId)) {\n                target.releasePointerCapture(event.pointerId);\n                onSlideEnd(event);\n            }\n        })\n    });\n});\nvar TRACK_NAME = \"SliderTrack\";\nvar SliderTrack = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSlider, ...trackProps } = props;\n    const context = useSliderContext(TRACK_NAME, __scopeSlider);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.span, {\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        \"data-orientation\": context.orientation,\n        ...trackProps,\n        ref: forwardedRef\n    });\n});\nSliderTrack.displayName = TRACK_NAME;\nvar RANGE_NAME = \"SliderRange\";\nvar SliderRange = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSlider, ...rangeProps } = props;\n    const context = useSliderContext(RANGE_NAME, __scopeSlider);\n    const orientation = useSliderOrientationContext(RANGE_NAME, __scopeSlider);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.useComposedRefs)(forwardedRef, ref);\n    const valuesCount = context.values.length;\n    const percentages = context.values.map((value)=>convertValueToPercentage(value, context.min, context.max));\n    const offsetStart = valuesCount > 1 ? Math.min(...percentages) : 0;\n    const offsetEnd = 100 - Math.max(...percentages);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.span, {\n        \"data-orientation\": context.orientation,\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        ...rangeProps,\n        ref: composedRefs,\n        style: {\n            ...props.style,\n            [orientation.startEdge]: offsetStart + \"%\",\n            [orientation.endEdge]: offsetEnd + \"%\"\n        }\n    });\n});\nSliderRange.displayName = RANGE_NAME;\nvar THUMB_NAME = \"SliderThumb\";\nvar SliderThumb = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const getItems = useCollection(props.__scopeSlider);\n    const [thumb, setThumb] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.useComposedRefs)(forwardedRef, {\n        \"SliderThumb.useComposedRefs[composedRefs]\": (node)=>setThumb(node)\n    }[\"SliderThumb.useComposedRefs[composedRefs]\"]);\n    const index = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"SliderThumb.useMemo[index]\": ()=>thumb ? getItems().findIndex({\n                \"SliderThumb.useMemo[index]\": (item)=>item.ref.current === thumb\n            }[\"SliderThumb.useMemo[index]\"]) : -1\n    }[\"SliderThumb.useMemo[index]\"], [\n        getItems,\n        thumb\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderThumbImpl, {\n        ...props,\n        ref: composedRefs,\n        index\n    });\n});\nvar SliderThumbImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSlider, index, name, ...thumbProps } = props;\n    const context = useSliderContext(THUMB_NAME, __scopeSlider);\n    const orientation = useSliderOrientationContext(THUMB_NAME, __scopeSlider);\n    const [thumb, setThumb] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.useComposedRefs)(forwardedRef, {\n        \"SliderThumbImpl.useComposedRefs[composedRefs]\": (node)=>setThumb(node)\n    }[\"SliderThumbImpl.useComposedRefs[composedRefs]\"]);\n    const isFormControl = thumb ? context.form || !!thumb.closest(\"form\") : true;\n    const size = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_10__.useSize)(thumb);\n    const value = context.values[index];\n    const percent = value === void 0 ? 0 : convertValueToPercentage(value, context.min, context.max);\n    const label = getLabel(index, context.values.length);\n    const orientationSize = size?.[orientation.size];\n    const thumbInBoundsOffset = orientationSize ? getThumbInBoundsOffset(orientationSize, percent, orientation.direction) : 0;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SliderThumbImpl.useEffect\": ()=>{\n            if (thumb) {\n                context.thumbs.add(thumb);\n                return ({\n                    \"SliderThumbImpl.useEffect\": ()=>{\n                        context.thumbs.delete(thumb);\n                    }\n                })[\"SliderThumbImpl.useEffect\"];\n            }\n        }\n    }[\"SliderThumbImpl.useEffect\"], [\n        thumb,\n        context.thumbs\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"span\", {\n        style: {\n            transform: \"var(--radix-slider-thumb-transform)\",\n            position: \"absolute\",\n            [orientation.startEdge]: `calc(${percent}% + ${thumbInBoundsOffset}px)`\n        },\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.ItemSlot, {\n                scope: props.__scopeSlider,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.span, {\n                    role: \"slider\",\n                    \"aria-label\": props[\"aria-label\"] || label,\n                    \"aria-valuemin\": context.min,\n                    \"aria-valuenow\": value,\n                    \"aria-valuemax\": context.max,\n                    \"aria-orientation\": context.orientation,\n                    \"data-orientation\": context.orientation,\n                    \"data-disabled\": context.disabled ? \"\" : void 0,\n                    tabIndex: context.disabled ? void 0 : 0,\n                    ...thumbProps,\n                    ref: composedRefs,\n                    style: value === void 0 ? {\n                        display: \"none\"\n                    } : props.style,\n                    onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onFocus, ()=>{\n                        context.valueIndexToChangeRef.current = index;\n                    })\n                })\n            }),\n            isFormControl && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderBubbleInput, {\n                name: name ?? (context.name ? context.name + (context.values.length > 1 ? \"[]\" : \"\") : void 0),\n                form: context.form,\n                value\n            }, index)\n        ]\n    });\n});\nSliderThumb.displayName = THUMB_NAME;\nvar BUBBLE_INPUT_NAME = \"RadioBubbleInput\";\nvar SliderBubbleInput = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(({ __scopeSlider, value, ...props }, forwardedRef)=>{\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.useComposedRefs)(ref, forwardedRef);\n    const prevValue = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_11__.usePrevious)(value);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SliderBubbleInput.useEffect\": ()=>{\n            const input = ref.current;\n            if (!input) return;\n            const inputProto = window.HTMLInputElement.prototype;\n            const descriptor = Object.getOwnPropertyDescriptor(inputProto, \"value\");\n            const setValue = descriptor.set;\n            if (prevValue !== value && setValue) {\n                const event = new Event(\"input\", {\n                    bubbles: true\n                });\n                setValue.call(input, value);\n                input.dispatchEvent(event);\n            }\n        }\n    }[\"SliderBubbleInput.useEffect\"], [\n        prevValue,\n        value\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.input, {\n        style: {\n            display: \"none\"\n        },\n        ...props,\n        ref: composedRefs,\n        defaultValue: value\n    });\n});\nSliderBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction getNextSortedValues(prevValues = [], nextValue, atIndex) {\n    const nextValues = [\n        ...prevValues\n    ];\n    nextValues[atIndex] = nextValue;\n    return nextValues.sort((a, b)=>a - b);\n}\nfunction convertValueToPercentage(value, min, max) {\n    const maxSteps = max - min;\n    const percentPerStep = 100 / maxSteps;\n    const percentage = percentPerStep * (value - min);\n    return (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_5__.clamp)(percentage, [\n        0,\n        100\n    ]);\n}\nfunction getLabel(index, totalValues) {\n    if (totalValues > 2) {\n        return `Value ${index + 1} of ${totalValues}`;\n    } else if (totalValues === 2) {\n        return [\n            \"Minimum\",\n            \"Maximum\"\n        ][index];\n    } else {\n        return void 0;\n    }\n}\nfunction getClosestValueIndex(values, nextValue) {\n    if (values.length === 1) return 0;\n    const distances = values.map((value)=>Math.abs(value - nextValue));\n    const closestDistance = Math.min(...distances);\n    return distances.indexOf(closestDistance);\n}\nfunction getThumbInBoundsOffset(width, left, direction) {\n    const halfWidth = width / 2;\n    const halfPercent = 50;\n    const offset = linearScale([\n        0,\n        halfPercent\n    ], [\n        0,\n        halfWidth\n    ]);\n    return (halfWidth - offset(left) * direction) * direction;\n}\nfunction getStepsBetweenValues(values) {\n    return values.slice(0, -1).map((value, index)=>values[index + 1] - value);\n}\nfunction hasMinStepsBetweenValues(values, minStepsBetweenValues) {\n    if (minStepsBetweenValues > 0) {\n        const stepsBetweenValues = getStepsBetweenValues(values);\n        const actualMinStepsBetweenValues = Math.min(...stepsBetweenValues);\n        return actualMinStepsBetweenValues >= minStepsBetweenValues;\n    }\n    return true;\n}\nfunction linearScale(input, output) {\n    return (value)=>{\n        if (input[0] === input[1] || output[0] === output[1]) return output[0];\n        const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n        return output[0] + ratio * (value - input[0]);\n    };\n}\nfunction getDecimalCount(value) {\n    return (String(value).split(\".\")[1] || \"\").length;\n}\nfunction roundValue(value, decimalCount) {\n    const rounder = Math.pow(10, decimalCount);\n    return Math.round(value * rounder) / rounder;\n}\nvar Root = Slider;\nvar Track = SliderTrack;\nvar Range = SliderRange;\nvar Thumb = SliderThumb;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-slider@1.3._4c14d2fd402c58691c37d83bdf4590e9/node_modules/@radix-ui/react-slider/dist/index.mjs\n");

/***/ })

};
;