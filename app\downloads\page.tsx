"use client"

import { ProtectedRoute } from "@/components/auth/protected-route"
import { MainLayout } from "@/components/layout/main-layout"
import { DownloadManager } from "@/components/downloads/download-manager"

export default function DownloadsPage() {
  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <DownloadManager />
        </div>
      </MainLayout>
    </ProtectedRoute>
  )
}
