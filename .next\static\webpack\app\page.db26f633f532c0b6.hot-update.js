"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/landing-page.tsx":
/*!*************************************!*\
  !*** ./components/landing-page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LandingPage: () => (/* binding */ LandingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/microscope.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _components_ui_effects_particle_background__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui-effects/particle-background */ \"(app-pages-browser)/./components/ui-effects/particle-background.tsx\");\n/* harmony import */ var _components_ui_effects_statistic_counter__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui-effects/statistic-counter */ \"(app-pages-browser)/./components/ui-effects/statistic-counter.tsx\");\n/* harmony import */ var _components_landing_testimonial_carousel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/landing/testimonial-carousel */ \"(app-pages-browser)/./components/landing/testimonial-carousel.tsx\");\n/* harmony import */ var _components_landing_feature_showcase__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/landing/feature-showcase */ \"(app-pages-browser)/./components/landing/feature-showcase.tsx\");\n/* harmony import */ var _components_landing_ai_demo__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/landing/ai-demo */ \"(app-pages-browser)/./components/landing/ai-demo.tsx\");\n/* harmony import */ var _components_visualization_3d_cell_structure__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/visualization/3d-cell-structure */ \"(app-pages-browser)/./components/visualization/3d-cell-structure.tsx\");\n/* harmony import */ var _components_visualization_full_body_neural_network_3d__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/visualization/full-body-neural-network-3d */ \"(app-pages-browser)/./components/visualization/full-body-neural-network-3d.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_12__);\n/* __next_internal_client_entry_do_not_use__ LandingPage auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction LandingPage() {\n    _s();\n    const [currentFeature, setCurrentFeature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            setIsVisible(true);\n            const interval = setInterval({\n                \"LandingPage.useEffect.interval\": ()=>{\n                    setCurrentFeature({\n                        \"LandingPage.useEffect.interval\": (prev)=>(prev + 1) % 4\n                    }[\"LandingPage.useEffect.interval\"]);\n                }\n            }[\"LandingPage.useEffect.interval\"], 5000);\n            return ({\n                \"LandingPage.useEffect\": ()=>clearInterval(interval)\n            })[\"LandingPage.useEffect\"];\n        }\n    }[\"LandingPage.useEffect\"], []);\n    const features = [\n        {\n            icon: _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            title: \"AI-Powered Diagnosis\",\n            description: \"Advanced machine learning algorithms for accurate brain tumor detection and classification\",\n            color: \"text-teal-400\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            title: \"Real-time Monitoring\",\n            description: \"Continuous patient monitoring with intelligent alerts and predictive analytics\",\n            color: \"text-blue-400\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            title: \"Predictive Analytics\",\n            description: \"Data-driven insights for treatment planning and outcome prediction\",\n            color: \"text-green-400\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            title: \"Secure & Compliant\",\n            description: \"HIPAA-compliant platform with enterprise-grade security and data protection\",\n            color: \"text-purple-400\"\n        }\n    ];\n    const stats = [\n        {\n            label: \"Patients Treated\",\n            value: 10000,\n            suffix: \"+\"\n        },\n        {\n            label: \"Diagnostic Accuracy\",\n            value: 97.3,\n            suffix: \"%\"\n        },\n        {\n            label: \"Healthcare Partners\",\n            value: 250,\n            suffix: \"+\"\n        },\n        {\n            label: \"Countries Served\",\n            value: 15,\n            suffix: \"\"\n        }\n    ];\n    const benefits = [\n        \"Reduce diagnostic time by 60%\",\n        \"Improve accuracy by 25%\",\n        \"24/7 AI-powered monitoring\",\n        \"Seamless EHR integration\",\n        \"Real-time collaboration tools\",\n        \"Comprehensive reporting suite\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-teal-900 to-slate-900 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_effects_particle_background__WEBPACK_IMPORTED_MODULE_5__.ParticleBackground, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"relative z-50 bg-black/20 backdrop-blur-xl border-b border-teal-500/30\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-8 w-8 text-teal-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl sm:text-2xl font-bold text-teal-400\",\n                                        children: \"Quant-NEX\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex items-center gap-6 lg:gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#features\",\n                                        className: \"text-gray-300 hover:text-teal-400 transition-colors text-sm lg:text-base\",\n                                        children: \"Features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#demo\",\n                                        className: \"text-gray-300 hover:text-teal-400 transition-colors text-sm lg:text-base\",\n                                        children: \"Demo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#testimonials\",\n                                        className: \"text-gray-300 hover:text-teal-400 transition-colors text-sm lg:text-base\",\n                                        children: \"Testimonials\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#pricing\",\n                                        className: \"text-gray-300 hover:text-teal-400 transition-colors text-sm lg:text-base\",\n                                        children: \"Pricing\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 sm:gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_12___default()), {\n                                        href: \"/login\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"glow-hover bg-transparent text-xs sm:text-sm\",\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_12___default()), {\n                                        href: \"/login\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"sm\",\n                                            className: \"btn-glow-primary text-xs sm:text-sm\",\n                                            children: \"Get Started\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative z-10 pt-12 sm:pt-16 lg:pt-20 pb-16 sm:pb-24 lg:pb-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6 lg:space-y-8 \".concat(isVisible ? \"animate-slide-up\" : \"opacity-0\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: \"bg-teal-600/20 text-teal-400 border-teal-500/30 text-xs sm:text-sm\",\n                                                children: \"\\uD83D\\uDE80 Next-Generation Medical AI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold text-white leading-tight\",\n                                                children: [\n                                                    \"Revolutionizing\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"block text-transparent bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text\",\n                                                        children: \"Brain Tumor\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Diagnosis\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg sm:text-xl text-gray-300 leading-relaxed max-w-2xl\",\n                                                children: \"Harness the power of AI to transform medical diagnosis, treatment planning, and patient care. Join thousands of healthcare professionals using Quant-NEX to save lives.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_12___default()), {\n                                                href: \"/login\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    size: \"lg\",\n                                                    className: \"btn-glow-primary text-base sm:text-lg px-6 sm:px-8 py-4 sm:py-6 w-full sm:w-auto\",\n                                                    children: [\n                                                        \"Start Free Trial\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"ml-2 h-4 w-4 sm:h-5 sm:w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"lg\",\n                                                variant: \"outline\",\n                                                className: \"glow-hover bg-transparent text-base sm:text-lg px-6 sm:px-8 py-4 sm:py-6 w-full sm:w-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4 sm:h-5 sm:w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Watch Demo\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row items-start sm:items-center gap-4 sm:gap-6 pt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    ...Array(5)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4 sm:h-5 sm:w-5 text-yellow-400 fill-current\"\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm sm:text-base text-gray-300\",\n                                                children: \"Trusted by 250+ healthcare institutions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative mt-8 lg:mt-0 \".concat(isVisible ? \"animate-slide-up\" : \"opacity-0\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-teal-400/20 to-blue-400/20 rounded-3xl blur-3xl\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_visualization_3d_cell_structure__WEBPACK_IMPORTED_MODULE_10__.CellStructure3D, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative z-10 py-12 sm:py-16 lg:py-20 bg-black/20 backdrop-blur-xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8\",\n                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold text-teal-400 mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_effects_statistic_counter__WEBPACK_IMPORTED_MODULE_6__.StatisticCounter, {\n                                            end: stat.value,\n                                            suffix: stat.suffix\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm sm:text-base text-gray-300\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                className: \"relative z-10 py-16 sm:py-24 lg:py-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12 lg:mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-4 lg:mb-6\",\n                                    children: [\n                                        \"Powerful Features for\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-transparent bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text\",\n                                            children: \"Modern Healthcare\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg sm:text-xl text-gray-300 max-w-3xl mx-auto\",\n                                    children: \"Our comprehensive platform combines cutting-edge AI technology with intuitive design to deliver unprecedented capabilities for medical professionals.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center mb-12 lg:mb-20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6 lg:space-y-8\",\n                                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 lg:p-6 rounded-xl border transition-all duration-500 cursor-pointer \".concat(currentFeature === index ? \"border-teal-400 bg-teal-900/20 shadow-lg shadow-teal-400/20\" : \"border-teal-500/30 bg-black/20 hover:border-teal-400/50\"),\n                                            onClick: ()=>setCurrentFeature(index),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 rounded-lg bg-gradient-to-r from-teal-500/20 to-blue-500/20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                            className: \"h-5 w-5 lg:h-6 lg:w-6 \".concat(feature.color)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg lg:text-xl font-semibold text-white mb-2\",\n                                                                children: feature.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm lg:text-base text-gray-300\",\n                                                                children: feature.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative mt-8 lg:mt-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_feature_showcase__WEBPACK_IMPORTED_MODULE_8__.FeatureShowcase, {\n                                        currentFeature: currentFeature\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6\",\n                            children: benefits.map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 p-3 lg:p-4 rounded-lg bg-black/20 border border-teal-500/30\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-4 w-4 lg:h-5 lg:w-5 text-green-400 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm lg:text-base text-gray-300\",\n                                            children: benefit\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"demo\",\n                className: \"relative z-10 py-16 sm:py-24 lg:py-32 bg-black/20 backdrop-blur-xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12 lg:mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-4 lg:mb-6\",\n                                    children: [\n                                        \"Experience AI in\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-transparent bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text\",\n                                            children: \"Action\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg sm:text-xl text-gray-300 max-w-3xl mx-auto\",\n                                    children: \"See how our advanced AI algorithms analyze medical data in real-time to provide accurate diagnoses and treatment recommendations.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 xl:grid-cols-2 gap-6 lg:gap-8 mb-8 lg:mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"card-glow border-teal-500/30 bg-black/40 backdrop-blur-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"text-teal-400 flex items-center gap-2 text-lg lg:text-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-5 w-5 lg:h-6 lg:w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Cellular Structure Analysis\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"pt-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_visualization_3d_cell_structure__WEBPACK_IMPORTED_MODULE_10__.CellStructure3D, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"card-glow border-teal-500/30 bg-black/40 backdrop-blur-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"text-teal-400 flex items-center gap-2 text-lg lg:text-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-5 w-5 lg:h-6 lg:w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Full Body Neural Network\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"pt-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_visualization_full_body_neural_network_3d__WEBPACK_IMPORTED_MODULE_11__.FullBodyNeuralNetwork3D, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 lg:mt-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_ai_demo__WEBPACK_IMPORTED_MODULE_9__.AIDemo, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 274,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"testimonials\",\n                className: \"relative z-10 py-16 sm:py-24 lg:py-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12 lg:mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-4 lg:mb-6\",\n                                    children: [\n                                        \"Trusted by\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-transparent bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text\",\n                                            children: \"Healthcare Leaders\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg sm:text-xl text-gray-300 max-w-3xl mx-auto\",\n                                    children: \"Join thousands of medical professionals who are already transforming patient care with Quant-NEX.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_testimonial_carousel__WEBPACK_IMPORTED_MODULE_7__.TestimonialCarousel, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-12 lg:mt-16 grid grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-10 w-10 lg:h-12 lg:w-12 text-teal-400 mx-auto mb-3 lg:mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-2 text-sm lg:text-base\",\n                                            children: \"Global Reach\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs lg:text-sm text-gray-400\",\n                                            children: \"15+ countries worldwide\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-10 w-10 lg:h-12 lg:w-12 text-teal-400 mx-auto mb-3 lg:mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-2 text-sm lg:text-base\",\n                                            children: \"Award Winning\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs lg:text-sm text-gray-400\",\n                                            children: \"Healthcare Innovation Award 2024\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-10 w-10 lg:h-12 lg:w-12 text-teal-400 mx-auto mb-3 lg:mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-2 text-sm lg:text-base\",\n                                            children: \"HIPAA Compliant\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs lg:text-sm text-gray-400\",\n                                            children: \"Enterprise-grade security\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-10 w-10 lg:h-12 lg:w-12 text-teal-400 mx-auto mb-3 lg:mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-2 text-sm lg:text-base\",\n                                            children: \"97.3% Accuracy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs lg:text-sm text-gray-400\",\n                                            children: \"Clinically validated\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 322,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"pricing\",\n                className: \"relative z-10 py-16 sm:py-24 lg:py-32 bg-black/20 backdrop-blur-xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12 lg:mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-4 lg:mb-6\",\n                                    children: [\n                                        \"Choose Your\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-transparent bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text\",\n                                            children: \"Plan\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg sm:text-xl text-gray-300 max-w-3xl mx-auto\",\n                                    children: \"Flexible pricing options designed to scale with your healthcare organization's needs.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"card-glow border-teal-500/30 bg-black/40 backdrop-blur-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-white text-lg lg:text-xl\",\n                                                    children: \"Starter\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl lg:text-3xl font-bold text-teal-400\",\n                                                    children: [\n                                                        \"₹9,999\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-base lg:text-lg text-gray-400\",\n                                                            children: \"/month\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Up to 100 patients\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 395,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Basic AI diagnosis\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 396,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 399,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Standard reporting\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 400,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 403,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Email support\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 404,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    className: \"w-full btn-glow-primary\",\n                                                    children: \"Start Free Trial\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"card-glow border-teal-400 bg-teal-900/20 backdrop-blur-xl relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-4 left-1/2 transform -translate-x-1/2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: \"bg-teal-600 text-white\",\n                                                children: \"Most Popular\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-white text-lg lg:text-xl\",\n                                                    children: \"Professional\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl lg:text-3xl font-bold text-teal-400\",\n                                                    children: [\n                                                        \"₹24,999\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-base lg:text-lg text-gray-400\",\n                                                            children: \"/month\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 26\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Up to 500 patients\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 426,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 429,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Advanced AI features\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 430,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Real-time monitoring\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 437,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Priority support\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 441,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"API access\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 442,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    className: \"w-full btn-glow-primary\",\n                                                    children: \"Start Free Trial\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"card-glow border-teal-500/30 bg-black/40 backdrop-blur-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-white text-lg lg:text-xl\",\n                                                    children: \"Enterprise\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl lg:text-3xl font-bold text-teal-400\",\n                                                    children: [\n                                                        \"Custom\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-base lg:text-lg text-gray-400\",\n                                                            children: \" pricing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 460,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Unlimited patients\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 461,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 464,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Full AI suite\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 465,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 463,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 468,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Custom integrations\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 469,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"24/7 dedicated support\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 476,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"On-premise deployment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 477,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"w-full glow-hover bg-transparent\",\n                                                    children: \"Contact Sales\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 366,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 365,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative z-10 py-16 sm:py-24 lg:py-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6 lg:space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-white\",\n                                children: [\n                                    \"Ready to Transform\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-transparent bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text\",\n                                        children: \"Healthcare?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 493,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg sm:text-xl text-gray-300\",\n                                children: \"Join thousands of healthcare professionals who are already using Quant-NEX to improve patient outcomes and streamline their workflow. Start your free trial today.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_12___default()), {\n                                        href: \"/login\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"lg\",\n                                            className: \"btn-glow-primary text-base sm:text-lg px-6 sm:px-8 py-4 sm:py-6 w-full sm:w-auto\",\n                                            children: [\n                                                \"Start Free Trial\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"ml-2 h-4 w-4 sm:h-5 sm:w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"lg\",\n                                        variant: \"outline\",\n                                        className: \"glow-hover bg-transparent text-base sm:text-lg px-6 sm:px-8 py-4 sm:py-6 w-full sm:w-auto\",\n                                        children: \"Schedule Demo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                        lineNumber: 492,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 491,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 490,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"relative z-10 bg-black/40 backdrop-blur-xl border-t border-teal-500/30\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 lg:py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6 lg:gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-6 w-6 lg:h-8 lg:w-8 text-teal-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl lg:text-2xl font-bold text-teal-400\",\n                                                    children: \"Quant-NEX\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 530,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm lg:text-base text-gray-400\",\n                                            children: \"Revolutionizing healthcare with AI-powered medical diagnosis and treatment planning.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 529,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-4 text-sm lg:text-base\",\n                                            children: \"Product\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-400 text-sm lg:text-base\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Pricing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"API\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Integrations\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-4 text-sm lg:text-base\",\n                                            children: \"Company\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-400 text-sm lg:text-base\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"About\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Careers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Press\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 579,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Contact\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 584,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 583,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 567,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-4 text-sm lg:text-base\",\n                                            children: \"Support\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 592,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-400 text-sm lg:text-base\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Documentation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Help Center\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Community\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 605,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 609,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 591,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 528,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-teal-500/30 mt-8 lg:mt-12 pt-6 lg:pt-8 flex flex-col md:flex-row justify-between items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm lg:text-base text-gray-400\",\n                                    children: \"\\xa9 2024 Quant-NEX. All rights reserved.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 lg:gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-sm lg:text-base text-gray-400 hover:text-teal-400 transition-colors\",\n                                            children: \"Privacy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-sm lg:text-base text-gray-400 hover:text-teal-400 transition-colors\",\n                                            children: \"Terms\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 624,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-sm lg:text-base text-gray-400 hover:text-teal-400 transition-colors\",\n                                            children: \"Security\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 627,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 620,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 618,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 527,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 526,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingPage, \"U51Q3I1DXByzsY0PyH7u+FF1mxw=\");\n_c = LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/landing-page.tsx\n"));

/***/ })

});