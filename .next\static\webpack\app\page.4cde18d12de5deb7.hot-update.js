"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/landing-page.tsx":
/*!*************************************!*\
  !*** ./components/landing-page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LandingPage: () => (/* binding */ LandingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./contexts/auth-context.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/microscope.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,Award,Brain,CheckCircle,Globe,Microscope,Play,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _components_ui_effects_particle_background__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui-effects/particle-background */ \"(app-pages-browser)/./components/ui-effects/particle-background.tsx\");\n/* harmony import */ var _components_ui_effects_statistic_counter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui-effects/statistic-counter */ \"(app-pages-browser)/./components/ui-effects/statistic-counter.tsx\");\n/* harmony import */ var _components_landing_testimonial_carousel__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/landing/testimonial-carousel */ \"(app-pages-browser)/./components/landing/testimonial-carousel.tsx\");\n/* harmony import */ var _components_landing_feature_showcase__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/landing/feature-showcase */ \"(app-pages-browser)/./components/landing/feature-showcase.tsx\");\n/* harmony import */ var _components_landing_ai_demo__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/landing/ai-demo */ \"(app-pages-browser)/./components/landing/ai-demo.tsx\");\n/* harmony import */ var _components_visualization_3d_cell_structure__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/visualization/3d-cell-structure */ \"(app-pages-browser)/./components/visualization/3d-cell-structure.tsx\");\n/* harmony import */ var _components_visualization_full_body_neural_network_3d__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/visualization/full-body-neural-network-3d */ \"(app-pages-browser)/./components/visualization/full-body-neural-network-3d.tsx\");\n/* __next_internal_client_entry_do_not_use__ LandingPage auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction LandingPage() {\n    _s();\n    const [currentFeature, setCurrentFeature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNavigating, setIsNavigating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isAuthenticated, isLoading } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const handleNavigation = async (path)=>{\n        setIsNavigating(true);\n        try {\n            await router.push(path);\n        } catch (error) {\n            console.error('Navigation error:', error);\n        } finally{\n            setIsNavigating(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            setMounted(true);\n            setIsVisible(true);\n            const interval = setInterval({\n                \"LandingPage.useEffect.interval\": ()=>{\n                    setCurrentFeature({\n                        \"LandingPage.useEffect.interval\": (prev)=>(prev + 1) % 4\n                    }[\"LandingPage.useEffect.interval\"]);\n                }\n            }[\"LandingPage.useEffect.interval\"], 5000);\n            return ({\n                \"LandingPage.useEffect\": ()=>clearInterval(interval)\n            })[\"LandingPage.useEffect\"];\n        }\n    }[\"LandingPage.useEffect\"], []);\n    // Redirect authenticated users to dashboard\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            if (mounted && !isLoading && isAuthenticated) {\n                router.push('/dashboard');\n            }\n        }\n    }[\"LandingPage.useEffect\"], [\n        mounted,\n        isLoading,\n        isAuthenticated,\n        router\n    ]);\n    // Prevent hydration mismatch by not rendering until mounted\n    if (!mounted) {\n        return null;\n    }\n    const features = [\n        {\n            icon: _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            title: \"AI-Powered Diagnosis\",\n            description: \"Advanced machine learning algorithms for accurate brain tumor detection and classification\",\n            color: \"text-teal-400\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            title: \"Real-time Monitoring\",\n            description: \"Continuous patient monitoring with intelligent alerts and predictive analytics\",\n            color: \"text-blue-400\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            title: \"Predictive Analytics\",\n            description: \"Data-driven insights for treatment planning and outcome prediction\",\n            color: \"text-green-400\"\n        },\n        {\n            icon: _barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            title: \"Secure & Compliant\",\n            description: \"HIPAA-compliant platform with enterprise-grade security and data protection\",\n            color: \"text-purple-400\"\n        }\n    ];\n    const stats = [\n        {\n            label: \"Patients Treated\",\n            value: 10000,\n            suffix: \"+\"\n        },\n        {\n            label: \"Diagnostic Accuracy\",\n            value: 97.3,\n            suffix: \"%\"\n        },\n        {\n            label: \"Healthcare Partners\",\n            value: 250,\n            suffix: \"+\"\n        },\n        {\n            label: \"Countries Served\",\n            value: 15,\n            suffix: \"\"\n        }\n    ];\n    const benefits = [\n        \"Reduce diagnostic time by 60%\",\n        \"Improve accuracy by 25%\",\n        \"24/7 AI-powered monitoring\",\n        \"Seamless EHR integration\",\n        \"Real-time collaboration tools\",\n        \"Comprehensive reporting suite\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-teal-900 to-slate-900 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_effects_particle_background__WEBPACK_IMPORTED_MODULE_7__.ParticleBackground, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"relative z-50 bg-black/20 backdrop-blur-xl border-b border-teal-500/30\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-8 w-8 text-teal-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl sm:text-2xl font-bold text-teal-400\",\n                                        children: \"Quant-NEX\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex items-center gap-6 lg:gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#features\",\n                                        className: \"text-gray-300 hover:text-teal-400 transition-colors text-sm lg:text-base\",\n                                        children: \"Features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#demo\",\n                                        className: \"text-gray-300 hover:text-teal-400 transition-colors text-sm lg:text-base\",\n                                        children: \"Demo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#testimonials\",\n                                        className: \"text-gray-300 hover:text-teal-400 transition-colors text-sm lg:text-base\",\n                                        children: \"Testimonials\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#pricing\",\n                                        className: \"text-gray-300 hover:text-teal-400 transition-colors text-sm lg:text-base\",\n                                        children: \"Pricing\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 sm:gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"glow-hover bg-transparent text-xs sm:text-sm\",\n                                        onClick: ()=>handleNavigation('/login'),\n                                        disabled: isNavigating || isLoading,\n                                        children: isNavigating ? 'Loading...' : 'Sign In'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        size: \"sm\",\n                                        className: \"btn-glow-primary text-xs sm:text-sm\",\n                                        onClick: ()=>handleNavigation('/login'),\n                                        disabled: isNavigating || isLoading,\n                                        children: isNavigating ? 'Loading...' : 'Get Started'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative z-10 pt-12 sm:pt-16 lg:pt-20 pb-16 sm:pb-24 lg:pb-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6 lg:space-y-8 \".concat(isVisible ? \"animate-slide-up\" : \"opacity-0\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                className: \"bg-teal-600/20 text-teal-400 border-teal-500/30 text-xs sm:text-sm\",\n                                                children: \"\\uD83D\\uDE80 Next-Generation Medical AI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold text-white leading-tight\",\n                                                children: [\n                                                    \"Revolutionizing\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"block text-transparent bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text\",\n                                                        children: \"Brain Tumor\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Diagnosis\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg sm:text-xl text-gray-300 leading-relaxed max-w-2xl\",\n                                                children: \"Harness the power of AI to transform medical diagnosis, treatment planning, and patient care. Join thousands of healthcare professionals using Quant-NEX to save lives.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                size: \"lg\",\n                                                className: \"btn-glow-primary text-base sm:text-lg px-6 sm:px-8 py-4 sm:py-6 w-full sm:w-auto\",\n                                                onClick: ()=>handleNavigation('/login'),\n                                                disabled: isNavigating || isLoading,\n                                                children: [\n                                                    isNavigating ? 'Loading...' : 'Start Free Trial',\n                                                    !isNavigating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"ml-2 h-4 w-4 sm:h-5 sm:w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                size: \"lg\",\n                                                variant: \"outline\",\n                                                className: \"glow-hover bg-transparent text-base sm:text-lg px-6 sm:px-8 py-4 sm:py-6 w-full sm:w-auto\",\n                                                onClick: ()=>{\n                                                    const demoSection = document.getElementById('demo');\n                                                    if (demoSection) {\n                                                        demoSection.scrollIntoView({\n                                                            behavior: 'smooth'\n                                                        });\n                                                    }\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4 sm:h-5 sm:w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Watch Demo\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row items-start sm:items-center gap-4 sm:gap-6 pt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    ...Array(5)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-4 w-4 sm:h-5 sm:w-5 text-yellow-400 fill-current\"\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm sm:text-base text-gray-300\",\n                                                children: \"Trusted by 250+ healthcare institutions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative mt-8 lg:mt-0 \".concat(isVisible ? \"animate-slide-up\" : \"opacity-0\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-teal-400/20 to-blue-400/20 rounded-3xl blur-3xl\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_visualization_3d_cell_structure__WEBPACK_IMPORTED_MODULE_12__.CellStructure3D, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative z-10 py-12 sm:py-16 lg:py-20 bg-black/20 backdrop-blur-xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8\",\n                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold text-teal-400 mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_effects_statistic_counter__WEBPACK_IMPORTED_MODULE_8__.StatisticCounter, {\n                                            end: stat.value,\n                                            suffix: stat.suffix\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm sm:text-base text-gray-300\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 238,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                className: \"relative z-10 py-16 sm:py-24 lg:py-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12 lg:mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-4 lg:mb-6\",\n                                    children: [\n                                        \"Powerful Features for\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-transparent bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text\",\n                                            children: \"Modern Healthcare\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg sm:text-xl text-gray-300 max-w-3xl mx-auto\",\n                                    children: \"Our comprehensive platform combines cutting-edge AI technology with intuitive design to deliver unprecedented capabilities for medical professionals.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center mb-12 lg:mb-20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6 lg:space-y-8\",\n                                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 lg:p-6 rounded-xl border transition-all duration-500 cursor-pointer \".concat(currentFeature === index ? \"border-teal-400 bg-teal-900/20 shadow-lg shadow-teal-400/20\" : \"border-teal-500/30 bg-black/20 hover:border-teal-400/50\"),\n                                            onClick: ()=>setCurrentFeature(index),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 rounded-lg bg-gradient-to-r from-teal-500/20 to-blue-500/20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                            className: \"h-5 w-5 lg:h-6 lg:w-6 \".concat(feature.color)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg lg:text-xl font-semibold text-white mb-2\",\n                                                                children: feature.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm lg:text-base text-gray-300\",\n                                                                children: feature.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative mt-8 lg:mt-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_feature_showcase__WEBPACK_IMPORTED_MODULE_10__.FeatureShowcase, {\n                                        currentFeature: currentFeature\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6\",\n                            children: benefits.map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 p-3 lg:p-4 rounded-lg bg-black/20 border border-teal-500/30\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-4 w-4 lg:h-5 lg:w-5 text-green-400 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm lg:text-base text-gray-300\",\n                                            children: benefit\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"demo\",\n                className: \"relative z-10 py-16 sm:py-24 lg:py-32 bg-black/20 backdrop-blur-xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12 lg:mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-4 lg:mb-6\",\n                                    children: [\n                                        \"Experience AI in\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-transparent bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text\",\n                                            children: \"Action\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg sm:text-xl text-gray-300 max-w-3xl mx-auto\",\n                                    children: \"See how our advanced AI algorithms analyze medical data in real-time to provide accurate diagnoses and treatment recommendations.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 xl:grid-cols-2 gap-6 lg:gap-8 mb-8 lg:mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"card-glow border-teal-500/30 bg-black/40 backdrop-blur-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"text-teal-400 flex items-center gap-2 text-lg lg:text-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-5 w-5 lg:h-6 lg:w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Cellular Structure Analysis\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"pt-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_visualization_3d_cell_structure__WEBPACK_IMPORTED_MODULE_12__.CellStructure3D, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"card-glow border-teal-500/30 bg-black/40 backdrop-blur-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"text-teal-400 flex items-center gap-2 text-lg lg:text-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-5 w-5 lg:h-6 lg:w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Full Body Neural Network\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"pt-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_visualization_full_body_neural_network_3d__WEBPACK_IMPORTED_MODULE_13__.FullBodyNeuralNetwork3D, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 lg:mt-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_ai_demo__WEBPACK_IMPORTED_MODULE_11__.AIDemo, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"testimonials\",\n                className: \"relative z-10 py-16 sm:py-24 lg:py-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12 lg:mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-4 lg:mb-6\",\n                                    children: [\n                                        \"Trusted by\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-transparent bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text\",\n                                            children: \"Healthcare Leaders\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg sm:text-xl text-gray-300 max-w-3xl mx-auto\",\n                                    children: \"Join thousands of medical professionals who are already transforming patient care with Quant-NEX.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_testimonial_carousel__WEBPACK_IMPORTED_MODULE_9__.TestimonialCarousel, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-12 lg:mt-16 grid grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-10 w-10 lg:h-12 lg:w-12 text-teal-400 mx-auto mb-3 lg:mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-2 text-sm lg:text-base\",\n                                            children: \"Global Reach\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs lg:text-sm text-gray-400\",\n                                            children: \"15+ countries worldwide\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-10 w-10 lg:h-12 lg:w-12 text-teal-400 mx-auto mb-3 lg:mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-2 text-sm lg:text-base\",\n                                            children: \"Award Winning\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs lg:text-sm text-gray-400\",\n                                            children: \"Healthcare Innovation Award 2024\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-10 w-10 lg:h-12 lg:w-12 text-teal-400 mx-auto mb-3 lg:mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-2 text-sm lg:text-base\",\n                                            children: \"HIPAA Compliant\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs lg:text-sm text-gray-400\",\n                                            children: \"Enterprise-grade security\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-10 w-10 lg:h-12 lg:w-12 text-teal-400 mx-auto mb-3 lg:mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-2 text-sm lg:text-base\",\n                                            children: \"97.3% Accuracy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs lg:text-sm text-gray-400\",\n                                            children: \"Clinically validated\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 364,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 363,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"pricing\",\n                className: \"relative z-10 py-16 sm:py-24 lg:py-32 bg-black/20 backdrop-blur-xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12 lg:mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-4 lg:mb-6\",\n                                    children: [\n                                        \"Choose Your\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-transparent bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text\",\n                                            children: \"Plan\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg sm:text-xl text-gray-300 max-w-3xl mx-auto\",\n                                    children: \"Flexible pricing options designed to scale with your healthcare organization's needs.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"card-glow border-teal-500/30 bg-black/40 backdrop-blur-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    className: \"text-white text-lg lg:text-xl\",\n                                                    children: \"Starter\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl lg:text-3xl font-bold text-teal-400\",\n                                                    children: [\n                                                        \"₹9,999\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-base lg:text-lg text-gray-400\",\n                                                            children: \"/month\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Up to 100 patients\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Basic AI diagnosis\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 437,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 440,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Standard reporting\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 441,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Email support\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    className: \"w-full btn-glow-primary\",\n                                                    children: \"Start Free Trial\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"card-glow border-teal-400 bg-teal-900/20 backdrop-blur-xl relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-4 left-1/2 transform -translate-x-1/2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                className: \"bg-teal-600 text-white\",\n                                                children: \"Most Popular\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    className: \"text-white text-lg lg:text-xl\",\n                                                    children: \"Professional\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl lg:text-3xl font-bold text-teal-400\",\n                                                    children: [\n                                                        \"₹24,999\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-base lg:text-lg text-gray-400\",\n                                                            children: \"/month\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 26\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 466,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Up to 500 patients\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 470,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Advanced AI features\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 474,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Real-time monitoring\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 475,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 478,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Priority support\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"API access\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    className: \"w-full btn-glow-primary\",\n                                                    children: \"Start Free Trial\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"card-glow border-teal-500/30 bg-black/40 backdrop-blur-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            className: \"pb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    className: \"text-white text-lg lg:text-xl\",\n                                                    children: \"Enterprise\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl lg:text-3xl font-bold text-teal-400\",\n                                                    children: [\n                                                        \"Custom\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-base lg:text-lg text-gray-400\",\n                                                            children: \" pricing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 501,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Unlimited patients\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Full AI suite\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 506,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 509,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"Custom integrations\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 513,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"24/7 dedicated support\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 514,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 512,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 517,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm lg:text-base text-gray-300\",\n                                                                    children: \"On-premise deployment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                                    lineNumber: 518,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"w-full glow-hover bg-transparent\",\n                                                    children: \"Contact Sales\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 407,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 406,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative z-10 py-16 sm:py-24 lg:py-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6 lg:space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-white\",\n                                children: [\n                                    \"Ready to Transform\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-transparent bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text\",\n                                        children: \"Healthcare?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg sm:text-xl text-gray-300\",\n                                children: \"Join thousands of healthcare professionals who are already using Quant-NEX to improve patient outcomes and streamline their workflow. Start your free trial today.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 540,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        size: \"lg\",\n                                        className: \"btn-glow-primary text-base sm:text-lg px-6 sm:px-8 py-4 sm:py-6 w-full sm:w-auto\",\n                                        onClick: ()=>handleNavigation('/login'),\n                                        disabled: isNavigating || isLoading,\n                                        children: [\n                                            isNavigating ? 'Loading...' : 'Start Free Trial',\n                                            !isNavigating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"ml-2 h-4 w-4 sm:h-5 sm:w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 35\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        size: \"lg\",\n                                        variant: \"outline\",\n                                        className: \"glow-hover bg-transparent text-base sm:text-lg px-6 sm:px-8 py-4 sm:py-6 w-full sm:w-auto\",\n                                        onClick: ()=>{\n                                            const demoSection = document.getElementById('demo');\n                                            if (demoSection) {\n                                                demoSection.scrollIntoView({\n                                                    behavior: 'smooth'\n                                                });\n                                            }\n                                        },\n                                        children: \"Schedule Demo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                lineNumber: 544,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                        lineNumber: 533,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 532,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 531,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"relative z-10 bg-black/40 backdrop-blur-xl border-t border-teal-500/30\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 lg:py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6 lg:gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_Award_Brain_CheckCircle_Globe_Microscope_Play_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-6 w-6 lg:h-8 lg:w-8 text-teal-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl lg:text-2xl font-bold text-teal-400\",\n                                                    children: \"Quant-NEX\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm lg:text-base text-gray-400\",\n                                            children: \"Revolutionizing healthcare with AI-powered medical diagnosis and treatment planning.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 581,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 576,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-4 text-sm lg:text-base\",\n                                            children: \"Product\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 587,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-400 text-sm lg:text-base\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Pricing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"API\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Integrations\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 605,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 588,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 586,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-4 text-sm lg:text-base\",\n                                            children: \"Company\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-400 text-sm lg:text-base\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"About\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Careers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 621,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 620,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Press\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 626,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Contact\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 630,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 614,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white mb-4 text-sm lg:text-base\",\n                                            children: \"Support\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 639,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-400 text-sm lg:text-base\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Documentation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Help Center\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 647,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 646,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Community\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 652,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 651,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-teal-400 transition-colors\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                        lineNumber: 657,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                                    lineNumber: 656,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 575,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-teal-500/30 mt-8 lg:mt-12 pt-6 lg:pt-8 flex flex-col md:flex-row justify-between items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm lg:text-base text-gray-400\",\n                                    children: \"\\xa9 2024 Quant-NEX. All rights reserved.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 666,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 lg:gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-sm lg:text-base text-gray-400 hover:text-teal-400 transition-colors\",\n                                            children: \"Privacy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-sm lg:text-base text-gray-400 hover:text-teal-400 transition-colors\",\n                                            children: \"Terms\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 671,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-sm lg:text-base text-gray-400 hover:text-teal-400 transition-colors\",\n                                            children: \"Security\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                            lineNumber: 674,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                                    lineNumber: 667,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                            lineNumber: 665,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                    lineNumber: 574,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n                lineNumber: 573,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\landing-page.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingPage, \"byeyjev5eUx2/xpRV4UuubiT8UE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/landing-page.tsx\n"));

/***/ })

});