@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Professional Teal Theme */
    --background: 8 12% 8%; /* #131619 */
    --foreground: 0 0% 98%; /* #fafafa */

    --card: 12 15% 12%; /* #1a1f23 */
    --card-foreground: 0 0% 98%;

    --popover: 12 15% 12%;
    --popover-foreground: 0 0% 98%;

    --primary: 180 100% 50%; /* #00ffff - Teal */
    --primary-foreground: 0 0% 8%;

    --secondary: 180 50% 15%; /* #0d2626 */
    --secondary-foreground: 0 0% 98%;

    --muted: 180 30% 20%; /* #1f3333 */
    --muted-foreground: 0 0% 85%;

    --accent: 180 80% 40%; /* #0d9488 - Darker Teal */
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84% 60%; /* #ef4444 */
    --destructive-foreground: 0 0% 98%;

    --border: 180 30% 25%; /* #264040 */
    --input: 180 30% 25%;
    --ring: 180 100% 50%;

    --radius: 0.75rem;

    /* Professional Glow Variables */
    --glow-primary: 0 0 20px hsl(180 100% 50% / 0.5);
    --glow-accent: 0 0 20px hsl(180 80% 40% / 0.5);
    --glow-success: 0 0 20px hsl(142 76% 36% / 0.5);
    --glow-warning: 0 0 20px hsl(38 92% 50% / 0.5);
    --glow-danger: 0 0 20px hsl(0 84% 60% / 0.5);
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    background: radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(13, 148, 136, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.05) 0%, transparent 50%),
      linear-gradient(135deg, #0a0f0f 0%, #131619 50%, #0a0f0f 100%);
    min-height: 100vh;
    background-attachment: fixed;
  }
}

/* Professional Interactive Effects */
@layer components {
  .glow-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
  }

  .glow-hover::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
      rgba(0, 255, 255, 0.1) 0%,
      transparent 50%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 1;
  }

  .glow-hover:hover::before {
    opacity: 1;
  }

  .glow-hover:hover {
    box-shadow: var(--glow-primary);
    transform: translateY(-2px);
    border-color: hsl(180 100% 50% / 0.5);
  }

  /* Professional Card Effects */
  .card-glow {
    @apply glow-hover border-border/50 bg-card/50 backdrop-blur-sm;
    background: rgba(26, 31, 35, 0.8);
    border: 1px solid rgba(0, 255, 255, 0.1);
    backdrop-filter: blur(10px);
  }

  .card-glow:hover {
    @apply border-primary/50;
    box-shadow: var(--glow-primary), 0 10px 25px -5px rgba(0, 0, 0, 0.3);
    background: rgba(26, 31, 35, 0.9);
  }

  /* Professional Button Variants */
  .btn-glow-primary {
    @apply bg-primary text-primary-foreground glow-hover;
    background: linear-gradient(135deg, #00ffff 0%, #0d9488 100%);
    box-shadow: 0 4px 15px rgba(0, 255, 255, 0.2);
  }

  .btn-glow-primary:hover {
    box-shadow: 0 6px 20px rgba(0, 255, 255, 0.4);
    transform: translateY(-2px);
  }

  .btn-glow-accent {
    @apply bg-accent text-accent-foreground glow-hover;
    background: linear-gradient(135deg, #0d9488 0%, #0f766e 100%);
    box-shadow: 0 4px 15px rgba(13, 148, 136, 0.2);
  }

  .btn-glow-accent:hover {
    box-shadow: 0 6px 20px rgba(13, 148, 136, 0.4);
    transform: translateY(-2px);
  }

  /* Professional Grid Pattern */
  .grid-pattern {
    background-image: linear-gradient(rgba(0, 255, 255, 0.03) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 255, 255, 0.03) 1px, transparent 1px);
    background-size: 50px 50px;
  }

  /* Medical Visualization Styles */
  .medical-glow {
    filter: drop-shadow(0 0 8px rgba(0, 255, 255, 0.6));
  }

  .pulse-medical {
    animation: pulse-medical 2s ease-in-out infinite;
  }

  @keyframes pulse-medical {
    0%,
    100% {
      opacity: 0.8;
      transform: scale(1);
    }
    50% {
      opacity: 1;
      transform: scale(1.05);
    }
  }
}

/* Professional Animations */
@layer utilities {
  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  @keyframes glow {
    from {
      box-shadow: 0 0 5px hsl(180 100% 50% / 0.2);
    }
    to {
      box-shadow: 0 0 20px hsl(180 100% 50% / 0.6);
    }
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-15px);
    }
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse-glow {
    0%,
    100% {
      box-shadow: 0 0 5px hsl(180 100% 50% / 0.4);
    }
    50% {
      box-shadow: 0 0 20px hsl(180 100% 50% / 0.8), 0 0 30px hsl(180 100% 50% / 0.4);
    }
  }

  .animate-slide-up {
    animation: slide-up 0.6s ease-out;
  }

  @keyframes slide-up {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in-up {
    animation: fade-in-up 0.8s ease-out;
  }

  @keyframes fade-in-up {
    from {
      opacity: 0;
      transform: translateY(40px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in-right {
    animation: fade-in-right 0.8s ease-out;
  }

  @keyframes fade-in-right {
    from {
      opacity: 0;
      transform: translateX(40px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
}

/* Professional Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(26, 31, 35, 0.5);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #00ffff 0%, #0d9488 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #0d9488 0%, #0f766e 100%);
}

/* Selection */
::selection {
  background: rgba(0, 255, 255, 0.3);
  color: white;
}

/* Focus styles */
*:focus {
  outline: none;
  box-shadow: 0 0 0 2px hsl(180 100% 50% / 0.5);
}

/* Professional Loading States */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.0) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Responsive Design Improvements */
@media (max-width: 640px) {
  .card-glow {
    margin: 0.5rem;
  }

  .btn-glow-primary,
  .btn-glow-accent {
    font-size: 0.875rem;
    padding: 0.75rem 1.5rem;
  }
}

/* Fix overlapping issues */
.relative {
  position: relative;
  z-index: 1;
}

.absolute {
  z-index: 2;
}

/* Ensure proper spacing */
.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-8 > * + * {
  margin-top: 2rem;
}

/* Professional container spacing */
.container-spacing {
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-spacing {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-spacing {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}
