# QuantNex.ai - AI-Powered Medical Diagnosis Platform

*Advanced quantum-enhanced medical imaging and diagnosis system*

[![Deployed on Vercel](https://img.shields.io/badge/Deployed%20on-Vercel-black?style=for-the-badge&logo=vercel)](https://quant-nex-git-master-abhijeetswami077gmailcoms-projects.vercel.app)
[![Built with Next.js](https://img.shields.io/badge/Built%20with-Next.js-black?style=for-the-badge&logo=next.js)](https://nextjs.org)

## Overview

QuantNex.ai is a cutting-edge medical diagnosis platform that leverages AI and quantum computing principles to provide accurate medical imaging analysis, patient monitoring, and treatment recommendations. This repository contains the full-stack Next.js application with integrated Firebase authentication and Neon database.

## 🚀 Live Deployment

Your project is live at:

**[https://quant-nex-git-master-abhijeetswami077gmailcoms-projects.vercel.app](https://quant-nex-git-master-ab<PERSON><PERSON><PERSON>wami077gmailcoms-projects.vercel.app)**

## 🛠️ Local Development Setup

1. **Clone the repository:**
   ```bash
   git clone https://github.com/Abhijeet-077/quant-nex.ai.git
   cd quant-nex.ai
   ```

2. **Install dependencies:**
   ```bash
   pnpm install
   ```

3. **Set up environment variables:**
   - Copy `.env.example` to `.env.local`
   - Fill in your Firebase and Neon database credentials

4. **Run the development server:**
   ```bash
   pnpm dev
   ```

5. **Open your browser:**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🔄 Automatic Deployment Workflow

This project is configured for automatic deployment:

1. Make changes locally and test them
2. Commit and push changes to GitHub
3. Vercel automatically detects changes and deploys
4. Your live site updates within minutes

## 🏗️ Tech Stack

- **Frontend:** Next.js 15, React 19, TypeScript
- **Styling:** Tailwind CSS, Radix UI Components
- **Authentication:** Firebase Auth
- **Database:** Neon PostgreSQL
- **Deployment:** Vercel
- **Package Manager:** pnpm
