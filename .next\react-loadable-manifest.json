{"lib\\indian-backend-service.ts -> ./firebase": {"id": "lib\\indian-backend-service.ts -> ./firebase", "files": ["static/chunks/_app-pages-browser_lib_firebase_ts.js"]}, "lib\\indian-backend-service.ts -> firebase/auth": {"id": "lib\\indian-backend-service.ts -> firebase/auth", "files": ["static/chunks/_app-pages-browser_node_modules_pnpm_firebase_11_10_0_node_modules_firebase_auth_dist_esm_ind-15c111.js"]}, "node_modules\\.pnpm\\next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts": {"id": "node_modules\\.pnpm\\next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts", "files": ["static/chunks/_app-pages-browser_node_modules_pnpm_next_15_2_4__babel_core_7_2_af462da0b44a64ee4ac7c62cb6f2-d6fcd5.js"]}}