/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/login/page";
exports.ids = ["app/login/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fd9c2a4e3ff9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWJoaWpcXERvd25sb2Fkc1xcUXVhbnRuZXguYWlcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJmZDljMmE0ZTNmZjlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/auth-context */ \"(rsc)/./contexts/auth-context.tsx\");\n\n\n\n\nconst viewport = {\n    width: 'device-width',\n    initialScale: 1,\n    themeColor: '#0d9488'\n};\nconst metadata = {\n    title: \"Quant-NEX - AI-Powered Medical Diagnosis Platform\",\n    description: \"Revolutionary AI-powered platform for brain tumor diagnosis, treatment planning, and patient monitoring. Trusted by healthcare professionals worldwide.\",\n    keywords: \"AI medical diagnosis, brain tumor detection, healthcare technology, medical AI, patient monitoring, treatment planning\",\n    authors: [\n        {\n            name: \"Quant-NEX Team\"\n        }\n    ],\n    robots: \"index, follow\",\n    metadataBase: new URL('https://quant-nex-git-master-abhijeetswami077gmailcoms-projects.vercel.app'),\n    openGraph: {\n        title: \"Quant-NEX - AI-Powered Medical Diagnosis Platform\",\n        description: \"Revolutionary AI-powered platform for brain tumor diagnosis, treatment planning, and patient monitoring.\",\n        type: \"website\",\n        url: \"https://quantnex.com\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"Quant-NEX AI Medical Platform\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Quant-NEX - AI-Powered Medical Diagnosis Platform\",\n        description: \"Revolutionary AI-powered platform for brain tumor diagnosis, treatment planning, and patient monitoring.\",\n        images: [\n            \"/og-image.jpg\"\n        ]\n    },\n    generator: 'v0.dev'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\app\\\\layout.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\app\\\\layout.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\app\\\\layout.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} antialiased`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\app\\\\layout.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\app\\\\layout.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\app\\\\layout.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBTU1BO0FBSGdCO0FBQ2dDO0FBSS9DLE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGNBQWM7SUFDZEMsWUFBWTtBQUNkLEVBQUM7QUFFTSxNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUNFO0lBQ0ZDLFVBQ0U7SUFDRkMsU0FBUztRQUFDO1lBQUVDLE1BQU07UUFBaUI7S0FBRTtJQUNyQ0MsUUFBUTtJQUNSQyxjQUFjLElBQUlDLElBQUk7SUFDdEJDLFdBQVc7UUFDVFIsT0FBTztRQUNQQyxhQUNFO1FBQ0ZRLE1BQU07UUFDTkMsS0FBSztRQUNMQyxRQUFRO1lBQ047Z0JBQ0VELEtBQUs7Z0JBQ0xkLE9BQU87Z0JBQ1BnQixRQUFRO2dCQUNSQyxLQUFLO1lBQ1A7U0FDRDtJQUNIO0lBQ0FDLFNBQVM7UUFDUEMsTUFBTTtRQUNOZixPQUFPO1FBQ1BDLGFBQ0U7UUFDRlUsUUFBUTtZQUFDO1NBQWdCO0lBQzNCO0lBQ0VLLFdBQVc7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0MsV0FBVTs7MEJBQ3hCLDhEQUFDQzs7a0NBQ0MsOERBQUNDO3dCQUFLQyxLQUFJO3dCQUFPQyxNQUFLOzs7Ozs7a0NBQ3RCLDhEQUFDRjt3QkFBS0MsS0FBSTt3QkFBbUJDLE1BQUs7Ozs7Ozs7Ozs7OzswQkFFcEMsOERBQUNDO2dCQUFLTCxXQUFXLEdBQUc1QiwySkFBZSxDQUFDLFlBQVksQ0FBQzswQkFDL0MsNEVBQUNDLGdFQUFZQTs4QkFBRXdCOzs7Ozs7Ozs7Ozs7Ozs7OztBQUl2QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBYmhpalxcRG93bmxvYWRzXFxRdWFudG5leC5haVxcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIFJlYWN0IGZyb20gXCJyZWFjdFwiXHJcbmltcG9ydCB0eXBlIHsgTWV0YWRhdGEsIFZpZXdwb3J0IH0gZnJvbSBcIm5leHRcIlxyXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCJcclxuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiXHJcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gXCJAL2NvbnRleHRzL2F1dGgtY29udGV4dFwiXHJcblxyXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogW1wibGF0aW5cIl0gfSlcclxuXHJcbmV4cG9ydCBjb25zdCB2aWV3cG9ydDogVmlld3BvcnQgPSB7XHJcbiAgd2lkdGg6ICdkZXZpY2Utd2lkdGgnLFxyXG4gIGluaXRpYWxTY2FsZTogMSxcclxuICB0aGVtZUNvbG9yOiAnIzBkOTQ4OCcsXHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XHJcbiAgdGl0bGU6IFwiUXVhbnQtTkVYIC0gQUktUG93ZXJlZCBNZWRpY2FsIERpYWdub3NpcyBQbGF0Zm9ybVwiLFxyXG4gIGRlc2NyaXB0aW9uOlxyXG4gICAgXCJSZXZvbHV0aW9uYXJ5IEFJLXBvd2VyZWQgcGxhdGZvcm0gZm9yIGJyYWluIHR1bW9yIGRpYWdub3NpcywgdHJlYXRtZW50IHBsYW5uaW5nLCBhbmQgcGF0aWVudCBtb25pdG9yaW5nLiBUcnVzdGVkIGJ5IGhlYWx0aGNhcmUgcHJvZmVzc2lvbmFscyB3b3JsZHdpZGUuXCIsXHJcbiAga2V5d29yZHM6XHJcbiAgICBcIkFJIG1lZGljYWwgZGlhZ25vc2lzLCBicmFpbiB0dW1vciBkZXRlY3Rpb24sIGhlYWx0aGNhcmUgdGVjaG5vbG9neSwgbWVkaWNhbCBBSSwgcGF0aWVudCBtb25pdG9yaW5nLCB0cmVhdG1lbnQgcGxhbm5pbmdcIixcclxuICBhdXRob3JzOiBbeyBuYW1lOiBcIlF1YW50LU5FWCBUZWFtXCIgfV0sXHJcbiAgcm9ib3RzOiBcImluZGV4LCBmb2xsb3dcIixcclxuICBtZXRhZGF0YUJhc2U6IG5ldyBVUkwoJ2h0dHBzOi8vcXVhbnQtbmV4LWdpdC1tYXN0ZXItYWJoaWplZXRzd2FtaTA3N2dtYWlsY29tcy1wcm9qZWN0cy52ZXJjZWwuYXBwJyksXHJcbiAgb3BlbkdyYXBoOiB7XHJcbiAgICB0aXRsZTogXCJRdWFudC1ORVggLSBBSS1Qb3dlcmVkIE1lZGljYWwgRGlhZ25vc2lzIFBsYXRmb3JtXCIsXHJcbiAgICBkZXNjcmlwdGlvbjpcclxuICAgICAgXCJSZXZvbHV0aW9uYXJ5IEFJLXBvd2VyZWQgcGxhdGZvcm0gZm9yIGJyYWluIHR1bW9yIGRpYWdub3NpcywgdHJlYXRtZW50IHBsYW5uaW5nLCBhbmQgcGF0aWVudCBtb25pdG9yaW5nLlwiLFxyXG4gICAgdHlwZTogXCJ3ZWJzaXRlXCIsXHJcbiAgICB1cmw6IFwiaHR0cHM6Ly9xdWFudG5leC5jb21cIixcclxuICAgIGltYWdlczogW1xyXG4gICAgICB7XHJcbiAgICAgICAgdXJsOiBcIi9vZy1pbWFnZS5qcGdcIixcclxuICAgICAgICB3aWR0aDogMTIwMCxcclxuICAgICAgICBoZWlnaHQ6IDYzMCxcclxuICAgICAgICBhbHQ6IFwiUXVhbnQtTkVYIEFJIE1lZGljYWwgUGxhdGZvcm1cIixcclxuICAgICAgfSxcclxuICAgIF0sXHJcbiAgfSxcclxuICB0d2l0dGVyOiB7XHJcbiAgICBjYXJkOiBcInN1bW1hcnlfbGFyZ2VfaW1hZ2VcIixcclxuICAgIHRpdGxlOiBcIlF1YW50LU5FWCAtIEFJLVBvd2VyZWQgTWVkaWNhbCBEaWFnbm9zaXMgUGxhdGZvcm1cIixcclxuICAgIGRlc2NyaXB0aW9uOlxyXG4gICAgICBcIlJldm9sdXRpb25hcnkgQUktcG93ZXJlZCBwbGF0Zm9ybSBmb3IgYnJhaW4gdHVtb3IgZGlhZ25vc2lzLCB0cmVhdG1lbnQgcGxhbm5pbmcsIGFuZCBwYXRpZW50IG1vbml0b3JpbmcuXCIsXHJcbiAgICBpbWFnZXM6IFtcIi9vZy1pbWFnZS5qcGdcIl0sXHJcbiAgfSxcclxuICAgIGdlbmVyYXRvcjogJ3YwLmRldidcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XHJcbiAgY2hpbGRyZW4sXHJcbn06IHtcclxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXHJcbn0pIHtcclxuICByZXR1cm4gKFxyXG4gICAgPGh0bWwgbGFuZz1cImVuXCIgY2xhc3NOYW1lPVwiZGFya1wiPlxyXG4gICAgICA8aGVhZD5cclxuICAgICAgICA8bGluayByZWw9XCJpY29uXCIgaHJlZj1cIi9mYXZpY29uLmljb1wiIC8+XHJcbiAgICAgICAgPGxpbmsgcmVsPVwiYXBwbGUtdG91Y2gtaWNvblwiIGhyZWY9XCIvYXBwbGUtdG91Y2gtaWNvbi5wbmdcIiAvPlxyXG4gICAgICA8L2hlYWQ+XHJcbiAgICAgIDxib2R5IGNsYXNzTmFtZT17YCR7aW50ZXIuY2xhc3NOYW1lfSBhbnRpYWxpYXNlZGB9PlxyXG4gICAgICAgIDxBdXRoUHJvdmlkZXI+e2NoaWxkcmVufTwvQXV0aFByb3ZpZGVyPlxyXG4gICAgICA8L2JvZHk+XHJcbiAgICA8L2h0bWw+XHJcbiAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJpbnRlciIsIkF1dGhQcm92aWRlciIsInZpZXdwb3J0Iiwid2lkdGgiLCJpbml0aWFsU2NhbGUiLCJ0aGVtZUNvbG9yIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwia2V5d29yZHMiLCJhdXRob3JzIiwibmFtZSIsInJvYm90cyIsIm1ldGFkYXRhQmFzZSIsIlVSTCIsIm9wZW5HcmFwaCIsInR5cGUiLCJ1cmwiLCJpbWFnZXMiLCJoZWlnaHQiLCJhbHQiLCJ0d2l0dGVyIiwiY2FyZCIsImdlbmVyYXRvciIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiY2xhc3NOYW1lIiwiaGVhZCIsImxpbmsiLCJyZWwiLCJocmVmIiwiYm9keSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/loading.tsx":
/*!*************************!*\
  !*** ./app/loading.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_page_loading__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/page-loading */ \"(rsc)/./components/ui/page-loading.tsx\");\n\n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_loading__WEBPACK_IMPORTED_MODULE_1__.PageLoading, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\app\\\\loading.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbG9hZGluZy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBMEQ7QUFFM0MsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELG9FQUFXQTs7Ozs7QUFDckIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWJoaWpcXERvd25sb2Fkc1xcUXVhbnRuZXguYWlcXGFwcFxcbG9hZGluZy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZUxvYWRpbmcgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3BhZ2UtbG9hZGluZ1wiXHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2FkaW5nKCkge1xyXG4gIHJldHVybiA8UGFnZUxvYWRpbmcgLz5cclxufVxyXG4iXSwibmFtZXMiOlsiUGFnZUxvYWRpbmciLCJMb2FkaW5nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./app/login/loading.tsx":
/*!*******************************!*\
  !*** ./app/login/loading.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_page_loading__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/page-loading */ \"(rsc)/./components/ui/page-loading.tsx\");\n\n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_loading__WEBPACK_IMPORTED_MODULE_1__.PageLoading, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\app\\\\login\\\\loading.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbG9naW4vbG9hZGluZy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBMEQ7QUFFM0MsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELG9FQUFXQTs7Ozs7QUFDckIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWJoaWpcXERvd25sb2Fkc1xcUXVhbnRuZXguYWlcXGFwcFxcbG9naW5cXGxvYWRpbmcudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBhZ2VMb2FkaW5nIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9wYWdlLWxvYWRpbmdcIlxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9hZGluZygpIHtcclxuICByZXR1cm4gPFBhZ2VMb2FkaW5nIC8+XHJcbn1cclxuIl0sIm5hbWVzIjpbIlBhZ2VMb2FkaW5nIiwiTG9hZGluZyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/login/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./app/login/page.tsx":
/*!****************************!*\
  !*** ./app/login/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Downloads\\Quantnex.ai\\app\\login\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/ui/loading-spinner.tsx":
/*!*******************************************!*\
  !*** ./components/ui/loading-spinner.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingSpinner: () => (/* binding */ LoadingSpinner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(rsc)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n\n\nfunction LoadingSpinner({ size = \"default\", text = \"Loading...\" }) {\n    const sizeClasses = {\n        sm: \"h-4 w-4\",\n        default: \"h-8 w-8\",\n        lg: \"h-12 w-12\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center p-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                className: `${sizeClasses[size]} animate-spin text-purple-500`\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\loading-spinner.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-4 text-gray-400\",\n                children: text\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\loading-spinner.tsx\",\n                lineNumber: 16,\n                columnNumber: 16\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\loading-spinner.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9jb21wb25lbnRzL3VpL2xvYWRpbmctc3Bpbm5lci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBc0M7QUFFL0IsU0FBU0MsZUFBZSxFQUM3QkMsT0FBTyxTQUFTLEVBQ2hCQyxPQUFPLFlBQVksRUFDK0I7SUFDbEQsTUFBTUMsY0FBYztRQUNsQkMsSUFBSTtRQUNKQyxTQUFTO1FBQ1RDLElBQUk7SUFDTjtJQUVBLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ1QsbUZBQU9BO2dCQUFDUyxXQUFXLEdBQUdMLFdBQVcsQ0FBQ0YsS0FBSyxDQUFDLDZCQUE2QixDQUFDOzs7Ozs7WUFDdEVDLHNCQUFRLDhEQUFDTztnQkFBRUQsV0FBVTswQkFBc0JOOzs7Ozs7Ozs7Ozs7QUFHbEQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWJoaWpcXERvd25sb2Fkc1xcUXVhbnRuZXguYWlcXGNvbXBvbmVudHNcXHVpXFxsb2FkaW5nLXNwaW5uZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IExvYWRlcjIgfSBmcm9tIFwibHVjaWRlLXJlYWN0XCJcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBMb2FkaW5nU3Bpbm5lcih7XHJcbiAgc2l6ZSA9IFwiZGVmYXVsdFwiLFxyXG4gIHRleHQgPSBcIkxvYWRpbmcuLi5cIixcclxufTogeyBzaXplPzogXCJzbVwiIHwgXCJkZWZhdWx0XCIgfCBcImxnXCI7IHRleHQ/OiBzdHJpbmcgfSkge1xyXG4gIGNvbnN0IHNpemVDbGFzc2VzID0ge1xyXG4gICAgc206IFwiaC00IHctNFwiLFxyXG4gICAgZGVmYXVsdDogXCJoLTggdy04XCIsXHJcbiAgICBsZzogXCJoLTEyIHctMTJcIixcclxuICB9XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHAtOFwiPlxyXG4gICAgICA8TG9hZGVyMiBjbGFzc05hbWU9e2Ake3NpemVDbGFzc2VzW3NpemVdfSBhbmltYXRlLXNwaW4gdGV4dC1wdXJwbGUtNTAwYH0gLz5cclxuICAgICAge3RleHQgJiYgPHAgY2xhc3NOYW1lPVwibXQtNCB0ZXh0LWdyYXktNDAwXCI+e3RleHR9PC9wPn1cclxuICAgIDwvZGl2PlxyXG4gIClcclxufVxyXG4iXSwibmFtZXMiOlsiTG9hZGVyMiIsIkxvYWRpbmdTcGlubmVyIiwic2l6ZSIsInRleHQiLCJzaXplQ2xhc3NlcyIsInNtIiwiZGVmYXVsdCIsImxnIiwiZGl2IiwiY2xhc3NOYW1lIiwicCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./components/ui/loading-spinner.tsx\n");

/***/ }),

/***/ "(rsc)/./components/ui/page-loading.tsx":
/*!****************************************!*\
  !*** ./components/ui/page-loading.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PageLoading: () => (/* binding */ PageLoading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _loading_spinner__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./loading-spinner */ \"(rsc)/./components/ui/loading-spinner.tsx\");\n\n\nfunction PageLoading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-black/80 p-8 rounded-xl border border-white/10 shadow-xl\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_loading_spinner__WEBPACK_IMPORTED_MODULE_1__.LoadingSpinner, {\n                size: \"lg\",\n                text: \"Loading page...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\page-loading.tsx\",\n                lineNumber: 7,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\page-loading.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\page-loading.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9jb21wb25lbnRzL3VpL3BhZ2UtbG9hZGluZy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBa0Q7QUFFM0MsU0FBU0M7SUFDZCxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0gsNERBQWNBO2dCQUFDSSxNQUFLO2dCQUFLQyxNQUFLOzs7Ozs7Ozs7Ozs7Ozs7O0FBSXZDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFiaGlqXFxEb3dubG9hZHNcXFF1YW50bmV4LmFpXFxjb21wb25lbnRzXFx1aVxccGFnZS1sb2FkaW5nLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBMb2FkaW5nU3Bpbm5lciB9IGZyb20gXCIuL2xvYWRpbmctc3Bpbm5lclwiXHJcblxyXG5leHBvcnQgZnVuY3Rpb24gUGFnZUxvYWRpbmcoKSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBiZy1ibGFjay81MCBiYWNrZHJvcC1ibHVyLXNtIHotNTBcIj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ibGFjay84MCBwLTggcm91bmRlZC14bCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIHNoYWRvdy14bFwiPlxyXG4gICAgICAgIDxMb2FkaW5nU3Bpbm5lciBzaXplPVwibGdcIiB0ZXh0PVwiTG9hZGluZyBwYWdlLi4uXCIgLz5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApXHJcbn1cclxuIl0sIm5hbWVzIjpbIkxvYWRpbmdTcGlubmVyIiwiUGFnZUxvYWRpbmciLCJkaXYiLCJjbGFzc05hbWUiLCJzaXplIiwidGV4dCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./components/ui/page-loading.tsx\n");

/***/ }),

/***/ "(rsc)/./contexts/auth-context.tsx":
/*!***********************************!*\
  !*** ./contexts/auth-context.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Downloads\\Quantnex.ai\\contexts\\auth-context.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Downloads\\Quantnex.ai\\contexts\\auth-context.tsx",
"useAuth",
);

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CAbhij%5CDownloads%5CQuantnex.ai%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbhij%5CDownloads%5CQuantnex.ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CAbhij%5CDownloads%5CQuantnex.ai%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbhij%5CDownloads%5CQuantnex.ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/module.compiled.js?5dcf\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/loading.tsx */ \"(rsc)/./app/loading.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/login/loading.tsx */ \"(rsc)/./app/login/loading.tsx\"));\nconst page6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/login/page.tsx */ \"(rsc)/./app/login/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page6, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\app\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'loading': [module5, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\app\\\\login\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\app\\\\layout.tsx\"],\n'loading': [module1, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\app\\\\loading.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\app\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CAbhij%5CDownloads%5CQuantnex.ai%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbhij%5CDownloads%5CQuantnex.ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/login/page.tsx */ \"(rsc)/./app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfQGJhYmVsK2NvcmVANy4yX2FmNDYyZGEwYjQ0YTY0ZWU0YWM3YzYyY2I2ZjIyNmQ4L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQWJoaWolNUMlNUNEb3dubG9hZHMlNUMlNUNRdWFudG5leC5haSU1QyU1Q2FwcCU1QyU1Q2xvZ2luJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9KQUFtRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQWJoaWpcXFxcRG93bmxvYWRzXFxcXFF1YW50bmV4LmFpXFxcXGFwcFxcXFxsb2dpblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/auth-context.tsx */ \"(rsc)/./contexts/auth-context.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfQGJhYmVsK2NvcmVANy4yX2FmNDYyZGEwYjQ0YTY0ZWU0YWM3YzYyY2I2ZjIyNmQ4L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQWJoaWolNUMlNUNEb3dubG9hZHMlNUMlNUNRdWFudG5leC5haSU1QyU1Q2NvbnRleHRzJTVDJTVDYXV0aC1jb250ZXh0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkF1dGhQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBYmhpaiU1QyU1Q0Rvd25sb2FkcyU1QyU1Q1F1YW50bmV4LmFpJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDLnBucG0lNUMlNUNuZXh0JTQwMTUuMi40XyU0MGJhYmVsJTJCY29yZSU0MDcuMl9hZjQ2MmRhMGI0NGE2NGVlNGFjN2M2MmNiNmYyMjZkOCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBYmhpaiU1QyU1Q0Rvd25sb2FkcyU1QyU1Q1F1YW50bmV4LmFpJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUEySSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcQWJoaWpcXFxcRG93bmxvYWRzXFxcXFF1YW50bmV4LmFpXFxcXGNvbnRleHRzXFxcXGF1dGgtY29udGV4dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \**************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/login/page.tsx":
/*!****************************!*\
  !*** ./app/login/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Login)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_auth_enhanced_indian_login__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/auth/enhanced-indian-login */ \"(ssr)/./components/auth/enhanced-indian-login.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Login() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_enhanced_indian_login__WEBPACK_IMPORTED_MODULE_1__.EnhancedIndianLogin, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvbG9naW4vcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFNkU7QUFFOUQsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELHVGQUFtQkE7Ozs7O0FBQzdCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFiaGlqXFxEb3dubG9hZHNcXFF1YW50bmV4LmFpXFxhcHBcXGxvZ2luXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxyXG5cclxuaW1wb3J0IHsgRW5oYW5jZWRJbmRpYW5Mb2dpbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvYXV0aC9lbmhhbmNlZC1pbmRpYW4tbG9naW5cIlxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9naW4oKSB7XHJcbiAgcmV0dXJuIDxFbmhhbmNlZEluZGlhbkxvZ2luIC8+XHJcbn1cclxuIl0sIm5hbWVzIjpbIkVuaGFuY2VkSW5kaWFuTG9naW4iLCJMb2dpbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/auth/enhanced-indian-login.tsx":
/*!***************************************************!*\
  !*** ./components/auth/enhanced-indian-login.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnhancedIndianLogin: () => (/* binding */ EnhancedIndianLogin)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/separator */ \"(ssr)/./components/ui/separator.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,CheckCircle,Chrome,Eye,EyeOff,Heart,Info,Lock,Mail,Shield,Stethoscope!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,CheckCircle,Chrome,Eye,EyeOff,Heart,Info,Lock,Mail,Shield,Stethoscope!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,CheckCircle,Chrome,Eye,EyeOff,Heart,Info,Lock,Mail,Shield,Stethoscope!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,CheckCircle,Chrome,Eye,EyeOff,Heart,Info,Lock,Mail,Shield,Stethoscope!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,CheckCircle,Chrome,Eye,EyeOff,Heart,Info,Lock,Mail,Shield,Stethoscope!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,CheckCircle,Chrome,Eye,EyeOff,Heart,Info,Lock,Mail,Shield,Stethoscope!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,CheckCircle,Chrome,Eye,EyeOff,Heart,Info,Lock,Mail,Shield,Stethoscope!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,CheckCircle,Chrome,Eye,EyeOff,Heart,Info,Lock,Mail,Shield,Stethoscope!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,CheckCircle,Chrome,Eye,EyeOff,Heart,Info,Lock,Mail,Shield,Stethoscope!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,CheckCircle,Chrome,Eye,EyeOff,Heart,Info,Lock,Mail,Shield,Stethoscope!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/chrome.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,CheckCircle,Chrome,Eye,EyeOff,Heart,Info,Lock,Mail,Shield,Stethoscope!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/stethoscope.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,CheckCircle,Chrome,Eye,EyeOff,Heart,Info,Lock,Mail,Shield,Stethoscope!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _lib_indian_backend_service__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/indian-backend-service */ \"(ssr)/./lib/indian-backend-service.ts\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/auth-context */ \"(ssr)/./contexts/auth-context.tsx\");\n/* __next_internal_client_entry_do_not_use__ EnhancedIndianLogin auto */ \n\n\n\n\n\n\n\n\n\n\n\n\nfunction EnhancedIndianLogin() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { login, isAuthenticated } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_11__.useAuth)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: \"\",\n        password: \"\"\n    });\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Get redirect URL from search params\n    const redirectUrl = searchParams.get('redirect') || '/dashboard';\n    // Redirect if already authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedIndianLogin.useEffect\": ()=>{\n            if (isAuthenticated) {\n                router.push(redirectUrl);\n            }\n        }\n    }[\"EnhancedIndianLogin.useEffect\"], [\n        isAuthenticated,\n        router,\n        redirectUrl\n    ]);\n    // Get demo credentials for display\n    const demoCredentials = _lib_indian_backend_service__WEBPACK_IMPORTED_MODULE_10__.indianBackendService.getDemoCredentials();\n    const isDemoMode = _lib_indian_backend_service__WEBPACK_IMPORTED_MODULE_10__.indianBackendService.isDemoModeActive();\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Clear errors when user starts typing\n        if (error) setError(\"\");\n    };\n    const handleLogin = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const response = await _lib_indian_backend_service__WEBPACK_IMPORTED_MODULE_10__.indianBackendService.login(formData);\n            if (response.success && response.user) {\n                setSuccess(`Welcome back, ${response.user.name}!`);\n                // Store user data\n                if (false) {}\n                // Update auth context\n                login(response.user);\n                // Redirect to intended page or dashboard\n                setTimeout(()=>{\n                    router.push(redirectUrl);\n                }, 1000);\n            } else {\n                setError(response.error || \"Login failed. Please try again.\");\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            setError(\"An unexpected error occurred. Please try again.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleGoogleSignIn = async ()=>{\n        setIsLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const response = await _lib_indian_backend_service__WEBPACK_IMPORTED_MODULE_10__.indianBackendService.googleSignIn();\n            if (response.success && response.user) {\n                setSuccess(`Welcome, ${response.user.name}!`);\n                // Store user data\n                if (false) {}\n                // Update auth context\n                login(response.user);\n                // Redirect to intended page or dashboard\n                setTimeout(()=>{\n                    router.push(redirectUrl);\n                }, 1000);\n            } else {\n                setError(response.error || \"Google sign-in failed. Please try again.\");\n            }\n        } catch (error) {\n            console.error(\"Google sign-in error:\", error);\n            setError(\"Google sign-in failed. Please try email/password login.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const fillDemoCredentials = ()=>{\n        setFormData({\n            email: demoCredentials.email,\n            password: demoCredentials.password\n        });\n        setError(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center p-4 grid-pattern\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-md space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center items-center gap-2 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 rounded-full bg-teal-500/20 flex items-center justify-center animate-pulse-glow\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-6 w-6 text-teal-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-teal-400\",\n                                            children: \"Quant-NEX\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-teal-300\",\n                                            children: \"Cancer Diagnosis System\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400\",\n                            children: \"Sign in to access the medical dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, this),\n                isDemoMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                    className: \"border-teal-500/30 bg-teal-900/20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"h-4 w-4 text-teal-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.AlertDescription, {\n                            className: \"text-teal-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-medium\",\n                                        children: \"Demo Mode Active\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: \"Firebase not configured. Using demo authentication.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: fillDemoCredentials,\n                                        className: \"mt-2 text-teal-400 border-teal-500/30 hover:bg-teal-500/10 bg-transparent\",\n                                        children: \"Use Demo Credentials\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"card-glow\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"text-center text-teal-400\",\n                                children: \"Welcome Back\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleLogin,\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"email\",\n                                                className: \"text-teal-300\",\n                                                children: \"Email Address\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"email\",\n                                                        name: \"email\",\n                                                        type: \"email\",\n                                                        placeholder: \"Enter your email\",\n                                                        value: formData.email,\n                                                        onChange: handleInputChange,\n                                                        className: \"pl-10 bg-teal-900/20 border-teal-500/30 focus:border-teal-400\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"password\",\n                                                className: \"text-teal-300\",\n                                                children: \"Password\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"password\",\n                                                        name: \"password\",\n                                                        type: showPassword ? \"text\" : \"password\",\n                                                        placeholder: \"Enter your password\",\n                                                        value: formData.password,\n                                                        onChange: handleInputChange,\n                                                        className: \"pl-10 pr-10 bg-teal-900/20 border-teal-500/30 focus:border-teal-400\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        className: \"absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0\",\n                                                        onClick: ()=>setShowPassword(!showPassword),\n                                                        children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                                        className: \"border-red-500/30 bg-red-900/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 text-red-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.AlertDescription, {\n                                                className: \"text-red-300 whitespace-pre-line\",\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 17\n                                    }, this),\n                                    success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                                        className: \"border-green-500/30 bg-green-900/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-4 w-4 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.AlertDescription, {\n                                                className: \"text-green-300\",\n                                                children: success\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        type: \"submit\",\n                                        className: \"w-full btn-glow-primary\",\n                                        disabled: isLoading,\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Signing In...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Sign In\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {\n                                                className: \"bg-teal-500/30\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-background px-2 text-sm text-gray-400\",\n                                                children: \"or\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        type: \"button\",\n                                        variant: \"outline\",\n                                        className: \"w-full glow-hover bg-transparent border-teal-500/30\",\n                                        onClick: handleGoogleSignIn,\n                                        disabled: isLoading,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Continue with Google\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 9\n                }, this),\n                isDemoMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"card-glow\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"text-sm text-teal-400 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Demo Accounts Available\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: \"Dr. Priya Patel (Oncologist)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"text-xs\",\n                                                    children: \"Tata Memorial\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"<EMAIL> / demo123\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: \"Dr. Amit Gupta (Surgeon)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"text-xs\",\n                                                    children: \"AIIMS Delhi\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"<EMAIL> / demo123\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-300\",\n                                                    children: \"Rajesh Kumar (Admin)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"text-xs\",\n                                                    children: \"Apollo Hospital\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"<EMAIL> / demo123\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-3 gap-4 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 rounded-full bg-teal-500/20 flex items-center justify-center mx-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-5 w-5 text-teal-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"AI Diagnosis\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 rounded-full bg-teal-500/20 flex items-center justify-center mx-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"h-5 w-5 text-teal-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Patient Care\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 rounded-full bg-teal-500/20 flex items-center justify-center mx-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_CheckCircle_Chrome_Eye_EyeOff_Heart_Info_Lock_Mail_Shield_Stethoscope_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-5 w-5 text-teal-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Secure Access\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                    lineNumber: 348,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center text-xs text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"\\xa9 2024 Quant-NEX. Advanced Cancer Diagnosis System.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1\",\n                            children: \"Powered by AI • Trusted by Indian Hospitals\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n                    lineNumber: 370,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n            lineNumber: 154,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\auth\\\\enhanced-indian-login.tsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL2F1dGgvZW5oYW5jZWQtaW5kaWFuLWxvZ2luLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSTJDO0FBQ2lCO0FBQ21CO0FBQ2hDO0FBQ0Y7QUFDQTtBQUNrQjtBQUNsQjtBQUNRO0FBY2hDO0FBQzhDO0FBQ2xCO0FBRTFDLFNBQVM2QjtJQUNkLE1BQU1DLFNBQVM1QiwwREFBU0E7SUFDeEIsTUFBTTZCLGVBQWU1QixnRUFBZUE7SUFDcEMsTUFBTSxFQUFFNkIsS0FBSyxFQUFFQyxlQUFlLEVBQUUsR0FBR0wsZ0VBQU9BO0lBRTFDLE1BQU0sQ0FBQ00sVUFBVUMsWUFBWSxHQUFHbkMsK0NBQVFBLENBQUM7UUFDdkNvQyxPQUFPO1FBQ1BDLFVBQVU7SUFDWjtJQUNBLE1BQU0sQ0FBQ0MsY0FBY0MsZ0JBQWdCLEdBQUd2QywrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUN3QyxXQUFXQyxhQUFhLEdBQUd6QywrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUMwQyxPQUFPQyxTQUFTLEdBQUczQywrQ0FBUUEsQ0FBQztJQUNuQyxNQUFNLENBQUM0QyxTQUFTQyxXQUFXLEdBQUc3QywrQ0FBUUEsQ0FBQztJQUV2QyxzQ0FBc0M7SUFDdEMsTUFBTThDLGNBQWNmLGFBQWFnQixHQUFHLENBQUMsZUFBZTtJQUVwRCxvQ0FBb0M7SUFDcEM5QyxnREFBU0E7eUNBQUM7WUFDUixJQUFJZ0MsaUJBQWlCO2dCQUNuQkgsT0FBT2tCLElBQUksQ0FBQ0Y7WUFDZDtRQUNGO3dDQUFHO1FBQUNiO1FBQWlCSDtRQUFRZ0I7S0FBWTtJQUV6QyxtQ0FBbUM7SUFDbkMsTUFBTUcsa0JBQWtCdEIsOEVBQW9CQSxDQUFDdUIsa0JBQWtCO0lBQy9ELE1BQU1DLGFBQWF4Qiw4RUFBb0JBLENBQUN5QixnQkFBZ0I7SUFFeEQsTUFBTUMsb0JBQW9CLENBQUNDO1FBQ3pCLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBR0YsRUFBRUcsTUFBTTtRQUNoQ3RCLFlBQVksQ0FBQ3VCLE9BQVU7Z0JBQ3JCLEdBQUdBLElBQUk7Z0JBQ1AsQ0FBQ0gsS0FBSyxFQUFFQztZQUNWO1FBQ0EsdUNBQXVDO1FBQ3ZDLElBQUlkLE9BQU9DLFNBQVM7SUFDdEI7SUFFQSxNQUFNZ0IsY0FBYyxPQUFPTDtRQUN6QkEsRUFBRU0sY0FBYztRQUNoQm5CLGFBQWE7UUFDYkUsU0FBUztRQUNURSxXQUFXO1FBRVgsSUFBSTtZQUNGLE1BQU1nQixXQUFXLE1BQU1sQyw4RUFBb0JBLENBQUNLLEtBQUssQ0FBQ0U7WUFFbEQsSUFBSTJCLFNBQVNqQixPQUFPLElBQUlpQixTQUFTQyxJQUFJLEVBQUU7Z0JBQ3JDakIsV0FBVyxDQUFDLGNBQWMsRUFBRWdCLFNBQVNDLElBQUksQ0FBQ1AsSUFBSSxDQUFDLENBQUMsQ0FBQztnQkFFakQsa0JBQWtCO2dCQUNsQixJQUFJLEtBQTZCLEVBQUUsRUFLbEM7Z0JBRUQsc0JBQXNCO2dCQUN0QnZCLE1BQU02QixTQUFTQyxJQUFJO2dCQUVuQix5Q0FBeUM7Z0JBQ3pDTSxXQUFXO29CQUNUdEMsT0FBT2tCLElBQUksQ0FBQ0Y7Z0JBQ2QsR0FBRztZQUNMLE9BQU87Z0JBQ0xILFNBQVNrQixTQUFTbkIsS0FBSyxJQUFJO1lBQzdCO1FBQ0YsRUFBRSxPQUFPQSxPQUFZO1lBQ25CMkIsUUFBUTNCLEtBQUssQ0FBQyxnQkFBZ0JBO1lBQzlCQyxTQUFTO1FBQ1gsU0FBVTtZQUNSRixhQUFhO1FBQ2Y7SUFDRjtJQUVBLE1BQU02QixxQkFBcUI7UUFDekI3QixhQUFhO1FBQ2JFLFNBQVM7UUFDVEUsV0FBVztRQUVYLElBQUk7WUFDRixNQUFNZ0IsV0FBVyxNQUFNbEMsOEVBQW9CQSxDQUFDNEMsWUFBWTtZQUV4RCxJQUFJVixTQUFTakIsT0FBTyxJQUFJaUIsU0FBU0MsSUFBSSxFQUFFO2dCQUNyQ2pCLFdBQVcsQ0FBQyxTQUFTLEVBQUVnQixTQUFTQyxJQUFJLENBQUNQLElBQUksQ0FBQyxDQUFDLENBQUM7Z0JBRTVDLGtCQUFrQjtnQkFDbEIsSUFBSSxLQUE2QixFQUFFLEVBS2xDO2dCQUVELHNCQUFzQjtnQkFDdEJ2QixNQUFNNkIsU0FBU0MsSUFBSTtnQkFFbkIseUNBQXlDO2dCQUN6Q00sV0FBVztvQkFDVHRDLE9BQU9rQixJQUFJLENBQUNGO2dCQUNkLEdBQUc7WUFDTCxPQUFPO2dCQUNMSCxTQUFTa0IsU0FBU25CLEtBQUssSUFBSTtZQUM3QjtRQUNGLEVBQUUsT0FBT0EsT0FBWTtZQUNuQjJCLFFBQVEzQixLQUFLLENBQUMseUJBQXlCQTtZQUN2Q0MsU0FBUztRQUNYLFNBQVU7WUFDUkYsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxNQUFNK0Isc0JBQXNCO1FBQzFCckMsWUFBWTtZQUNWQyxPQUFPYSxnQkFBZ0JiLEtBQUs7WUFDNUJDLFVBQVVZLGdCQUFnQlosUUFBUTtRQUNwQztRQUNBTSxTQUFTO0lBQ1g7SUFFQSxxQkFDRSw4REFBQzhCO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7OzhCQUViLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNwRCxvS0FBS0E7d0NBQUNvRCxXQUFVOzs7Ozs7Ozs7Ozs4Q0FFbkIsOERBQUNEOztzREFDQyw4REFBQ0U7NENBQUdELFdBQVU7c0RBQW1DOzs7Ozs7c0RBQ2pELDhEQUFDRTs0Q0FBRUYsV0FBVTtzREFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FHekMsOERBQUNFOzRCQUFFRixXQUFVO3NDQUFnQjs7Ozs7Ozs7Ozs7O2dCQUk5QnZCLDRCQUNDLDhEQUFDeEMsdURBQUtBO29CQUFDK0QsV0FBVTs7c0NBQ2YsOERBQUNqRCxvS0FBSUE7NEJBQUNpRCxXQUFVOzs7Ozs7c0NBQ2hCLDhEQUFDOUQsa0VBQWdCQTs0QkFBQzhELFdBQVU7c0NBQzFCLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNFO3dDQUFFRixXQUFVO2tEQUFjOzs7Ozs7a0RBQzNCLDhEQUFDRTt3Q0FBRUYsV0FBVTtrREFBVTs7Ozs7O2tEQUN2Qiw4REFBQ2xFLHlEQUFNQTt3Q0FDTHFFLFNBQVE7d0NBQ1JDLE1BQUs7d0NBQ0xDLFNBQVNQO3dDQUNURSxXQUFVO2tEQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFTVCw4REFBQ3RFLHFEQUFJQTtvQkFBQ3NFLFdBQVU7O3NDQUNkLDhEQUFDcEUsMkRBQVVBO3NDQUNULDRFQUFDQywwREFBU0E7Z0NBQUNtRSxXQUFVOzBDQUE0Qjs7Ozs7Ozs7Ozs7c0NBRW5ELDhEQUFDckUsNERBQVdBO3NDQUNWLDRFQUFDMkU7Z0NBQUtDLFVBQVV0QjtnQ0FBYWUsV0FBVTs7a0RBRXJDLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNoRSx1REFBS0E7Z0RBQUN3RSxTQUFRO2dEQUFRUixXQUFVOzBEQUFnQjs7Ozs7OzBEQUdqRCw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDekQsb0tBQUlBO3dEQUFDeUQsV0FBVTs7Ozs7O2tFQUNoQiw4REFBQ2pFLHVEQUFLQTt3REFDSjBFLElBQUc7d0RBQ0g1QixNQUFLO3dEQUNMNkIsTUFBSzt3REFDTEMsYUFBWTt3REFDWjdCLE9BQU90QixTQUFTRSxLQUFLO3dEQUNyQmtELFVBQVVqQzt3REFDVnFCLFdBQVU7d0RBQ1ZhLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFNZCw4REFBQ2Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDaEUsdURBQUtBO2dEQUFDd0UsU0FBUTtnREFBV1IsV0FBVTswREFBZ0I7Ozs7OzswREFHcEQsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ3hELG9LQUFJQTt3REFBQ3dELFdBQVU7Ozs7OztrRUFDaEIsOERBQUNqRSx1REFBS0E7d0RBQ0owRSxJQUFHO3dEQUNINUIsTUFBSzt3REFDTDZCLE1BQU05QyxlQUFlLFNBQVM7d0RBQzlCK0MsYUFBWTt3REFDWjdCLE9BQU90QixTQUFTRyxRQUFRO3dEQUN4QmlELFVBQVVqQzt3REFDVnFCLFdBQVU7d0RBQ1ZhLFFBQVE7Ozs7OztrRUFFViw4REFBQy9FLHlEQUFNQTt3REFDTDRFLE1BQUs7d0RBQ0xQLFNBQVE7d0RBQ1JDLE1BQUs7d0RBQ0xKLFdBQVU7d0RBQ1ZLLFNBQVMsSUFBTXhDLGdCQUFnQixDQUFDRDtrRUFFL0JBLDZCQUNDLDhEQUFDdEIsb0tBQU1BOzREQUFDMEQsV0FBVTs7Ozs7aUZBRWxCLDhEQUFDM0Qsb0tBQUdBOzREQUFDMkQsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7b0NBT3RCaEMsdUJBQ0MsOERBQUMvQix1REFBS0E7d0NBQUMrRCxXQUFVOzswREFDZiw4REFBQ2xELG9LQUFXQTtnREFBQ2tELFdBQVU7Ozs7OzswREFDdkIsOERBQUM5RCxrRUFBZ0JBO2dEQUFDOEQsV0FBVTswREFBb0NoQzs7Ozs7Ozs7Ozs7O29DQUtuRUUseUJBQ0MsOERBQUNqQyx1REFBS0E7d0NBQUMrRCxXQUFVOzswREFDZiw4REFBQ2hELG9LQUFXQTtnREFBQ2dELFdBQVU7Ozs7OzswREFDdkIsOERBQUM5RCxrRUFBZ0JBO2dEQUFDOEQsV0FBVTswREFBa0I5Qjs7Ozs7Ozs7Ozs7O2tEQUtsRCw4REFBQ3BDLHlEQUFNQTt3Q0FBQzRFLE1BQUs7d0NBQVNWLFdBQVU7d0NBQTBCYyxVQUFVaEQ7a0RBQ2pFQSwwQkFDQyw4REFBQ2lDOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7Ozs7OztnREFBOEU7Ozs7OztpRUFJL0YsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ3RELG9LQUFNQTtvREFBQ3NELFdBQVU7Ozs7OztnREFBWTs7Ozs7Ozs7Ozs7O2tEQU9wQyw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDNUQsK0RBQVNBO2dEQUFDNEQsV0FBVTs7Ozs7OzBEQUNyQiw4REFBQ2U7Z0RBQUtmLFdBQVU7MERBQWlIOzs7Ozs7Ozs7Ozs7a0RBTW5JLDhEQUFDbEUseURBQU1BO3dDQUNMNEUsTUFBSzt3Q0FDTFAsU0FBUTt3Q0FDUkgsV0FBVTt3Q0FDVkssU0FBU1Q7d0NBQ1RrQixVQUFVaEQ7OzBEQUVWLDhEQUFDckIsb0tBQU1BO2dEQUFDdUQsV0FBVTs7Ozs7OzRDQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dCQVExQ3ZCLDRCQUNDLDhEQUFDL0MscURBQUlBO29CQUFDc0UsV0FBVTs7c0NBQ2QsOERBQUNwRSwyREFBVUE7c0NBQ1QsNEVBQUNDLDBEQUFTQTtnQ0FBQ21FLFdBQVU7O2tEQUNuQiw4REFBQ3JELG9LQUFXQTt3Q0FBQ3FELFdBQVU7Ozs7OztvQ0FBWTs7Ozs7Ozs7Ozs7O3NDQUl2Qyw4REFBQ3JFLDREQUFXQTs0QkFBQ3FFLFdBQVU7OzhDQUNyQiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNlO29EQUFLZixXQUFVOzhEQUFnQjs7Ozs7OzhEQUNoQyw4REFBQzdELHVEQUFLQTtvREFBQ2dFLFNBQVE7b0RBQVVILFdBQVU7OERBQVU7Ozs7Ozs7Ozs7OztzREFJL0MsOERBQUNFOzRDQUFFRixXQUFVO3NEQUF3Qjs7Ozs7Ozs7Ozs7OzhDQUd2Qyw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNlO29EQUFLZixXQUFVOzhEQUFnQjs7Ozs7OzhEQUNoQyw4REFBQzdELHVEQUFLQTtvREFBQ2dFLFNBQVE7b0RBQVVILFdBQVU7OERBQVU7Ozs7Ozs7Ozs7OztzREFJL0MsOERBQUNFOzRDQUFFRixXQUFVO3NEQUF3Qjs7Ozs7Ozs7Ozs7OzhDQUd2Qyw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNlO29EQUFLZixXQUFVOzhEQUFnQjs7Ozs7OzhEQUNoQyw4REFBQzdELHVEQUFLQTtvREFBQ2dFLFNBQVE7b0RBQVVILFdBQVU7OERBQVU7Ozs7Ozs7Ozs7OztzREFJL0MsOERBQUNFOzRDQUFFRixXQUFVO3NEQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU83Qyw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDcEQsb0tBQUtBO3dDQUFDb0QsV0FBVTs7Ozs7Ozs7Ozs7OENBRW5CLDhEQUFDRTtvQ0FBRUYsV0FBVTs4Q0FBd0I7Ozs7Ozs7Ozs7OztzQ0FFdkMsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNuRCxvS0FBS0E7d0NBQUNtRCxXQUFVOzs7Ozs7Ozs7Ozs4Q0FFbkIsOERBQUNFO29DQUFFRixXQUFVOzhDQUF3Qjs7Ozs7Ozs7Ozs7O3NDQUV2Qyw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ3RELG9LQUFNQTt3Q0FBQ3NELFdBQVU7Ozs7Ozs7Ozs7OzhDQUVwQiw4REFBQ0U7b0NBQUVGLFdBQVU7OENBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBS3pDLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNFO3NDQUFFOzs7Ozs7c0NBQ0gsOERBQUNBOzRCQUFFRixXQUFVO3NDQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUs5QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBYmhpalxcRG93bmxvYWRzXFxRdWFudG5leC5haVxcY29tcG9uZW50c1xcYXV0aFxcZW5oYW5jZWQtaW5kaWFuLWxvZ2luLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxyXG5cclxuaW1wb3J0IHR5cGUgUmVhY3QgZnJvbSBcInJlYWN0XCJcclxuXHJcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIlxyXG5pbXBvcnQgeyB1c2VSb3V0ZXIsIHVzZVNlYXJjaFBhcmFtcyB9IGZyb20gXCJuZXh0L25hdmlnYXRpb25cIlxyXG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9jYXJkXCJcclxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIlxyXG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvaW5wdXRcIlxyXG5pbXBvcnQgeyBMYWJlbCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvbGFiZWxcIlxyXG5pbXBvcnQgeyBBbGVydCwgQWxlcnREZXNjcmlwdGlvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYWxlcnRcIlxyXG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYmFkZ2VcIlxyXG5pbXBvcnQgeyBTZXBhcmF0b3IgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3NlcGFyYXRvclwiXHJcbmltcG9ydCB7XHJcbiAgRXllLFxyXG4gIEV5ZU9mZixcclxuICBNYWlsLFxyXG4gIExvY2ssXHJcbiAgQ2hyb21lLFxyXG4gIFNoaWVsZCxcclxuICBTdGV0aG9zY29wZSxcclxuICBCcmFpbixcclxuICBIZWFydCxcclxuICBBbGVydENpcmNsZSxcclxuICBJbmZvLFxyXG4gIENoZWNrQ2lyY2xlLFxyXG59IGZyb20gXCJsdWNpZGUtcmVhY3RcIlxyXG5pbXBvcnQgeyBpbmRpYW5CYWNrZW5kU2VydmljZSB9IGZyb20gXCJAL2xpYi9pbmRpYW4tYmFja2VuZC1zZXJ2aWNlXCJcclxuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gXCJAL2NvbnRleHRzL2F1dGgtY29udGV4dFwiXHJcblxyXG5leHBvcnQgZnVuY3Rpb24gRW5oYW5jZWRJbmRpYW5Mb2dpbigpIHtcclxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxyXG4gIGNvbnN0IHNlYXJjaFBhcmFtcyA9IHVzZVNlYXJjaFBhcmFtcygpXHJcbiAgY29uc3QgeyBsb2dpbiwgaXNBdXRoZW50aWNhdGVkIH0gPSB1c2VBdXRoKClcclxuXHJcbiAgY29uc3QgW2Zvcm1EYXRhLCBzZXRGb3JtRGF0YV0gPSB1c2VTdGF0ZSh7XHJcbiAgICBlbWFpbDogXCJcIixcclxuICAgIHBhc3N3b3JkOiBcIlwiLFxyXG4gIH0pXHJcbiAgY29uc3QgW3Nob3dQYXNzd29yZCwgc2V0U2hvd1Bhc3N3b3JkXSA9IHVzZVN0YXRlKGZhbHNlKVxyXG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSlcclxuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlKFwiXCIpXHJcbiAgY29uc3QgW3N1Y2Nlc3MsIHNldFN1Y2Nlc3NdID0gdXNlU3RhdGUoXCJcIilcclxuXHJcbiAgLy8gR2V0IHJlZGlyZWN0IFVSTCBmcm9tIHNlYXJjaCBwYXJhbXNcclxuICBjb25zdCByZWRpcmVjdFVybCA9IHNlYXJjaFBhcmFtcy5nZXQoJ3JlZGlyZWN0JykgfHwgJy9kYXNoYm9hcmQnXHJcblxyXG4gIC8vIFJlZGlyZWN0IGlmIGFscmVhZHkgYXV0aGVudGljYXRlZFxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoaXNBdXRoZW50aWNhdGVkKSB7XHJcbiAgICAgIHJvdXRlci5wdXNoKHJlZGlyZWN0VXJsKVxyXG4gICAgfVxyXG4gIH0sIFtpc0F1dGhlbnRpY2F0ZWQsIHJvdXRlciwgcmVkaXJlY3RVcmxdKVxyXG5cclxuICAvLyBHZXQgZGVtbyBjcmVkZW50aWFscyBmb3IgZGlzcGxheVxyXG4gIGNvbnN0IGRlbW9DcmVkZW50aWFscyA9IGluZGlhbkJhY2tlbmRTZXJ2aWNlLmdldERlbW9DcmVkZW50aWFscygpXHJcbiAgY29uc3QgaXNEZW1vTW9kZSA9IGluZGlhbkJhY2tlbmRTZXJ2aWNlLmlzRGVtb01vZGVBY3RpdmUoKVxyXG5cclxuICBjb25zdCBoYW5kbGVJbnB1dENoYW5nZSA9IChlOiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50PikgPT4ge1xyXG4gICAgY29uc3QgeyBuYW1lLCB2YWx1ZSB9ID0gZS50YXJnZXRcclxuICAgIHNldEZvcm1EYXRhKChwcmV2KSA9PiAoe1xyXG4gICAgICAuLi5wcmV2LFxyXG4gICAgICBbbmFtZV06IHZhbHVlLFxyXG4gICAgfSkpXHJcbiAgICAvLyBDbGVhciBlcnJvcnMgd2hlbiB1c2VyIHN0YXJ0cyB0eXBpbmdcclxuICAgIGlmIChlcnJvcikgc2V0RXJyb3IoXCJcIilcclxuICB9XHJcblxyXG4gIGNvbnN0IGhhbmRsZUxvZ2luID0gYXN5bmMgKGU6IFJlYWN0LkZvcm1FdmVudCkgPT4ge1xyXG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpXHJcbiAgICBzZXRJc0xvYWRpbmcodHJ1ZSlcclxuICAgIHNldEVycm9yKFwiXCIpXHJcbiAgICBzZXRTdWNjZXNzKFwiXCIpXHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBpbmRpYW5CYWNrZW5kU2VydmljZS5sb2dpbihmb3JtRGF0YSlcclxuXHJcbiAgICAgIGlmIChyZXNwb25zZS5zdWNjZXNzICYmIHJlc3BvbnNlLnVzZXIpIHtcclxuICAgICAgICBzZXRTdWNjZXNzKGBXZWxjb21lIGJhY2ssICR7cmVzcG9uc2UudXNlci5uYW1lfSFgKVxyXG5cclxuICAgICAgICAvLyBTdG9yZSB1c2VyIGRhdGFcclxuICAgICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gXCJ1bmRlZmluZWRcIikge1xyXG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oXCJxdWFudG5leC11c2VyXCIsIEpTT04uc3RyaW5naWZ5KHJlc3BvbnNlLnVzZXIpKVxyXG4gICAgICAgICAgaWYgKHJlc3BvbnNlLnRva2VuKSB7XHJcbiAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFwicXVhbnRuZXgtdG9rZW5cIiwgcmVzcG9uc2UudG9rZW4pXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBVcGRhdGUgYXV0aCBjb250ZXh0XHJcbiAgICAgICAgbG9naW4ocmVzcG9uc2UudXNlcilcclxuXHJcbiAgICAgICAgLy8gUmVkaXJlY3QgdG8gaW50ZW5kZWQgcGFnZSBvciBkYXNoYm9hcmRcclxuICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICAgIHJvdXRlci5wdXNoKHJlZGlyZWN0VXJsKVxyXG4gICAgICAgIH0sIDEwMDApXHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgc2V0RXJyb3IocmVzcG9uc2UuZXJyb3IgfHwgXCJMb2dpbiBmYWlsZWQuIFBsZWFzZSB0cnkgYWdhaW4uXCIpXHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkxvZ2luIGVycm9yOlwiLCBlcnJvcilcclxuICAgICAgc2V0RXJyb3IoXCJBbiB1bmV4cGVjdGVkIGVycm9yIG9jY3VycmVkLiBQbGVhc2UgdHJ5IGFnYWluLlwiKVxyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgY29uc3QgaGFuZGxlR29vZ2xlU2lnbkluID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgc2V0SXNMb2FkaW5nKHRydWUpXHJcbiAgICBzZXRFcnJvcihcIlwiKVxyXG4gICAgc2V0U3VjY2VzcyhcIlwiKVxyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgaW5kaWFuQmFja2VuZFNlcnZpY2UuZ29vZ2xlU2lnbkluKClcclxuXHJcbiAgICAgIGlmIChyZXNwb25zZS5zdWNjZXNzICYmIHJlc3BvbnNlLnVzZXIpIHtcclxuICAgICAgICBzZXRTdWNjZXNzKGBXZWxjb21lLCAke3Jlc3BvbnNlLnVzZXIubmFtZX0hYClcclxuXHJcbiAgICAgICAgLy8gU3RvcmUgdXNlciBkYXRhXHJcbiAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09IFwidW5kZWZpbmVkXCIpIHtcclxuICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFwicXVhbnRuZXgtdXNlclwiLCBKU09OLnN0cmluZ2lmeShyZXNwb25zZS51c2VyKSlcclxuICAgICAgICAgIGlmIChyZXNwb25zZS50b2tlbikge1xyXG4gICAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShcInF1YW50bmV4LXRva2VuXCIsIHJlc3BvbnNlLnRva2VuKVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gVXBkYXRlIGF1dGggY29udGV4dFxyXG4gICAgICAgIGxvZ2luKHJlc3BvbnNlLnVzZXIpXHJcblxyXG4gICAgICAgIC8vIFJlZGlyZWN0IHRvIGludGVuZGVkIHBhZ2Ugb3IgZGFzaGJvYXJkXHJcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICByb3V0ZXIucHVzaChyZWRpcmVjdFVybClcclxuICAgICAgICB9LCAxMDAwKVxyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHNldEVycm9yKHJlc3BvbnNlLmVycm9yIHx8IFwiR29vZ2xlIHNpZ24taW4gZmFpbGVkLiBQbGVhc2UgdHJ5IGFnYWluLlwiKVxyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJHb29nbGUgc2lnbi1pbiBlcnJvcjpcIiwgZXJyb3IpXHJcbiAgICAgIHNldEVycm9yKFwiR29vZ2xlIHNpZ24taW4gZmFpbGVkLiBQbGVhc2UgdHJ5IGVtYWlsL3Bhc3N3b3JkIGxvZ2luLlwiKVxyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgY29uc3QgZmlsbERlbW9DcmVkZW50aWFscyA9ICgpID0+IHtcclxuICAgIHNldEZvcm1EYXRhKHtcclxuICAgICAgZW1haWw6IGRlbW9DcmVkZW50aWFscy5lbWFpbCxcclxuICAgICAgcGFzc3dvcmQ6IGRlbW9DcmVkZW50aWFscy5wYXNzd29yZCxcclxuICAgIH0pXHJcbiAgICBzZXRFcnJvcihcIlwiKVxyXG4gIH1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHAtNCBncmlkLXBhdHRlcm5cIj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgbWF4LXctbWQgc3BhY2UteS02XCI+XHJcbiAgICAgICAgey8qIEhlYWRlciAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHNwYWNlLXktMlwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktY2VudGVyIGl0ZW1zLWNlbnRlciBnYXAtMiBtYi00XCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMiBoLTEyIHJvdW5kZWQtZnVsbCBiZy10ZWFsLTUwMC8yMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBhbmltYXRlLXB1bHNlLWdsb3dcIj5cclxuICAgICAgICAgICAgICA8QnJhaW4gY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LXRlYWwtNDAwXCIgLz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXRlYWwtNDAwXCI+UXVhbnQtTkVYPC9oMT5cclxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtdGVhbC0zMDBcIj5DYW5jZXIgRGlhZ25vc2lzIFN5c3RlbTwvcD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIj5TaWduIGluIHRvIGFjY2VzcyB0aGUgbWVkaWNhbCBkYXNoYm9hcmQ8L3A+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIHsvKiBEZW1vIE1vZGUgQWxlcnQgKi99XHJcbiAgICAgICAge2lzRGVtb01vZGUgJiYgKFxyXG4gICAgICAgICAgPEFsZXJ0IGNsYXNzTmFtZT1cImJvcmRlci10ZWFsLTUwMC8zMCBiZy10ZWFsLTkwMC8yMFwiPlxyXG4gICAgICAgICAgICA8SW5mbyBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtdGVhbC00MDBcIiAvPlxyXG4gICAgICAgICAgICA8QWxlcnREZXNjcmlwdGlvbiBjbGFzc05hbWU9XCJ0ZXh0LXRlYWwtMzAwXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+RGVtbyBNb2RlIEFjdGl2ZTwvcD5cclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc21cIj5GaXJlYmFzZSBub3QgY29uZmlndXJlZC4gVXNpbmcgZGVtbyBhdXRoZW50aWNhdGlvbi48L3A+XHJcbiAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcclxuICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17ZmlsbERlbW9DcmVkZW50aWFsc31cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibXQtMiB0ZXh0LXRlYWwtNDAwIGJvcmRlci10ZWFsLTUwMC8zMCBob3ZlcjpiZy10ZWFsLTUwMC8xMCBiZy10cmFuc3BhcmVudFwiXHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIFVzZSBEZW1vIENyZWRlbnRpYWxzXHJcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9BbGVydERlc2NyaXB0aW9uPlxyXG4gICAgICAgICAgPC9BbGVydD5cclxuICAgICAgICApfVxyXG5cclxuICAgICAgICB7LyogTG9naW4gRm9ybSAqL31cclxuICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJjYXJkLWdsb3dcIj5cclxuICAgICAgICAgIDxDYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHRleHQtdGVhbC00MDBcIj5XZWxjb21lIEJhY2s8L0NhcmRUaXRsZT5cclxuICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cclxuICAgICAgICAgIDxDYXJkQ29udGVudD5cclxuICAgICAgICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZUxvZ2lufSBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cclxuICAgICAgICAgICAgICB7LyogRW1haWwgRmllbGQgKi99XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiZW1haWxcIiBjbGFzc05hbWU9XCJ0ZXh0LXRlYWwtMzAwXCI+XHJcbiAgICAgICAgICAgICAgICAgIEVtYWlsIEFkZHJlc3NcclxuICAgICAgICAgICAgICAgIDwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxNYWlsIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIGgtNCB3LTQgdGV4dC1ncmF5LTQwMFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgIGlkPVwiZW1haWxcIlxyXG4gICAgICAgICAgICAgICAgICAgIG5hbWU9XCJlbWFpbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cImVtYWlsXCJcclxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHlvdXIgZW1haWxcIlxyXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5lbWFpbH1cclxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSW5wdXRDaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicGwtMTAgYmctdGVhbC05MDAvMjAgYm9yZGVyLXRlYWwtNTAwLzMwIGZvY3VzOmJvcmRlci10ZWFsLTQwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICB7LyogUGFzc3dvcmQgRmllbGQgKi99XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwicGFzc3dvcmRcIiBjbGFzc05hbWU9XCJ0ZXh0LXRlYWwtMzAwXCI+XHJcbiAgICAgICAgICAgICAgICAgIFBhc3N3b3JkXHJcbiAgICAgICAgICAgICAgICA8L0xhYmVsPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxyXG4gICAgICAgICAgICAgICAgICA8TG9jayBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTMgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiBoLTQgdy00IHRleHQtZ3JheS00MDBcIiAvPlxyXG4gICAgICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgICAgICBpZD1cInBhc3N3b3JkXCJcclxuICAgICAgICAgICAgICAgICAgICBuYW1lPVwicGFzc3dvcmRcIlxyXG4gICAgICAgICAgICAgICAgICAgIHR5cGU9e3Nob3dQYXNzd29yZCA/IFwidGV4dFwiIDogXCJwYXNzd29yZFwifVxyXG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgeW91ciBwYXNzd29yZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnBhc3N3b3JkfVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwbC0xMCBwci0xMCBiZy10ZWFsLTkwMC8yMCBib3JkZXItdGVhbC01MDAvMzAgZm9jdXM6Ym9yZGVyLXRlYWwtNDAwXCJcclxuICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcclxuICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHJpZ2h0LTEgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiBoLTggdy04IHAtMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd1Bhc3N3b3JkKCFzaG93UGFzc3dvcmQpfVxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAge3Nob3dQYXNzd29yZCA/IChcclxuICAgICAgICAgICAgICAgICAgICAgIDxFeWVPZmYgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWdyYXktNDAwXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgPEV5ZSBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZ3JheS00MDBcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgIHsvKiBFcnJvciBBbGVydCAqL31cclxuICAgICAgICAgICAgICB7ZXJyb3IgJiYgKFxyXG4gICAgICAgICAgICAgICAgPEFsZXJ0IGNsYXNzTmFtZT1cImJvcmRlci1yZWQtNTAwLzMwIGJnLXJlZC05MDAvMjBcIj5cclxuICAgICAgICAgICAgICAgICAgPEFsZXJ0Q2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1yZWQtNDAwXCIgLz5cclxuICAgICAgICAgICAgICAgICAgPEFsZXJ0RGVzY3JpcHRpb24gY2xhc3NOYW1lPVwidGV4dC1yZWQtMzAwIHdoaXRlc3BhY2UtcHJlLWxpbmVcIj57ZXJyb3J9PC9BbGVydERlc2NyaXB0aW9uPlxyXG4gICAgICAgICAgICAgICAgPC9BbGVydD5cclxuICAgICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgICB7LyogU3VjY2VzcyBBbGVydCAqL31cclxuICAgICAgICAgICAgICB7c3VjY2VzcyAmJiAoXHJcbiAgICAgICAgICAgICAgICA8QWxlcnQgY2xhc3NOYW1lPVwiYm9yZGVyLWdyZWVuLTUwMC8zMCBiZy1ncmVlbi05MDAvMjBcIj5cclxuICAgICAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ncmVlbi00MDBcIiAvPlxyXG4gICAgICAgICAgICAgICAgICA8QWxlcnREZXNjcmlwdGlvbiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTMwMFwiPntzdWNjZXNzfTwvQWxlcnREZXNjcmlwdGlvbj5cclxuICAgICAgICAgICAgICAgIDwvQWxlcnQ+XHJcbiAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgey8qIExvZ2luIEJ1dHRvbiAqL31cclxuICAgICAgICAgICAgICA8QnV0dG9uIHR5cGU9XCJzdWJtaXRcIiBjbGFzc05hbWU9XCJ3LWZ1bGwgYnRuLWdsb3ctcHJpbWFyeVwiIGRpc2FibGVkPXtpc0xvYWRpbmd9PlxyXG4gICAgICAgICAgICAgICAge2lzTG9hZGluZyA/IChcclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy00IGgtNCBib3JkZXItMiBib3JkZXItd2hpdGUvMzAgYm9yZGVyLXQtd2hpdGUgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpblwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgU2lnbmluZyBJbi4uLlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICA8U2hpZWxkIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgIFNpZ24gSW5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG5cclxuICAgICAgICAgICAgICB7LyogRGl2aWRlciAqL31cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XHJcbiAgICAgICAgICAgICAgICA8U2VwYXJhdG9yIGNsYXNzTmFtZT1cImJnLXRlYWwtNTAwLzMwXCIgLz5cclxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMS8yIHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteC0xLzIgLXRyYW5zbGF0ZS15LTEvMiBiZy1iYWNrZ3JvdW5kIHB4LTIgdGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+XHJcbiAgICAgICAgICAgICAgICAgIG9yXHJcbiAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgIHsvKiBHb29nbGUgU2lnbiBJbiAqL31cclxuICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBnbG93LWhvdmVyIGJnLXRyYW5zcGFyZW50IGJvcmRlci10ZWFsLTUwMC8zMFwiXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVHb29nbGVTaWduSW59XHJcbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxDaHJvbWUgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cclxuICAgICAgICAgICAgICAgIENvbnRpbnVlIHdpdGggR29vZ2xlXHJcbiAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgIDwvZm9ybT5cclxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XHJcbiAgICAgICAgPC9DYXJkPlxyXG5cclxuICAgICAgICB7LyogRGVtbyBDcmVkZW50aWFscyBJbmZvICovfVxyXG4gICAgICAgIHtpc0RlbW9Nb2RlICYmIChcclxuICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImNhcmQtZ2xvd1wiPlxyXG4gICAgICAgICAgICA8Q2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC10ZWFsLTQwMCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgPFN0ZXRob3Njb3BlIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxyXG4gICAgICAgICAgICAgICAgRGVtbyBBY2NvdW50cyBBdmFpbGFibGVcclxuICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cclxuICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHRleHQtc21cIj5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMFwiPkRyLiBQcml5YSBQYXRlbCAoT25jb2xvZ2lzdCk8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PVwib3V0bGluZVwiIGNsYXNzTmFtZT1cInRleHQteHNcIj5cclxuICAgICAgICAgICAgICAgICAgICBUYXRhIE1lbW9yaWFsXHJcbiAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMFwiPmRyLnByaXlhQHF1YW50bmV4LmNvbSAvIGRlbW8xMjM8L3A+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiB0ZXh0LXNtXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDBcIj5Eci4gQW1pdCBHdXB0YSAoU3VyZ2Vvbik8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PVwib3V0bGluZVwiIGNsYXNzTmFtZT1cInRleHQteHNcIj5cclxuICAgICAgICAgICAgICAgICAgICBBSUlNUyBEZWxoaVxyXG4gICAgICAgICAgICAgICAgICA8L0JhZGdlPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5kci5hbWl0QHF1YW50bmV4LmNvbSAvIGRlbW8xMjM8L3A+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiB0ZXh0LXNtXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDBcIj5SYWplc2ggS3VtYXIgKEFkbWluKTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJvdXRsaW5lXCIgY2xhc3NOYW1lPVwidGV4dC14c1wiPlxyXG4gICAgICAgICAgICAgICAgICAgIEFwb2xsbyBIb3NwaXRhbFxyXG4gICAgICAgICAgICAgICAgICA8L0JhZGdlPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5hZG1pbkBxdWFudG5leC5jb20gLyBkZW1vMTIzPC9wPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxyXG4gICAgICAgICAgPC9DYXJkPlxyXG4gICAgICAgICl9XHJcblxyXG4gICAgICAgIHsvKiBGZWF0dXJlcyAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTMgZ2FwLTQgdGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMCBoLTEwIHJvdW5kZWQtZnVsbCBiZy10ZWFsLTUwMC8yMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvXCI+XHJcbiAgICAgICAgICAgICAgPEJyYWluIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC10ZWFsLTQwMFwiIC8+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5BSSBEaWFnbm9zaXM8L3A+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMCBoLTEwIHJvdW5kZWQtZnVsbCBiZy10ZWFsLTUwMC8yMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvXCI+XHJcbiAgICAgICAgICAgICAgPEhlYXJ0IGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC10ZWFsLTQwMFwiIC8+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5QYXRpZW50IENhcmU8L3A+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMCBoLTEwIHJvdW5kZWQtZnVsbCBiZy10ZWFsLTUwMC8yMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvXCI+XHJcbiAgICAgICAgICAgICAgPFNoaWVsZCBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtdGVhbC00MDBcIiAvPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNDAwXCI+U2VjdXJlIEFjY2VzczwvcD5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICB7LyogRm9vdGVyICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgdGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XHJcbiAgICAgICAgICA8cD7CqSAyMDI0IFF1YW50LU5FWC4gQWR2YW5jZWQgQ2FuY2VyIERpYWdub3NpcyBTeXN0ZW0uPC9wPlxyXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMVwiPlBvd2VyZWQgYnkgQUkg4oCiIFRydXN0ZWQgYnkgSW5kaWFuIEhvc3BpdGFsczwvcD5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApXHJcbn1cclxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUm91dGVyIiwidXNlU2VhcmNoUGFyYW1zIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIkJ1dHRvbiIsIklucHV0IiwiTGFiZWwiLCJBbGVydCIsIkFsZXJ0RGVzY3JpcHRpb24iLCJCYWRnZSIsIlNlcGFyYXRvciIsIkV5ZSIsIkV5ZU9mZiIsIk1haWwiLCJMb2NrIiwiQ2hyb21lIiwiU2hpZWxkIiwiU3RldGhvc2NvcGUiLCJCcmFpbiIsIkhlYXJ0IiwiQWxlcnRDaXJjbGUiLCJJbmZvIiwiQ2hlY2tDaXJjbGUiLCJpbmRpYW5CYWNrZW5kU2VydmljZSIsInVzZUF1dGgiLCJFbmhhbmNlZEluZGlhbkxvZ2luIiwicm91dGVyIiwic2VhcmNoUGFyYW1zIiwibG9naW4iLCJpc0F1dGhlbnRpY2F0ZWQiLCJmb3JtRGF0YSIsInNldEZvcm1EYXRhIiwiZW1haWwiLCJwYXNzd29yZCIsInNob3dQYXNzd29yZCIsInNldFNob3dQYXNzd29yZCIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJzdWNjZXNzIiwic2V0U3VjY2VzcyIsInJlZGlyZWN0VXJsIiwiZ2V0IiwicHVzaCIsImRlbW9DcmVkZW50aWFscyIsImdldERlbW9DcmVkZW50aWFscyIsImlzRGVtb01vZGUiLCJpc0RlbW9Nb2RlQWN0aXZlIiwiaGFuZGxlSW5wdXRDaGFuZ2UiLCJlIiwibmFtZSIsInZhbHVlIiwidGFyZ2V0IiwicHJldiIsImhhbmRsZUxvZ2luIiwicHJldmVudERlZmF1bHQiLCJyZXNwb25zZSIsInVzZXIiLCJsb2NhbFN0b3JhZ2UiLCJzZXRJdGVtIiwiSlNPTiIsInN0cmluZ2lmeSIsInRva2VuIiwic2V0VGltZW91dCIsImNvbnNvbGUiLCJoYW5kbGVHb29nbGVTaWduSW4iLCJnb29nbGVTaWduSW4iLCJmaWxsRGVtb0NyZWRlbnRpYWxzIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIiwidmFyaWFudCIsInNpemUiLCJvbkNsaWNrIiwiZm9ybSIsIm9uU3VibWl0IiwiaHRtbEZvciIsImlkIiwidHlwZSIsInBsYWNlaG9sZGVyIiwib25DaGFuZ2UiLCJyZXF1aXJlZCIsImRpc2FibGVkIiwic3BhbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/auth/enhanced-indian-login.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/alert.tsx":
/*!*********************************!*\
  !*** ./components/ui/alert.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* binding */ Alert),\n/* harmony export */   AlertDescription: () => (/* binding */ AlertDescription),\n/* harmony export */   AlertTitle: () => (/* binding */ AlertTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst alertVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\", {\n    variants: {\n        variant: {\n            default: \"bg-background text-foreground\",\n            destructive: \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Alert = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        role: \"alert\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(alertVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 26,\n        columnNumber: 3\n    }, undefined));\nAlert.displayName = \"Alert\";\nconst AlertTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"mb-1 font-medium leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined));\nAlertTitle.displayName = \"AlertTitle\";\nconst AlertDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm [&_p]:leading-relaxed\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nAlertDescription.displayName = \"AlertDescription\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2FsZXJ0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQThCO0FBQ21DO0FBRWpDO0FBRWhDLE1BQU1HLGdCQUFnQkYsNkRBQUdBLENBQ3ZCLDZKQUNBO0lBQ0VHLFVBQVU7UUFDUkMsU0FBUztZQUNQQyxTQUFTO1lBQ1RDLGFBQ0U7UUFDSjtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmSCxTQUFTO0lBQ1g7QUFDRjtBQUdGLE1BQU1JLHNCQUFRVCw2Q0FBZ0IsQ0FHNUIsQ0FBQyxFQUFFVyxTQUFTLEVBQUVOLE9BQU8sRUFBRSxHQUFHTyxPQUFPLEVBQUVDLG9CQUNuQyw4REFBQ0M7UUFDQ0QsS0FBS0E7UUFDTEUsTUFBSztRQUNMSixXQUFXVCw4Q0FBRUEsQ0FBQ0MsY0FBYztZQUFFRTtRQUFRLElBQUlNO1FBQ3pDLEdBQUdDLEtBQUs7Ozs7OztBQUdiSCxNQUFNTyxXQUFXLEdBQUc7QUFFcEIsTUFBTUMsMkJBQWFqQiw2Q0FBZ0IsQ0FHakMsQ0FBQyxFQUFFVyxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNLO1FBQ0NMLEtBQUtBO1FBQ0xGLFdBQVdULDhDQUFFQSxDQUFDLGdEQUFnRFM7UUFDN0QsR0FBR0MsS0FBSzs7Ozs7O0FBR2JLLFdBQVdELFdBQVcsR0FBRztBQUV6QixNQUFNRyxpQ0FBbUJuQiw2Q0FBZ0IsQ0FHdkMsQ0FBQyxFQUFFVyxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQ0NELEtBQUtBO1FBQ0xGLFdBQVdULDhDQUFFQSxDQUFDLGlDQUFpQ1M7UUFDOUMsR0FBR0MsS0FBSzs7Ozs7O0FBR2JPLGlCQUFpQkgsV0FBVyxHQUFHO0FBRWUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWJoaWpcXERvd25sb2Fkc1xcUXVhbnRuZXguYWlcXGNvbXBvbmVudHNcXHVpXFxhbGVydC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcclxuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gXCJjbGFzcy12YXJpYW5jZS1hdXRob3JpdHlcIlxyXG5cclxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxyXG5cclxuY29uc3QgYWxlcnRWYXJpYW50cyA9IGN2YShcclxuICBcInJlbGF0aXZlIHctZnVsbCByb3VuZGVkLWxnIGJvcmRlciBwLTQgWyY+c3ZnfipdOnBsLTcgWyY+c3ZnK2Rpdl06dHJhbnNsYXRlLXktWy0zcHhdIFsmPnN2Z106YWJzb2x1dGUgWyY+c3ZnXTpsZWZ0LTQgWyY+c3ZnXTp0b3AtNCBbJj5zdmddOnRleHQtZm9yZWdyb3VuZFwiLFxyXG4gIHtcclxuICAgIHZhcmlhbnRzOiB7XHJcbiAgICAgIHZhcmlhbnQ6IHtcclxuICAgICAgICBkZWZhdWx0OiBcImJnLWJhY2tncm91bmQgdGV4dC1mb3JlZ3JvdW5kXCIsXHJcbiAgICAgICAgZGVzdHJ1Y3RpdmU6XHJcbiAgICAgICAgICBcImJvcmRlci1kZXN0cnVjdGl2ZS81MCB0ZXh0LWRlc3RydWN0aXZlIGRhcms6Ym9yZGVyLWRlc3RydWN0aXZlIFsmPnN2Z106dGV4dC1kZXN0cnVjdGl2ZVwiLFxyXG4gICAgICB9LFxyXG4gICAgfSxcclxuICAgIGRlZmF1bHRWYXJpYW50czoge1xyXG4gICAgICB2YXJpYW50OiBcImRlZmF1bHRcIixcclxuICAgIH0sXHJcbiAgfVxyXG4pXHJcblxyXG5jb25zdCBBbGVydCA9IFJlYWN0LmZvcndhcmRSZWY8XHJcbiAgSFRNTERpdkVsZW1lbnQsXHJcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+ICYgVmFyaWFudFByb3BzPHR5cGVvZiBhbGVydFZhcmlhbnRzPlxyXG4+KCh7IGNsYXNzTmFtZSwgdmFyaWFudCwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXHJcbiAgPGRpdlxyXG4gICAgcmVmPXtyZWZ9XHJcbiAgICByb2xlPVwiYWxlcnRcIlxyXG4gICAgY2xhc3NOYW1lPXtjbihhbGVydFZhcmlhbnRzKHsgdmFyaWFudCB9KSwgY2xhc3NOYW1lKX1cclxuICAgIHsuLi5wcm9wc31cclxuICAvPlxyXG4pKVxyXG5BbGVydC5kaXNwbGF5TmFtZSA9IFwiQWxlcnRcIlxyXG5cclxuY29uc3QgQWxlcnRUaXRsZSA9IFJlYWN0LmZvcndhcmRSZWY8XHJcbiAgSFRNTFBhcmFncmFwaEVsZW1lbnQsXHJcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTEhlYWRpbmdFbGVtZW50PlxyXG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXHJcbiAgPGg1XHJcbiAgICByZWY9e3JlZn1cclxuICAgIGNsYXNzTmFtZT17Y24oXCJtYi0xIGZvbnQtbWVkaXVtIGxlYWRpbmctbm9uZSB0cmFja2luZy10aWdodFwiLCBjbGFzc05hbWUpfVxyXG4gICAgey4uLnByb3BzfVxyXG4gIC8+XHJcbikpXHJcbkFsZXJ0VGl0bGUuZGlzcGxheU5hbWUgPSBcIkFsZXJ0VGl0bGVcIlxyXG5cclxuY29uc3QgQWxlcnREZXNjcmlwdGlvbiA9IFJlYWN0LmZvcndhcmRSZWY8XHJcbiAgSFRNTFBhcmFncmFwaEVsZW1lbnQsXHJcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTFBhcmFncmFwaEVsZW1lbnQ+XHJcbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcclxuICA8ZGl2XHJcbiAgICByZWY9e3JlZn1cclxuICAgIGNsYXNzTmFtZT17Y24oXCJ0ZXh0LXNtIFsmX3BdOmxlYWRpbmctcmVsYXhlZFwiLCBjbGFzc05hbWUpfVxyXG4gICAgey4uLnByb3BzfVxyXG4gIC8+XHJcbikpXHJcbkFsZXJ0RGVzY3JpcHRpb24uZGlzcGxheU5hbWUgPSBcIkFsZXJ0RGVzY3JpcHRpb25cIlxyXG5cclxuZXhwb3J0IHsgQWxlcnQsIEFsZXJ0VGl0bGUsIEFsZXJ0RGVzY3JpcHRpb24gfVxyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjdmEiLCJjbiIsImFsZXJ0VmFyaWFudHMiLCJ2YXJpYW50cyIsInZhcmlhbnQiLCJkZWZhdWx0IiwiZGVzdHJ1Y3RpdmUiLCJkZWZhdWx0VmFyaWFudHMiLCJBbGVydCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJwcm9wcyIsInJlZiIsImRpdiIsInJvbGUiLCJkaXNwbGF5TmFtZSIsIkFsZXJ0VGl0bGUiLCJoNSIsIkFsZXJ0RGVzY3JpcHRpb24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/alert.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@19.0.0_react@19.0.0/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 8,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFFaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGtZQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFiaGlqXFxEb3dubG9hZHNcXFF1YW50bmV4LmFpXFxjb21wb25lbnRzXFx1aVxcaW5wdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXHJcblxyXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXHJcblxyXG5jb25zdCBJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgUmVhY3QuQ29tcG9uZW50UHJvcHM8XCJpbnB1dFwiPj4oXHJcbiAgKHsgY2xhc3NOYW1lLCB0eXBlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxpbnB1dFxyXG4gICAgICAgIHR5cGU9e3R5cGV9XHJcbiAgICAgICAgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgIFwiZmxleCBoLTEwIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBweC0zIHB5LTIgdGV4dC1iYXNlIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIGZpbGU6dGV4dC1mb3JlZ3JvdW5kIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTAgbWQ6dGV4dC1zbVwiLFxyXG4gICAgICAgICAgY2xhc3NOYW1lXHJcbiAgICAgICAgKX1cclxuICAgICAgICByZWY9e3JlZn1cclxuICAgICAgICB7Li4ucHJvcHN9XHJcbiAgICAgIC8+XHJcbiAgICApXHJcbiAgfVxyXG4pXHJcbklucHV0LmRpc3BsYXlOYW1lID0gXCJJbnB1dFwiXHJcblxyXG5leHBvcnQgeyBJbnB1dCB9XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiSW5wdXQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwidHlwZSIsInByb3BzIiwicmVmIiwiaW5wdXQiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/label.tsx":
/*!*********************************!*\
  !*** ./components/ui/label.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-label@2.1.7_740dc056907464af869becd071b4ef8b/node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined));\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2xhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFOEI7QUFDeUI7QUFDVTtBQUVqQztBQUVoQyxNQUFNSSxnQkFBZ0JGLDZEQUFHQSxDQUN2QjtBQUdGLE1BQU1HLHNCQUFRTCw2Q0FBZ0IsQ0FJNUIsQ0FBQyxFQUFFTyxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNSLHVEQUFtQjtRQUNsQlEsS0FBS0E7UUFDTEYsV0FBV0osOENBQUVBLENBQUNDLGlCQUFpQkc7UUFDOUIsR0FBR0MsS0FBSzs7Ozs7O0FBR2JILE1BQU1NLFdBQVcsR0FBR1YsdURBQW1CLENBQUNVLFdBQVc7QUFFbkMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWJoaWpcXERvd25sb2Fkc1xcUXVhbnRuZXguYWlcXGNvbXBvbmVudHNcXHVpXFxsYWJlbC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcclxuXHJcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXHJcbmltcG9ydCAqIGFzIExhYmVsUHJpbWl0aXZlIGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtbGFiZWxcIlxyXG5pbXBvcnQgeyBjdmEsIHR5cGUgVmFyaWFudFByb3BzIH0gZnJvbSBcImNsYXNzLXZhcmlhbmNlLWF1dGhvcml0eVwiXHJcblxyXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXHJcblxyXG5jb25zdCBsYWJlbFZhcmlhbnRzID0gY3ZhKFxyXG4gIFwidGV4dC1zbSBmb250LW1lZGl1bSBsZWFkaW5nLW5vbmUgcGVlci1kaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgcGVlci1kaXNhYmxlZDpvcGFjaXR5LTcwXCJcclxuKVxyXG5cclxuY29uc3QgTGFiZWwgPSBSZWFjdC5mb3J3YXJkUmVmPFxyXG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIExhYmVsUHJpbWl0aXZlLlJvb3Q+LFxyXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4gJlxyXG4gICAgVmFyaWFudFByb3BzPHR5cGVvZiBsYWJlbFZhcmlhbnRzPlxyXG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXHJcbiAgPExhYmVsUHJpbWl0aXZlLlJvb3RcclxuICAgIHJlZj17cmVmfVxyXG4gICAgY2xhc3NOYW1lPXtjbihsYWJlbFZhcmlhbnRzKCksIGNsYXNzTmFtZSl9XHJcbiAgICB7Li4ucHJvcHN9XHJcbiAgLz5cclxuKSlcclxuTGFiZWwuZGlzcGxheU5hbWUgPSBMYWJlbFByaW1pdGl2ZS5Sb290LmRpc3BsYXlOYW1lXHJcblxyXG5leHBvcnQgeyBMYWJlbCB9XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkxhYmVsUHJpbWl0aXZlIiwiY3ZhIiwiY24iLCJsYWJlbFZhcmlhbnRzIiwiTGFiZWwiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJyZWYiLCJSb290IiwiZGlzcGxheU5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/separator.tsx":
/*!*************************************!*\
  !*** ./components/ui/separator.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Separator: () => (/* binding */ Separator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-separator */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-separator@1_950c1e6bfa4978bee213a7fb1774abec/node_modules/@radix-ui/react-separator/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Separator auto */ \n\n\n\nconst Separator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, orientation = \"horizontal\", decorative = true, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        decorative: decorative,\n        orientation: orientation,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"shrink-0 bg-border\", orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\separator.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, undefined));\nSeparator.displayName = _radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/separator.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/auth-context.tsx":
/*!***********************************!*\
  !*** ./contexts/auth-context.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_indian_backend_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/indian-backend-service */ \"(ssr)/./lib/indian-backend-service.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Check for existing user session\n            const checkAuth = {\n                \"AuthProvider.useEffect.checkAuth\": async ()=>{\n                    try {\n                        const currentUser = await _lib_indian_backend_service__WEBPACK_IMPORTED_MODULE_2__.indianBackendService.getCurrentUser();\n                        if (currentUser) {\n                            setUser(currentUser);\n                        }\n                    } catch (error) {\n                        console.error(\"Auth check error:\", error);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkAuth\"];\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = (userData)=>{\n        setUser(userData);\n        if (false) {}\n    };\n    const logout = async ()=>{\n        try {\n            await _lib_indian_backend_service__WEBPACK_IMPORTED_MODULE_2__.indianBackendService.logout();\n            setUser(null);\n            if (false) {}\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        }\n    };\n    const value = {\n        user,\n        isLoading,\n        login,\n        logout,\n        isAuthenticated: !!user\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\contexts\\\\auth-context.tsx\",\n        lineNumber: 67,\n        columnNumber: 10\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/auth-context.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/indian-backend-service.ts":
/*!***************************************!*\
  !*** ./lib/indian-backend-service.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   indianBackendService: () => (/* binding */ indianBackendService)\n/* harmony export */ });\n// Enhanced Indian Backend Service with proper error handling\n// Demo users for fallback authentication\nconst DEMO_USERS = [\n    {\n        id: \"demo-1\",\n        email: \"<EMAIL>\",\n        name: \"Dr. Priya Patel\",\n        role: \"Oncologist\",\n        hospital: \"Tata Memorial Hospital\",\n        department: \"Neuro-Oncology\"\n    },\n    {\n        id: \"demo-2\",\n        email: \"<EMAIL>\",\n        name: \"Dr. Amit Gupta\",\n        role: \"Surgeon\",\n        hospital: \"AIIMS Delhi\",\n        department: \"Cancer Surgery\"\n    },\n    {\n        id: \"demo-3\",\n        email: \"<EMAIL>\",\n        name: \"Rajesh Kumar\",\n        role: \"Administrator\",\n        hospital: \"Apollo Hospital\",\n        department: \"Administration\"\n    }\n];\nclass IndianBackendService {\n    constructor(){\n        this.baseUrl = process.env.NEXT_PUBLIC_API_URL || \"https://api.quantnex.in\";\n        this.isDemoMode = false;\n        // Check if we're in demo mode (development or Firebase not configured)\n        this.isDemoMode =  true || 0;\n    }\n    async login(credentials) {\n        try {\n            // If Firebase is not configured or we're in demo mode, use demo authentication\n            if (this.isDemoMode) {\n                return this.demoLogin(credentials);\n            }\n            // Try Firebase authentication first\n            try {\n                const { signInWithEmailAndPassword } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/tslib@2.8.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+auth@1.10.8_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+util@1.12.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+component@0.6.18\"), __webpack_require__.e(\"vendor-chunks/idb@7.1.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+logger@0.4.4\"), __webpack_require__.e(\"vendor-chunks/firebase@11.10.0\")]).then(__webpack_require__.bind(__webpack_require__, /*! firebase/auth */ \"(ssr)/./node_modules/.pnpm/firebase@11.10.0/node_modules/firebase/auth/dist/index.mjs\"));\n                const { auth } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/tslib@2.8.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+auth@1.10.8_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+util@1.12.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+component@0.6.18\"), __webpack_require__.e(\"vendor-chunks/idb@7.1.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+logger@0.4.4\"), __webpack_require__.e(\"vendor-chunks/firebase@11.10.0\"), __webpack_require__.e(\"vendor-chunks/@grpc+grpc-js@1.9.15\"), __webpack_require__.e(\"vendor-chunks/protobufjs@7.5.3\"), __webpack_require__.e(\"vendor-chunks/@grpc+proto-loader@0.7.15\"), __webpack_require__.e(\"vendor-chunks/@firebase+webchannel-wrapper@1.0.3\"), __webpack_require__.e(\"vendor-chunks/@firebase+storage@0.13.14_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+firestore@4.8.0_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/long@5.3.2\"), __webpack_require__.e(\"vendor-chunks/lodash.camelcase@4.3.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+utf8@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+pool@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+path@1.1.2\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+inquire@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+float@1.0.2\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+fetch@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+eventemitter@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+codegen@2.0.4\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+base64@1.1.2\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+aspromise@1.1.2\"), __webpack_require__.e(\"_ssr_lib_firebase_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! ./firebase */ \"(ssr)/./lib/firebase.ts\"));\n                const userCredential = await signInWithEmailAndPassword(auth, credentials.email, credentials.password);\n                return {\n                    success: true,\n                    user: {\n                        id: userCredential.user.uid,\n                        email: userCredential.user.email || \"\",\n                        name: userCredential.user.displayName || \"User\",\n                        role: \"Doctor\"\n                    },\n                    token: await userCredential.user.getIdToken()\n                };\n            } catch (firebaseError) {\n                console.warn(\"Firebase authentication failed, falling back to demo mode:\", firebaseError.message);\n                // If Firebase fails, fall back to demo mode\n                return this.demoLogin(credentials);\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            // Final fallback to demo mode\n            return this.demoLogin(credentials);\n        }\n    }\n    async demoLogin(credentials) {\n        // Simulate API delay\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        // Check demo credentials\n        const user = DEMO_USERS.find((u)=>u.email === credentials.email);\n        if (user && (credentials.password === \"demo123\" || credentials.password === \"password\")) {\n            return {\n                success: true,\n                user,\n                token: `demo-token-${user.id}-${Date.now()}`\n            };\n        }\n        // If no match found, provide helpful demo credentials\n        return {\n            success: false,\n            error: `Invalid credentials. Demo accounts available:\n      \n      • <EMAIL> / demo123\n      • <EMAIL> / demo123  \n      • <EMAIL> / demo123\n      \n      Or use any email with password: demo123`\n        };\n    }\n    async googleSignIn() {\n        try {\n            if (this.isDemoMode) {\n                // Demo Google sign-in\n                await new Promise((resolve)=>setTimeout(resolve, 1500));\n                return {\n                    success: true,\n                    user: {\n                        id: \"google-demo-1\",\n                        email: \"<EMAIL>\",\n                        name: \"Dr. Kavya Sharma\",\n                        role: \"Radiologist\",\n                        hospital: \"Apollo Hospital\",\n                        department: \"Medical Imaging\"\n                    },\n                    token: `google-demo-token-${Date.now()}`\n                };\n            }\n            // Try Firebase Google authentication\n            try {\n                const { signInWithPopup, GoogleAuthProvider } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/tslib@2.8.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+auth@1.10.8_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+util@1.12.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+component@0.6.18\"), __webpack_require__.e(\"vendor-chunks/idb@7.1.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+logger@0.4.4\"), __webpack_require__.e(\"vendor-chunks/firebase@11.10.0\")]).then(__webpack_require__.bind(__webpack_require__, /*! firebase/auth */ \"(ssr)/./node_modules/.pnpm/firebase@11.10.0/node_modules/firebase/auth/dist/index.mjs\"));\n                const { auth } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/tslib@2.8.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+auth@1.10.8_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+util@1.12.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+component@0.6.18\"), __webpack_require__.e(\"vendor-chunks/idb@7.1.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+logger@0.4.4\"), __webpack_require__.e(\"vendor-chunks/firebase@11.10.0\"), __webpack_require__.e(\"vendor-chunks/@grpc+grpc-js@1.9.15\"), __webpack_require__.e(\"vendor-chunks/protobufjs@7.5.3\"), __webpack_require__.e(\"vendor-chunks/@grpc+proto-loader@0.7.15\"), __webpack_require__.e(\"vendor-chunks/@firebase+webchannel-wrapper@1.0.3\"), __webpack_require__.e(\"vendor-chunks/@firebase+storage@0.13.14_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+firestore@4.8.0_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/long@5.3.2\"), __webpack_require__.e(\"vendor-chunks/lodash.camelcase@4.3.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+utf8@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+pool@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+path@1.1.2\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+inquire@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+float@1.0.2\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+fetch@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+eventemitter@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+codegen@2.0.4\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+base64@1.1.2\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+aspromise@1.1.2\"), __webpack_require__.e(\"_ssr_lib_firebase_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! ./firebase */ \"(ssr)/./lib/firebase.ts\"));\n                const provider = new GoogleAuthProvider();\n                provider.addScope(\"email\");\n                provider.addScope(\"profile\");\n                const result = await signInWithPopup(auth, provider);\n                return {\n                    success: true,\n                    user: {\n                        id: result.user.uid,\n                        email: result.user.email || \"\",\n                        name: result.user.displayName || \"User\",\n                        role: \"Doctor\"\n                    },\n                    token: await result.user.getIdToken()\n                };\n            } catch (firebaseError) {\n                console.warn(\"Firebase Google sign-in failed, using demo mode:\", firebaseError.message);\n                // Fallback to demo Google sign-in\n                return {\n                    success: true,\n                    user: {\n                        id: \"google-demo-fallback\",\n                        email: \"<EMAIL>\",\n                        name: \"Dr. Arjun Singh\",\n                        role: \"Oncologist\",\n                        hospital: \"Fortis Hospital\",\n                        department: \"Medical Oncology\"\n                    },\n                    token: `google-demo-fallback-${Date.now()}`\n                };\n            }\n        } catch (error) {\n            console.error(\"Google sign-in error:\", error);\n            return {\n                success: false,\n                error: \"Google sign-in failed. Please try email/password login or contact support.\"\n            };\n        }\n    }\n    async logout() {\n        try {\n            if (!this.isDemoMode) {\n                const { signOut } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/tslib@2.8.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+auth@1.10.8_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+util@1.12.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+component@0.6.18\"), __webpack_require__.e(\"vendor-chunks/idb@7.1.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+logger@0.4.4\"), __webpack_require__.e(\"vendor-chunks/firebase@11.10.0\")]).then(__webpack_require__.bind(__webpack_require__, /*! firebase/auth */ \"(ssr)/./node_modules/.pnpm/firebase@11.10.0/node_modules/firebase/auth/dist/index.mjs\"));\n                const { auth } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/tslib@2.8.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+auth@1.10.8_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+util@1.12.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+component@0.6.18\"), __webpack_require__.e(\"vendor-chunks/idb@7.1.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+logger@0.4.4\"), __webpack_require__.e(\"vendor-chunks/firebase@11.10.0\"), __webpack_require__.e(\"vendor-chunks/@grpc+grpc-js@1.9.15\"), __webpack_require__.e(\"vendor-chunks/protobufjs@7.5.3\"), __webpack_require__.e(\"vendor-chunks/@grpc+proto-loader@0.7.15\"), __webpack_require__.e(\"vendor-chunks/@firebase+webchannel-wrapper@1.0.3\"), __webpack_require__.e(\"vendor-chunks/@firebase+storage@0.13.14_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+firestore@4.8.0_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/long@5.3.2\"), __webpack_require__.e(\"vendor-chunks/lodash.camelcase@4.3.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+utf8@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+pool@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+path@1.1.2\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+inquire@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+float@1.0.2\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+fetch@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+eventemitter@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+codegen@2.0.4\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+base64@1.1.2\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+aspromise@1.1.2\"), __webpack_require__.e(\"_ssr_lib_firebase_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! ./firebase */ \"(ssr)/./lib/firebase.ts\"));\n                await signOut(auth);\n            }\n            // Clear any stored tokens\n            if (false) {}\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n            return {\n                success: true\n            } // Always succeed for logout\n            ;\n        }\n    }\n    async getCurrentUser() {\n        try {\n            if (this.isDemoMode) {\n                // Return demo user if token exists\n                const token =  false ? 0 : null;\n                if (token && token.startsWith(\"demo-token\")) {\n                    return DEMO_USERS[0] // Return first demo user\n                    ;\n                }\n                return null;\n            }\n            const { auth } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/tslib@2.8.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+auth@1.10.8_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+util@1.12.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+component@0.6.18\"), __webpack_require__.e(\"vendor-chunks/idb@7.1.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+logger@0.4.4\"), __webpack_require__.e(\"vendor-chunks/firebase@11.10.0\"), __webpack_require__.e(\"vendor-chunks/@grpc+grpc-js@1.9.15\"), __webpack_require__.e(\"vendor-chunks/protobufjs@7.5.3\"), __webpack_require__.e(\"vendor-chunks/@grpc+proto-loader@0.7.15\"), __webpack_require__.e(\"vendor-chunks/@firebase+webchannel-wrapper@1.0.3\"), __webpack_require__.e(\"vendor-chunks/@firebase+storage@0.13.14_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+firestore@4.8.0_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/long@5.3.2\"), __webpack_require__.e(\"vendor-chunks/lodash.camelcase@4.3.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+utf8@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+pool@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+path@1.1.2\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+inquire@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+float@1.0.2\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+fetch@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+eventemitter@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+codegen@2.0.4\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+base64@1.1.2\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+aspromise@1.1.2\"), __webpack_require__.e(\"_ssr_lib_firebase_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! ./firebase */ \"(ssr)/./lib/firebase.ts\"));\n            const { onAuthStateChanged } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/tslib@2.8.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+auth@1.10.8_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+util@1.12.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+component@0.6.18\"), __webpack_require__.e(\"vendor-chunks/idb@7.1.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+logger@0.4.4\"), __webpack_require__.e(\"vendor-chunks/firebase@11.10.0\")]).then(__webpack_require__.bind(__webpack_require__, /*! firebase/auth */ \"(ssr)/./node_modules/.pnpm/firebase@11.10.0/node_modules/firebase/auth/dist/index.mjs\"));\n            return new Promise((resolve)=>{\n                const unsubscribe = onAuthStateChanged(auth, (user)=>{\n                    unsubscribe();\n                    if (user) {\n                        resolve({\n                            id: user.uid,\n                            email: user.email || \"\",\n                            name: user.displayName || \"User\",\n                            role: \"Doctor\"\n                        });\n                    } else {\n                        resolve(null);\n                    }\n                });\n            });\n        } catch (error) {\n            console.error(\"Get current user error:\", error);\n            return null;\n        }\n    }\n    // Patient management methods\n    async getPatients() {\n        // Simulate API call\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        return {\n            success: true,\n            data: [\n                {\n                    id: \"P001\",\n                    name: \"Rajesh Kumar Sharma\",\n                    age: 45,\n                    diagnosis: \"Glioblastoma\",\n                    status: \"active\"\n                },\n                {\n                    id: \"P002\",\n                    name: \"Sunita Devi Gupta\",\n                    age: 52,\n                    diagnosis: \"Breast Cancer\",\n                    status: \"stable\"\n                }\n            ]\n        };\n    }\n    async getDiagnosisData() {\n        await new Promise((resolve)=>setTimeout(resolve, 300));\n        return {\n            success: true,\n            data: {\n                totalScans: 1247,\n                pendingReviews: 23,\n                aiAccuracy: 94.2,\n                criticalCases: 8\n            }\n        };\n    }\n    async getHospitalData() {\n        await new Promise((resolve)=>setTimeout(resolve, 400));\n        return {\n            success: true,\n            data: [\n                {\n                    name: \"Tata Memorial Hospital\",\n                    location: \"Mumbai, Maharashtra\",\n                    patients: 1250,\n                    capacity: 1500\n                },\n                {\n                    name: \"AIIMS Delhi\",\n                    location: \"New Delhi\",\n                    patients: 2100,\n                    capacity: 2500\n                }\n            ]\n        };\n    }\n    // Check if service is in demo mode\n    isDemoModeActive() {\n        return this.isDemoMode;\n    }\n    // Get demo credentials for UI display\n    getDemoCredentials() {\n        return {\n            email: \"<EMAIL>\",\n            password: \"demo123\",\n            alternatives: [\n                \"<EMAIL> / demo123\",\n                \"<EMAIL> / demo123\"\n            ]\n        };\n    }\n}\n// Export singleton instance\nconst indianBackendService = new IndianBackendService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./lib/indian-backend-service.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/.pnpm/tailwind-merge@2.5.5/node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFiaGlqXFxEb3dubG9hZHNcXFF1YW50bmV4LmFpXFxsaWJcXHV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcclxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXHJcblxyXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcclxuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXHJcbn1cclxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/login/page.tsx */ \"(ssr)/./app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfQGJhYmVsK2NvcmVANy4yX2FmNDYyZGEwYjQ0YTY0ZWU0YWM3YzYyY2I2ZjIyNmQ4L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQWJoaWolNUMlNUNEb3dubG9hZHMlNUMlNUNRdWFudG5leC5haSU1QyU1Q2FwcCU1QyU1Q2xvZ2luJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9KQUFtRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQWJoaWpcXFxcRG93bmxvYWRzXFxcXFF1YW50bmV4LmFpXFxcXGFwcFxcXFxsb2dpblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/auth-context.tsx */ \"(ssr)/./contexts/auth-context.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfQGJhYmVsK2NvcmVANy4yX2FmNDYyZGEwYjQ0YTY0ZWU0YWM3YzYyY2I2ZjIyNmQ4L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQWJoaWolNUMlNUNEb3dubG9hZHMlNUMlNUNRdWFudG5leC5haSU1QyU1Q2NvbnRleHRzJTVDJTVDYXV0aC1jb250ZXh0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkF1dGhQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBYmhpaiU1QyU1Q0Rvd25sb2FkcyU1QyU1Q1F1YW50bmV4LmFpJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDLnBucG0lNUMlNUNuZXh0JTQwMTUuMi40XyU0MGJhYmVsJTJCY29yZSU0MDcuMl9hZjQ2MmRhMGI0NGE2NGVlNGFjN2M2MmNiNmYyMjZkOCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBYmhpaiU1QyU1Q0Rvd25sb2FkcyU1QyU1Q1F1YW50bmV4LmFpJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUEySSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcQWJoaWpcXFxcRG93bmxvYWRzXFxcXFF1YW50bmV4LmFpXFxcXGNvbnRleHRzXFxcXGF1dGgtY29udGV4dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \**************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8","vendor-chunks/tailwind-merge@2.5.5","vendor-chunks/lucide-react@0.454.0_react@19.0.0","vendor-chunks/@radix-ui+react-slot@1.2.3_@types+react@19.0.0_react@19.0.0","vendor-chunks/class-variance-authority@0.7.1","vendor-chunks/@swc+helpers@0.5.15","vendor-chunks/@radix-ui+react-primitive@2_f1559ddf590287c33aa65e2eac7c63be","vendor-chunks/@radix-ui+react-compose-ref_a1d2853b5188b26995ccf8fe08b4ac18","vendor-chunks/clsx@2.1.1","vendor-chunks/@radix-ui+react-separator@1_950c1e6bfa4978bee213a7fb1774abec","vendor-chunks/@radix-ui+react-label@2.1.7_740dc056907464af869becd071b4ef8b"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CAbhij%5CDownloads%5CQuantnex.ai%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbhij%5CDownloads%5CQuantnex.ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();