/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"dc9e47d3cea6\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWJoaWpcXERvd25sb2Fkc1xcUXVhbnRuZXguYWlcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkYzllNDdkM2NlYTZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./contexts/auth-context.tsx":
/*!***********************************!*\
  !*** ./contexts/auth-context.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_indian_backend_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/indian-backend-service */ \"(app-pages-browser)/./lib/indian-backend-service.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Check for existing user session\n            const checkAuth = {\n                \"AuthProvider.useEffect.checkAuth\": async ()=>{\n                    try {\n                        const currentUser = await _lib_indian_backend_service__WEBPACK_IMPORTED_MODULE_2__.indianBackendService.getCurrentUser();\n                        if (currentUser) {\n                            setUser(currentUser);\n                        }\n                    } catch (error) {\n                        console.error(\"Auth check error:\", error);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkAuth\"];\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = (userData)=>{\n        setUser(userData);\n        if (true) {\n            localStorage.setItem(\"quantnex-user\", JSON.stringify(userData));\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await _lib_indian_backend_service__WEBPACK_IMPORTED_MODULE_2__.indianBackendService.logout();\n            setUser(null);\n            if (true) {\n                localStorage.removeItem(\"quantnex-user\");\n                localStorage.removeItem(\"quantnex-token\");\n            }\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        }\n    };\n    const value = {\n        user,\n        isLoading,\n        login,\n        logout,\n        isAuthenticated: !!user\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\contexts\\\\auth-context.tsx\",\n        lineNumber: 67,\n        columnNumber: 10\n    }, this);\n}\n_s(AuthProvider, \"YajQB7LURzRD+QP5gw0+K2TZIWA=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/auth-context.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/indian-backend-service.ts":
/*!***************************************!*\
  !*** ./lib/indian-backend-service.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   indianBackendService: () => (/* binding */ indianBackendService)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/polyfills/process.js\");\n// Enhanced Indian Backend Service with proper error handling\n// Demo users for fallback authentication\nconst DEMO_USERS = [\n    {\n        id: \"demo-1\",\n        email: \"<EMAIL>\",\n        name: \"Dr. Priya Patel\",\n        role: \"Oncologist\",\n        hospital: \"Tata Memorial Hospital\",\n        department: \"Neuro-Oncology\"\n    },\n    {\n        id: \"demo-2\",\n        email: \"<EMAIL>\",\n        name: \"Dr. Amit Gupta\",\n        role: \"Surgeon\",\n        hospital: \"AIIMS Delhi\",\n        department: \"Cancer Surgery\"\n    },\n    {\n        id: \"demo-3\",\n        email: \"<EMAIL>\",\n        name: \"Rajesh Kumar\",\n        role: \"Administrator\",\n        hospital: \"Apollo Hospital\",\n        department: \"Administration\"\n    }\n];\nclass IndianBackendService {\n    async login(credentials) {\n        try {\n            // If Firebase is not configured or we're in demo mode, use demo authentication\n            if (this.isDemoMode) {\n                return this.demoLogin(credentials);\n            }\n            // Try Firebase authentication first\n            try {\n                const { signInWithEmailAndPassword } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_pnpm_firebase_11_10_0_node_modules_firebase_auth_dist_esm_ind-15c111\").then(__webpack_require__.bind(__webpack_require__, /*! firebase/auth */ \"(app-pages-browser)/./node_modules/.pnpm/firebase@11.10.0/node_modules/firebase/auth/dist/esm/index.esm.js\"));\n                const { auth } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_lib_firebase_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./firebase */ \"(app-pages-browser)/./lib/firebase.ts\"));\n                const userCredential = await signInWithEmailAndPassword(auth, credentials.email, credentials.password);\n                return {\n                    success: true,\n                    user: {\n                        id: userCredential.user.uid,\n                        email: userCredential.user.email || \"\",\n                        name: userCredential.user.displayName || \"User\",\n                        role: \"Doctor\"\n                    },\n                    token: await userCredential.user.getIdToken()\n                };\n            } catch (firebaseError) {\n                console.warn(\"Firebase authentication failed, falling back to demo mode:\", firebaseError.message);\n                // If Firebase fails, fall back to demo mode\n                return this.demoLogin(credentials);\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            // Final fallback to demo mode\n            return this.demoLogin(credentials);\n        }\n    }\n    async demoLogin(credentials) {\n        // Simulate API delay\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        // Check demo credentials\n        const user = DEMO_USERS.find((u)=>u.email === credentials.email);\n        if (user && (credentials.password === \"demo123\" || credentials.password === \"password\")) {\n            return {\n                success: true,\n                user,\n                token: \"demo-token-\".concat(user.id, \"-\").concat(Date.now())\n            };\n        }\n        // If no match found, provide helpful demo credentials\n        return {\n            success: false,\n            error: \"Invalid credentials. Demo accounts available:\\n      \\n      • <EMAIL> / demo123\\n      • <EMAIL> / demo123  \\n      • <EMAIL> / demo123\\n      \\n      Or use any email with password: demo123\"\n        };\n    }\n    async googleSignIn() {\n        try {\n            if (this.isDemoMode) {\n                // Demo Google sign-in\n                await new Promise((resolve)=>setTimeout(resolve, 1500));\n                return {\n                    success: true,\n                    user: {\n                        id: \"google-demo-1\",\n                        email: \"<EMAIL>\",\n                        name: \"Dr. Kavya Sharma\",\n                        role: \"Radiologist\",\n                        hospital: \"Apollo Hospital\",\n                        department: \"Medical Imaging\"\n                    },\n                    token: \"google-demo-token-\".concat(Date.now())\n                };\n            }\n            // Try Firebase Google authentication\n            try {\n                const { signInWithPopup, GoogleAuthProvider } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_pnpm_firebase_11_10_0_node_modules_firebase_auth_dist_esm_ind-15c111\").then(__webpack_require__.bind(__webpack_require__, /*! firebase/auth */ \"(app-pages-browser)/./node_modules/.pnpm/firebase@11.10.0/node_modules/firebase/auth/dist/esm/index.esm.js\"));\n                const { auth } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_lib_firebase_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./firebase */ \"(app-pages-browser)/./lib/firebase.ts\"));\n                const provider = new GoogleAuthProvider();\n                provider.addScope(\"email\");\n                provider.addScope(\"profile\");\n                const result = await signInWithPopup(auth, provider);\n                return {\n                    success: true,\n                    user: {\n                        id: result.user.uid,\n                        email: result.user.email || \"\",\n                        name: result.user.displayName || \"User\",\n                        role: \"Doctor\"\n                    },\n                    token: await result.user.getIdToken()\n                };\n            } catch (firebaseError) {\n                console.warn(\"Firebase Google sign-in failed, using demo mode:\", firebaseError.message);\n                // Fallback to demo Google sign-in\n                return {\n                    success: true,\n                    user: {\n                        id: \"google-demo-fallback\",\n                        email: \"<EMAIL>\",\n                        name: \"Dr. Arjun Singh\",\n                        role: \"Oncologist\",\n                        hospital: \"Fortis Hospital\",\n                        department: \"Medical Oncology\"\n                    },\n                    token: \"google-demo-fallback-\".concat(Date.now())\n                };\n            }\n        } catch (error) {\n            console.error(\"Google sign-in error:\", error);\n            return {\n                success: false,\n                error: \"Google sign-in failed. Please try email/password login or contact support.\"\n            };\n        }\n    }\n    async logout() {\n        try {\n            if (!this.isDemoMode) {\n                const { signOut } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_pnpm_firebase_11_10_0_node_modules_firebase_auth_dist_esm_ind-15c111\").then(__webpack_require__.bind(__webpack_require__, /*! firebase/auth */ \"(app-pages-browser)/./node_modules/.pnpm/firebase@11.10.0/node_modules/firebase/auth/dist/esm/index.esm.js\"));\n                const { auth } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_lib_firebase_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./firebase */ \"(app-pages-browser)/./lib/firebase.ts\"));\n                await signOut(auth);\n            }\n            // Clear any stored tokens\n            if (true) {\n                localStorage.removeItem(\"quantnex-token\");\n                localStorage.removeItem(\"quantnex-user\");\n            }\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n            return {\n                success: true\n            } // Always succeed for logout\n            ;\n        }\n    }\n    async getCurrentUser() {\n        try {\n            if (this.isDemoMode) {\n                // Return demo user if token exists\n                const token =  true ? localStorage.getItem(\"quantnex-token\") : 0;\n                if (token && token.startsWith(\"demo-token\")) {\n                    return DEMO_USERS[0] // Return first demo user\n                    ;\n                }\n                return null;\n            }\n            const { auth } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_lib_firebase_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./firebase */ \"(app-pages-browser)/./lib/firebase.ts\"));\n            const { onAuthStateChanged } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_pnpm_firebase_11_10_0_node_modules_firebase_auth_dist_esm_ind-15c111\").then(__webpack_require__.bind(__webpack_require__, /*! firebase/auth */ \"(app-pages-browser)/./node_modules/.pnpm/firebase@11.10.0/node_modules/firebase/auth/dist/esm/index.esm.js\"));\n            return new Promise((resolve)=>{\n                const unsubscribe = onAuthStateChanged(auth, (user)=>{\n                    unsubscribe();\n                    if (user) {\n                        resolve({\n                            id: user.uid,\n                            email: user.email || \"\",\n                            name: user.displayName || \"User\",\n                            role: \"Doctor\"\n                        });\n                    } else {\n                        resolve(null);\n                    }\n                });\n            });\n        } catch (error) {\n            console.error(\"Get current user error:\", error);\n            return null;\n        }\n    }\n    // Patient management methods\n    async getPatients() {\n        // Simulate API call\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        return {\n            success: true,\n            data: [\n                {\n                    id: \"P001\",\n                    name: \"Rajesh Kumar Sharma\",\n                    age: 45,\n                    diagnosis: \"Glioblastoma\",\n                    status: \"active\"\n                },\n                {\n                    id: \"P002\",\n                    name: \"Sunita Devi Gupta\",\n                    age: 52,\n                    diagnosis: \"Breast Cancer\",\n                    status: \"stable\"\n                }\n            ]\n        };\n    }\n    async getDiagnosisData() {\n        await new Promise((resolve)=>setTimeout(resolve, 300));\n        return {\n            success: true,\n            data: {\n                totalScans: 1247,\n                pendingReviews: 23,\n                aiAccuracy: 94.2,\n                criticalCases: 8\n            }\n        };\n    }\n    async getHospitalData() {\n        await new Promise((resolve)=>setTimeout(resolve, 400));\n        return {\n            success: true,\n            data: [\n                {\n                    name: \"Tata Memorial Hospital\",\n                    location: \"Mumbai, Maharashtra\",\n                    patients: 1250,\n                    capacity: 1500\n                },\n                {\n                    name: \"AIIMS Delhi\",\n                    location: \"New Delhi\",\n                    patients: 2100,\n                    capacity: 2500\n                }\n            ]\n        };\n    }\n    // Check if service is in demo mode\n    isDemoModeActive() {\n        return this.isDemoMode;\n    }\n    // Get demo credentials for UI display\n    getDemoCredentials() {\n        return {\n            email: \"<EMAIL>\",\n            password: \"demo123\",\n            alternatives: [\n                \"<EMAIL> / demo123\",\n                \"<EMAIL> / demo123\"\n            ]\n        };\n    }\n    constructor(){\n        this.baseUrl = process.env.NEXT_PUBLIC_API_URL || \"https://api.quantnex.in\";\n        this.isDemoMode = false;\n        // Check if we're in demo mode (development or Firebase not configured)\n        this.isDemoMode =  true || 0;\n    }\n}\n// Export singleton instance\nconst indianBackendService = new IndianBackendService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/indian-backend-service.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/auth-context.tsx */ \"(app-pages-browser)/./contexts/auth-context.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/globals.css */ \"(app-pages-browser)/./app/globals.css\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(()=>{\"use strict\";var e={529:(e,r,t)=>{var n=t(191);var i=Object.create(null);var a=typeof document===\"undefined\";var o=Array.prototype.forEach;function debounce(e,r){var t=0;return function(){var n=this;var i=arguments;var a=function functionCall(){return e.apply(n,i)};clearTimeout(t);t=setTimeout(a,r)}}function noop(){}function getCurrentScriptUrl(e){var r=i[e];if(!r){if(document.currentScript){r=document.currentScript.src}else{var t=document.getElementsByTagName(\"script\");var a=t[t.length-1];if(a){r=a.src}}i[e]=r}return function(e){if(!r){return null}var t=r.split(/([^\\\\/]+)\\.js$/);var i=t&&t[1];if(!i){return[r.replace(\".js\",\".css\")]}if(!e){return[r.replace(\".js\",\".css\")]}return e.split(\",\").map((function(e){var t=new RegExp(\"\".concat(i,\"\\\\.js$\"),\"g\");return n(r.replace(t,\"\".concat(e.replace(/{fileName}/g,i),\".css\")))}))}}function updateCss(e,r){if(!r){if(!e.href){return}r=e.href.split(\"?\")[0]}if(!isUrlRequest(r)){return}if(e.isLoaded===false){return}if(!r||!(r.indexOf(\".css\")>-1)){return}e.visited=true;var t=e.cloneNode();t.isLoaded=false;t.addEventListener(\"load\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.addEventListener(\"error\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.href=\"\".concat(r,\"?\").concat(Date.now());if(e.nextSibling){e.parentNode.insertBefore(t,e.nextSibling)}else{e.parentNode.appendChild(t)}}function getReloadUrl(e,r){var t;e=n(e,{stripWWW:false});r.some((function(n){if(e.indexOf(r)>-1){t=n}}));return t}function reloadStyle(e){if(!e){return false}var r=document.querySelectorAll(\"link\");var t=false;o.call(r,(function(r){if(!r.href){return}var n=getReloadUrl(r.href,e);if(!isUrlRequest(n)){return}if(r.visited===true){return}if(n){updateCss(r,n);t=true}}));return t}function reloadAll(){var e=document.querySelectorAll(\"link\");o.call(e,(function(e){if(e.visited===true){return}updateCss(e)}))}function isUrlRequest(e){if(!/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.test(e)){return false}return true}e.exports=function(e,r){if(a){console.log(\"no window.document found, will not HMR CSS\");return noop}var t=getCurrentScriptUrl(e);function update(){var e=t(r.filename);var n=reloadStyle(e);if(r.locals){console.log(\"[HMR] Detected local css modules. Reload all css\");reloadAll();return}if(n){console.log(\"[HMR] css reload %s\",e.join(\" \"))}else{console.log(\"[HMR] Reload all css\");reloadAll()}}return debounce(update,50)}},191:e=>{function normalizeUrl(e){return e.reduce((function(e,r){switch(r){case\"..\":e.pop();break;case\".\":break;default:e.push(r)}return e}),[]).join(\"/\")}e.exports=function(e){e=e.trim();if(/^data:/i.test(e)){return e}var r=e.indexOf(\"//\")!==-1?e.split(\"//\")[0]+\"//\":\"\";var t=e.replace(new RegExp(r,\"i\"),\"\").split(\"/\");var n=t[0].toLowerCase().replace(/\\.$/,\"\");t[0]=\"\";var i=normalizeUrl(t);return r+n+i}}};var r={};function __nccwpck_require__(t){var n=r[t];if(n!==undefined){return n.exports}var i=r[t]={exports:{}};var a=true;try{e[t](i,i.exports,__nccwpck_require__);a=false}finally{if(a)delete r[t]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var t=__nccwpck_require__(529);module.exports=t})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PORTAL_TYPE:\n          return \"Portal\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          },\n      specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        Error(\"react-stack-top-frame\"),\n        createTask(getTaskName(type))\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \**************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjIuNF9AYmFiZWwrY29yZUA3LjJfYWY0NjJkYTBiNDRhNjRlZTRhYzdjNjJjYjZmMjI2ZDgvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsSUFBSSxLQUFxQyxFQUFFLEVBRTFDLENBQUM7QUFDRixFQUFFLDhRQUFzRTtBQUN4RSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBYmhpalxcRG93bmxvYWRzXFxRdWFudG5leC5haVxcbm9kZV9tb2R1bGVzXFwucG5wbVxcbmV4dEAxNS4yLjRfQGJhYmVsK2NvcmVANy4yX2FmNDYyZGEwYjQ0YTY0ZWU0YWM3YzYyY2I2ZjIyNmQ4XFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":
/*!*****************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/font/google/target.css?{"path":"app\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \*****************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'Inter', 'Inter Fallback'\",\"fontStyle\":\"normal\"},\"className\":\"__className_e8ce0c\"};\n    if(true) {\n      // 1751891253505\n      var cssReload = __webpack_require__(/*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjIuNF9AYmFiZWwrY29yZUA3LjJfYWY0NjJkYTBiNDRhNjRlZTRhYzdjNjJjYjZmMjI2ZDgvbm9kZV9tb2R1bGVzL25leHQvZm9udC9nb29nbGUvdGFyZ2V0LmNzcz97XCJwYXRoXCI6XCJhcHBcXFxcbGF5b3V0LnRzeFwiLFwiaW1wb3J0XCI6XCJJbnRlclwiLFwiYXJndW1lbnRzXCI6W3tcInN1YnNldHNcIjpbXCJsYXRpblwiXX1dLFwidmFyaWFibGVOYW1lXCI6XCJpbnRlclwifSIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBLGtCQUFrQixTQUFTLDhEQUE4RDtBQUN6RixPQUFPLElBQVU7QUFDakI7QUFDQSxzQkFBc0IsbUJBQU8sQ0FBQyx3V0FBME0sY0FBYyxzREFBc0Q7QUFDNVMsTUFBTSxVQUFVO0FBQ2hCO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBYmhpalxcRG93bmxvYWRzXFxRdWFudG5leC5haVxcbm9kZV9tb2R1bGVzXFwucG5wbVxcbmV4dEAxNS4yLjRfQGJhYmVsK2NvcmVANy4yX2FmNDYyZGEwYjQ0YTY0ZWU0YWM3YzYyY2I2ZjIyNmQ4XFxub2RlX21vZHVsZXNcXG5leHRcXGZvbnRcXGdvb2dsZVxcdGFyZ2V0LmNzcz97XCJwYXRoXCI6XCJhcHBcXGxheW91dC50c3hcIixcImltcG9ydFwiOlwiSW50ZXJcIixcImFyZ3VtZW50c1wiOlt7XCJzdWJzZXRzXCI6W1wibGF0aW5cIl19XSxcInZhcmlhYmxlTmFtZVwiOlwiaW50ZXJcIn18YXBwLXBhZ2VzLWJyb3dzZXIiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gZXh0cmFjdGVkIGJ5IG1pbmktY3NzLWV4dHJhY3QtcGx1Z2luXG5tb2R1bGUuZXhwb3J0cyA9IHtcInN0eWxlXCI6e1wiZm9udEZhbWlseVwiOlwiJ0ludGVyJywgJ0ludGVyIEZhbGxiYWNrJ1wiLFwiZm9udFN0eWxlXCI6XCJub3JtYWxcIn0sXCJjbGFzc05hbWVcIjpcIl9fY2xhc3NOYW1lX2U4Y2UwY1wifTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICAvLyAxNzUxODkxMjUzNTA1XG4gICAgICB2YXIgY3NzUmVsb2FkID0gcmVxdWlyZShcIkM6L1VzZXJzL0FiaGlqL0Rvd25sb2Fkcy9RdWFudG5leC5haS9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfQGJhYmVsK2NvcmVANy4yX2FmNDYyZGEwYjQ0YTY0ZWU0YWM3YzYyY2I2ZjIyNmQ4L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvbWluaS1jc3MtZXh0cmFjdC1wbHVnaW4vaG1yL2hvdE1vZHVsZVJlcGxhY2VtZW50LmpzXCIpKG1vZHVsZS5pZCwge1wicHVibGljUGF0aFwiOlwiL19uZXh0L1wiLFwiZXNNb2R1bGVcIjpmYWxzZSxcImxvY2Fsc1wiOnRydWV9KTtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShjc3NSZWxvYWQpO1xuICAgICAgXG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);