{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "6JlV7agJ9mJJNXJW0+jl/RBNjnoVSi+qEJL8pIdrb9k="}}}, "functions": {}, "sortedMiddleware": ["/"]}