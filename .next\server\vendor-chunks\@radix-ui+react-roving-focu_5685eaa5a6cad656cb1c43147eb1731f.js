"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-roving-focu_5685eaa5a6cad656cb1c43147eb1731f";
exports.ids = ["vendor-chunks/@radix-ui+react-roving-focu_5685eaa5a6cad656cb1c43147eb1731f"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-roving-focu_5685eaa5a6cad656cb1c43147eb1731f/node_modules/@radix-ui/react-roving-focus/dist/index.mjs":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-roving-focu_5685eaa5a6cad656cb1c43147eb1731f/node_modules/@radix-ui/react-roving-focus/dist/index.mjs ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   RovingFocusGroup: () => (/* binding */ RovingFocusGroup),\n/* harmony export */   RovingFocusGroupItem: () => (/* binding */ RovingFocusGroupItem),\n/* harmony export */   createRovingFocusGroupScope: () => (/* binding */ createRovingFocusGroupScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@_651f60b83f43ee655942b219a5b6e6e2/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_a1d2853b5188b26995ccf8fe08b4ac18/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_b6776874cf08148c72027fb114cc7f90/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@19.0.0_react@19.0.0/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_f1559ddf590287c33aa65e2eac7c63be/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callbac_53dba0e53a4329f03ccb2f86db9d934d/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_a1c3931d508de372008f1d96de06e51b/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1_1117bdf9bd5118f735c90c37f510b6ee/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Item,Root,RovingFocusGroup,RovingFocusGroupItem,createRovingFocusGroupScope auto */ // src/roving-focus-group.tsx\n\n\n\n\n\n\n\n\n\n\n\nvar ENTRY_FOCUS = \"rovingFocusGroup.onEntryFocus\";\nvar EVENT_OPTIONS = {\n    bubbles: false,\n    cancelable: true\n};\nvar GROUP_NAME = \"RovingFocusGroup\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__.createCollection)(GROUP_NAME);\nvar [createRovingFocusGroupContext, createRovingFocusGroupScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__.createContextScope)(GROUP_NAME, [\n    createCollectionScope\n]);\nvar [RovingFocusProvider, useRovingFocusContext] = createRovingFocusGroupContext(GROUP_NAME);\nvar RovingFocusGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n        scope: props.__scopeRovingFocusGroup,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n            scope: props.__scopeRovingFocusGroup,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RovingFocusGroupImpl, {\n                ...props,\n                ref: forwardedRef\n            })\n        })\n    });\n});\nRovingFocusGroup.displayName = GROUP_NAME;\nvar RovingFocusGroupImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRovingFocusGroup, orientation, loop = false, dir, currentTabStopId: currentTabStopIdProp, defaultCurrentTabStopId, onCurrentTabStopIdChange, onEntryFocus, preventScrollOnEntryFocus = false, ...groupProps } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_5__.useDirection)(dir);\n    const [currentTabStopId, setCurrentTabStopId] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_6__.useControllableState)({\n        prop: currentTabStopIdProp,\n        defaultProp: defaultCurrentTabStopId ?? null,\n        onChange: onCurrentTabStopIdChange,\n        caller: GROUP_NAME\n    });\n    const [isTabbingBackOut, setIsTabbingBackOut] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const handleEntryFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_7__.useCallbackRef)(onEntryFocus);\n    const getItems = useCollection(__scopeRovingFocusGroup);\n    const isClickFocusRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const [focusableItemsCount, setFocusableItemsCount] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"RovingFocusGroupImpl.useEffect\": ()=>{\n            const node = ref.current;\n            if (node) {\n                node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n                return ({\n                    \"RovingFocusGroupImpl.useEffect\": ()=>node.removeEventListener(ENTRY_FOCUS, handleEntryFocus)\n                })[\"RovingFocusGroupImpl.useEffect\"];\n            }\n        }\n    }[\"RovingFocusGroupImpl.useEffect\"], [\n        handleEntryFocus\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RovingFocusProvider, {\n        scope: __scopeRovingFocusGroup,\n        orientation,\n        dir: direction,\n        loop,\n        currentTabStopId,\n        onItemFocus: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"RovingFocusGroupImpl.useCallback\": (tabStopId)=>setCurrentTabStopId(tabStopId)\n        }[\"RovingFocusGroupImpl.useCallback\"], [\n            setCurrentTabStopId\n        ]),\n        onItemShiftTab: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"RovingFocusGroupImpl.useCallback\": ()=>setIsTabbingBackOut(true)\n        }[\"RovingFocusGroupImpl.useCallback\"], []),\n        onFocusableItemAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"RovingFocusGroupImpl.useCallback\": ()=>setFocusableItemsCount({\n                    \"RovingFocusGroupImpl.useCallback\": (prevCount)=>prevCount + 1\n                }[\"RovingFocusGroupImpl.useCallback\"])\n        }[\"RovingFocusGroupImpl.useCallback\"], []),\n        onFocusableItemRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"RovingFocusGroupImpl.useCallback\": ()=>setFocusableItemsCount({\n                    \"RovingFocusGroupImpl.useCallback\": (prevCount)=>prevCount - 1\n                }[\"RovingFocusGroupImpl.useCallback\"])\n        }[\"RovingFocusGroupImpl.useCallback\"], []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n            tabIndex: isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0,\n            \"data-orientation\": orientation,\n            ...groupProps,\n            ref: composedRefs,\n            style: {\n                outline: \"none\",\n                ...props.style\n            },\n            onMouseDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onMouseDown, ()=>{\n                isClickFocusRef.current = true;\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onFocus, (event)=>{\n                const isKeyboardFocus = !isClickFocusRef.current;\n                if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n                    const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n                    event.currentTarget.dispatchEvent(entryFocusEvent);\n                    if (!entryFocusEvent.defaultPrevented) {\n                        const items = getItems().filter((item)=>item.focusable);\n                        const activeItem = items.find((item)=>item.active);\n                        const currentItem = items.find((item)=>item.id === currentTabStopId);\n                        const candidateItems = [\n                            activeItem,\n                            currentItem,\n                            ...items\n                        ].filter(Boolean);\n                        const candidateNodes = candidateItems.map((item)=>item.ref.current);\n                        focusFirst(candidateNodes, preventScrollOnEntryFocus);\n                    }\n                }\n                isClickFocusRef.current = false;\n            }),\n            onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onBlur, ()=>setIsTabbingBackOut(false))\n        })\n    });\n});\nvar ITEM_NAME = \"RovingFocusGroupItem\";\nvar RovingFocusGroupItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRovingFocusGroup, focusable = true, active = false, tabStopId, children, ...itemProps } = props;\n    const autoId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__.useId)();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n    const { onFocusableItemAdd, onFocusableItemRemove, currentTabStopId } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"RovingFocusGroupItem.useEffect\": ()=>{\n            if (focusable) {\n                onFocusableItemAdd();\n                return ({\n                    \"RovingFocusGroupItem.useEffect\": ()=>onFocusableItemRemove()\n                })[\"RovingFocusGroupItem.useEffect\"];\n            }\n        }\n    }[\"RovingFocusGroupItem.useEffect\"], [\n        focusable,\n        onFocusableItemAdd,\n        onFocusableItemRemove\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.ItemSlot, {\n        scope: __scopeRovingFocusGroup,\n        id,\n        focusable,\n        active,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.span, {\n            tabIndex: isCurrentTabStop ? 0 : -1,\n            \"data-orientation\": context.orientation,\n            ...itemProps,\n            ref: forwardedRef,\n            onMouseDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onMouseDown, (event)=>{\n                if (!focusable) event.preventDefault();\n                else context.onItemFocus(id);\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onFocus, ()=>context.onItemFocus(id)),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                if (event.key === \"Tab\" && event.shiftKey) {\n                    context.onItemShiftTab();\n                    return;\n                }\n                if (event.target !== event.currentTarget) return;\n                const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n                if (focusIntent !== void 0) {\n                    if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) return;\n                    event.preventDefault();\n                    const items = getItems().filter((item)=>item.focusable);\n                    let candidateNodes = items.map((item)=>item.ref.current);\n                    if (focusIntent === \"last\") candidateNodes.reverse();\n                    else if (focusIntent === \"prev\" || focusIntent === \"next\") {\n                        if (focusIntent === \"prev\") candidateNodes.reverse();\n                        const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                        candidateNodes = context.loop ? wrapArray(candidateNodes, currentIndex + 1) : candidateNodes.slice(currentIndex + 1);\n                    }\n                    setTimeout(()=>focusFirst(candidateNodes));\n                }\n            }),\n            children: typeof children === \"function\" ? children({\n                isCurrentTabStop,\n                hasTabStop: currentTabStopId != null\n            }) : children\n        })\n    });\n});\nRovingFocusGroupItem.displayName = ITEM_NAME;\nvar MAP_KEY_TO_FOCUS_INTENT = {\n    ArrowLeft: \"prev\",\n    ArrowUp: \"prev\",\n    ArrowRight: \"next\",\n    ArrowDown: \"next\",\n    PageUp: \"first\",\n    Home: \"first\",\n    PageDown: \"last\",\n    End: \"last\"\n};\nfunction getDirectionAwareKey(key, dir) {\n    if (dir !== \"rtl\") return key;\n    return key === \"ArrowLeft\" ? \"ArrowRight\" : key === \"ArrowRight\" ? \"ArrowLeft\" : key;\n}\nfunction getFocusIntent(event, orientation, dir) {\n    const key = getDirectionAwareKey(event.key, dir);\n    if (orientation === \"vertical\" && [\n        \"ArrowLeft\",\n        \"ArrowRight\"\n    ].includes(key)) return void 0;\n    if (orientation === \"horizontal\" && [\n        \"ArrowUp\",\n        \"ArrowDown\"\n    ].includes(key)) return void 0;\n    return MAP_KEY_TO_FOCUS_INTENT[key];\n}\nfunction focusFirst(candidates, preventScroll = false) {\n    const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n    for (const candidate of candidates){\n        if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n        candidate.focus({\n            preventScroll\n        });\n        if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n    }\n}\nfunction wrapArray(array, startIndex) {\n    return array.map((_, index)=>array[(startIndex + index) % array.length]);\n}\nvar Root = RovingFocusGroup;\nvar Item = RovingFocusGroupItem;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJhZGl4LXVpK3JlYWN0LXJvdmluZy1mb2N1XzU2ODVlYWE1YTZjYWQ2NTZjYjFjNDMxNDdlYjE3MzFmL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3Qtcm92aW5nLWZvY3VzL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXVCO0FBQ2M7QUFDSjtBQUNEO0FBQ0c7QUFDYjtBQUNJO0FBQ0s7QUFDTTtBQUNSO0FBZ0VuQjtBQTVEVixJQUFNLGNBQWM7QUFDcEIsSUFBTSxnQkFBZ0I7SUFBRSxTQUFTO0lBQU8sWUFBWTtBQUFLO0FBTXpELElBQU0sYUFBYTtBQUduQixJQUFNLENBQUMsWUFBWSxlQUFlLHFCQUFxQixJQUFJLDRFQUFnQixDQUd6RSxVQUFVO0FBR1osSUFBTSxDQUFDLCtCQUErQiwyQkFBMkIsSUFBSSwyRUFBa0IsQ0FDckYsWUFDQTtJQUFDLHFCQUFxQjtDQUFBO0FBK0J4QixJQUFNLENBQUMscUJBQXFCLHFCQUFxQixJQUMvQyw4QkFBa0QsVUFBVTtBQUs5RCxJQUFNLGlDQUF5Qiw4Q0FDN0IsQ0FBQyxPQUEyQztJQUMxQyxPQUNFLHVFQUFDLFdBQVcsVUFBWDtRQUFvQixPQUFPLE1BQU07UUFDaEMsaUZBQUMsV0FBVyxNQUFYO1lBQWdCLE9BQU8sTUFBTTtZQUM1QixpRkFBQztnQkFBc0IsR0FBRztnQkFBTyxLQUFLO1lBQUEsQ0FBYztRQUFBLENBQ3REO0lBQUEsQ0FDRjtBQUVKO0FBR0YsaUJBQWlCLGNBQWM7QUFnQi9CLElBQU0scUNBQTZCLDhDQUdqQyxDQUFDLE9BQStDO0lBQ2hELE1BQU0sRUFDSix5QkFDQSxhQUNBLE9BQU8sT0FDUCxLQUNBLGtCQUFrQixzQkFDbEIseUJBQ0EsMEJBQ0EsY0FDQSw0QkFBNEIsT0FDNUIsR0FBRyxZQUNMLEdBQUk7SUFDSixNQUFNLE1BQVksMENBQW9DLElBQUk7SUFDMUQsTUFBTSxlQUFlLDZFQUFlLENBQUMsY0FBYyxHQUFHO0lBQ3RELE1BQU0sWUFBWSx1RUFBWSxDQUFDLEdBQUc7SUFDbEMsTUFBTSxDQUFDLGtCQUFrQixtQkFBbUIsSUFBSSw0RkFBb0IsQ0FBQztRQUNuRSxNQUFNO1FBQ04sYUFBYSwyQkFBMkI7UUFDeEMsVUFBVTtRQUNWLFFBQVE7SUFDVixDQUFDO0lBQ0QsTUFBTSxDQUFDLGtCQUFrQixtQkFBbUIsSUFBVSw0Q0FBUyxLQUFLO0lBQ3BFLE1BQU0sbUJBQW1CLGdGQUFjLENBQUMsWUFBWTtJQUNwRCxNQUFNLFdBQVcsY0FBYyx1QkFBdUI7SUFDdEQsTUFBTSxrQkFBd0IsMENBQU8sS0FBSztJQUMxQyxNQUFNLENBQUMscUJBQXFCLHNCQUFzQixJQUFVLDRDQUFTLENBQUM7SUFFaEU7MENBQVU7WUFDZCxNQUFNLE9BQU8sSUFBSTtZQUNqQixJQUFJLE1BQU07Z0JBQ1IsS0FBSyxpQkFBaUIsYUFBYSxnQkFBZ0I7Z0JBQ25EO3NEQUFPLElBQU0sS0FBSyxvQkFBb0IsYUFBYSxnQkFBZ0I7O1lBQ3JFO1FBQ0Y7eUNBQUc7UUFBQyxnQkFBZ0I7S0FBQztJQUVyQixPQUNFLHVFQUFDO1FBQ0MsT0FBTztRQUNQO1FBQ0EsS0FBSztRQUNMO1FBQ0E7UUFDQSxhQUFtQjtnREFDakIsQ0FBQyxZQUFjLG9CQUFvQixTQUFTOytDQUM1QztZQUFDLG1CQUFtQjtTQUFBO1FBRXRCLGdCQUFzQjtnREFBWSxJQUFNLG9CQUFvQixJQUFJOytDQUFHLENBQUMsQ0FBQztRQUNyRSxvQkFBMEI7Z0RBQ3hCLElBQU07d0RBQXVCLENBQUMsWUFBYyxZQUFZLENBQUM7OytDQUN6RCxDQUFDO1FBRUgsdUJBQTZCO2dEQUMzQixJQUFNO3dEQUF1QixDQUFDLFlBQWMsWUFBWSxDQUFDOzsrQ0FDekQsQ0FBQztRQUdILGlGQUFDLGdFQUFTLENBQUMsS0FBVjtZQUNDLFVBQVUsb0JBQW9CLHdCQUF3QixJQUFJLEtBQUs7WUFDL0Qsb0JBQWtCO1lBQ2pCLEdBQUc7WUFDSixLQUFLO1lBQ0wsT0FBTztnQkFBRSxTQUFTO2dCQUFRLEdBQUcsTUFBTTtZQUFNO1lBQ3pDLGFBQWEseUVBQW9CLENBQUMsTUFBTSxhQUFhO2dCQUNuRCxnQkFBZ0IsVUFBVTtZQUM1QixDQUFDO1lBQ0QsU0FBUyx5RUFBb0IsQ0FBQyxNQUFNLFNBQVMsQ0FBQztnQkFLNUMsTUFBTSxrQkFBa0IsQ0FBQyxnQkFBZ0I7Z0JBRXpDLElBQUksTUFBTSxXQUFXLE1BQU0saUJBQWlCLG1CQUFtQixDQUFDLGtCQUFrQjtvQkFDaEYsTUFBTSxrQkFBa0IsSUFBSSxZQUFZLGFBQWEsYUFBYTtvQkFDbEUsTUFBTSxjQUFjLGNBQWMsZUFBZTtvQkFFakQsSUFBSSxDQUFDLGdCQUFnQixrQkFBa0I7d0JBQ3JDLE1BQU0sUUFBUSxTQUFTLEVBQUUsT0FBTyxDQUFDLE9BQVMsS0FBSyxTQUFTO3dCQUN4RCxNQUFNLGFBQWEsTUFBTSxLQUFLLENBQUMsT0FBUyxLQUFLLE1BQU07d0JBQ25ELE1BQU0sY0FBYyxNQUFNLEtBQUssQ0FBQyxPQUFTLEtBQUssT0FBTyxnQkFBZ0I7d0JBQ3JFLE1BQU0saUJBQWlCOzRCQUFDOzRCQUFZOytCQUFnQixLQUFLO3lCQUFBLENBQUUsT0FDekQ7d0JBRUYsTUFBTSxpQkFBaUIsZUFBZSxJQUFJLENBQUMsT0FBUyxLQUFLLElBQUksT0FBUTt3QkFDckUsV0FBVyxnQkFBZ0IseUJBQXlCO29CQUN0RDtnQkFDRjtnQkFFQSxnQkFBZ0IsVUFBVTtZQUM1QixDQUFDO1lBQ0QsUUFBUSx5RUFBb0IsQ0FBQyxNQUFNLFFBQVEsSUFBTSxvQkFBb0IsS0FBSyxDQUFDO1FBQUE7SUFDN0U7QUFHTixDQUFDO0FBTUQsSUFBTSxZQUFZO0FBYWxCLElBQU0scUNBQTZCLDhDQUNqQyxDQUFDLE9BQTBDO0lBQ3pDLE1BQU0sRUFDSix5QkFDQSxZQUFZLE1BQ1osU0FBUyxPQUNULFdBQ0EsVUFDQSxHQUFHLFdBQ0wsR0FBSTtJQUNKLE1BQU0sU0FBUywwREFBSyxDQUFDO0lBQ3JCLE1BQU0sS0FBSyxhQUFhO0lBQ3hCLE1BQU0sVUFBVSxzQkFBc0IsV0FBVyx1QkFBdUI7SUFDeEUsTUFBTSxtQkFBbUIsUUFBUSxxQkFBcUI7SUFDdEQsTUFBTSxXQUFXLGNBQWMsdUJBQXVCO0lBRXRELE1BQU0sRUFBRSxvQkFBb0IsdUJBQXVCLGlCQUFpQixJQUFJO0lBRWxFOzBDQUFVO1lBQ2QsSUFBSSxXQUFXO2dCQUNiLG1CQUFtQjtnQkFDbkI7c0RBQU8sSUFBTSxzQkFBc0I7O1lBQ3JDO1FBQ0Y7eUNBQUc7UUFBQztRQUFXO1FBQW9CLHFCQUFxQjtLQUFDO0lBRXpELE9BQ0UsdUVBQUMsV0FBVyxVQUFYO1FBQ0MsT0FBTztRQUNQO1FBQ0E7UUFDQTtRQUVBLGlGQUFDLGdFQUFTLENBQUMsTUFBVjtZQUNDLFVBQVUsbUJBQW1CLElBQUk7WUFDakMsb0JBQWtCLFFBQVE7WUFDekIsR0FBRztZQUNKLEtBQUs7WUFDTCxhQUFhLHlFQUFvQixDQUFDLE1BQU0sYUFBYSxDQUFDO2dCQUdwRCxJQUFJLENBQUMsVUFBVyxPQUFNLGVBQWU7cUJBRWhDLFFBQVEsWUFBWSxFQUFFO1lBQzdCLENBQUM7WUFDRCxTQUFTLHlFQUFvQixDQUFDLE1BQU0sU0FBUyxJQUFNLFFBQVEsWUFBWSxFQUFFLENBQUM7WUFDMUUsV0FBVyx5RUFBb0IsQ0FBQyxNQUFNLFdBQVcsQ0FBQztnQkFDaEQsSUFBSSxNQUFNLFFBQVEsU0FBUyxNQUFNLFVBQVU7b0JBQ3pDLFFBQVEsZUFBZTtvQkFDdkI7Z0JBQ0Y7Z0JBRUEsSUFBSSxNQUFNLFdBQVcsTUFBTSxjQUFlO2dCQUUxQyxNQUFNLGNBQWMsZUFBZSxPQUFPLFFBQVEsYUFBYSxRQUFRLEdBQUc7Z0JBRTFFLElBQUksZ0JBQWdCLFFBQVc7b0JBQzdCLElBQUksTUFBTSxXQUFXLE1BQU0sV0FBVyxNQUFNLFVBQVUsTUFBTSxTQUFVO29CQUN0RSxNQUFNLGVBQWU7b0JBQ3JCLE1BQU0sUUFBUSxTQUFTLEVBQUUsT0FBTyxDQUFDLE9BQVMsS0FBSyxTQUFTO29CQUN4RCxJQUFJLGlCQUFpQixNQUFNLElBQUksQ0FBQyxPQUFTLEtBQUssSUFBSSxPQUFRO29CQUUxRCxJQUFJLGdCQUFnQixPQUFRLGdCQUFlLFFBQVE7eUJBQUEsSUFDMUMsZ0JBQWdCLFVBQVUsZ0JBQWdCLFFBQVE7d0JBQ3pELElBQUksZ0JBQWdCLE9BQVEsZ0JBQWUsUUFBUTt3QkFDbkQsTUFBTSxlQUFlLGVBQWUsUUFBUSxNQUFNLGFBQWE7d0JBQy9ELGlCQUFpQixRQUFRLE9BQ3JCLFVBQVUsZ0JBQWdCLGVBQWUsQ0FBQyxJQUMxQyxlQUFlLE1BQU0sZUFBZSxDQUFDO29CQUMzQztvQkFNQSxXQUFXLElBQU0sV0FBVyxjQUFjLENBQUM7Z0JBQzdDO1lBQ0YsQ0FBQztZQUVBLGlCQUFPLGFBQWEsYUFDakIsU0FBUztnQkFBRTtnQkFBa0IsWUFBWSxvQkFBb0I7WUFBSyxDQUFDLElBQ25FO1FBQUE7SUFDTjtBQUdOO0FBR0YscUJBQXFCLGNBQWM7QUFLbkMsSUFBTSwwQkFBdUQ7SUFDM0QsV0FBVztJQUFRLFNBQVM7SUFDNUIsWUFBWTtJQUFRLFdBQVc7SUFDL0IsUUFBUTtJQUFTLE1BQU07SUFDdkIsVUFBVTtJQUFRLEtBQUs7QUFDekI7QUFFQSxTQUFTLHFCQUFxQixLQUFhLEtBQWlCO0lBQzFELElBQUksUUFBUSxNQUFPLFFBQU87SUFDMUIsT0FBTyxRQUFRLGNBQWMsZUFBZSxRQUFRLGVBQWUsY0FBYztBQUNuRjtBQUlBLFNBQVMsZUFBZSxPQUE0QixhQUEyQixLQUFpQjtJQUM5RixNQUFNLE1BQU0scUJBQXFCLE1BQU0sS0FBSyxHQUFHO0lBQy9DLElBQUksZ0JBQWdCLGNBQWM7UUFBQztRQUFhLFlBQVk7S0FBQSxDQUFFLFNBQVMsR0FBRyxFQUFHLFFBQU87SUFDcEYsSUFBSSxnQkFBZ0IsZ0JBQWdCO1FBQUM7UUFBVyxXQUFXO0tBQUEsQ0FBRSxTQUFTLEdBQUcsRUFBRyxRQUFPO0lBQ25GLE9BQU8sd0JBQXdCLEdBQUc7QUFDcEM7QUFFQSxTQUFTLFdBQVcsWUFBMkIsZ0JBQWdCLE9BQU87SUFDcEUsTUFBTSw2QkFBNkIsU0FBUztJQUM1QyxXQUFXLGFBQWEsV0FBWTtRQUVsQyxJQUFJLGNBQWMsMkJBQTRCO1FBQzlDLFVBQVUsTUFBTTtZQUFFO1FBQWMsQ0FBQztRQUNqQyxJQUFJLFNBQVMsa0JBQWtCLDJCQUE0QjtJQUM3RDtBQUNGO0FBTUEsU0FBUyxVQUFhLE9BQVksWUFBb0I7SUFDcEQsT0FBTyxNQUFNLElBQU8sQ0FBQyxHQUFHLFFBQVUsT0FBTyxhQUFhLFNBQVMsTUFBTSxNQUFNLENBQUU7QUFDL0U7QUFFQSxJQUFNLE9BQU87QUFDYixJQUFNLE9BQU8iLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWJoaWpcXERvd25sb2Fkc1xcc3JjXFxyb3ZpbmctZm9jdXMtZ3JvdXAudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGNvbXBvc2VFdmVudEhhbmRsZXJzIH0gZnJvbSAnQHJhZGl4LXVpL3ByaW1pdGl2ZSc7XG5pbXBvcnQgeyBjcmVhdGVDb2xsZWN0aW9uIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LWNvbGxlY3Rpb24nO1xuaW1wb3J0IHsgdXNlQ29tcG9zZWRSZWZzIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LWNvbXBvc2UtcmVmcyc7XG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0U2NvcGUgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtY29udGV4dCc7XG5pbXBvcnQgeyB1c2VJZCB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC1pZCc7XG5pbXBvcnQgeyBQcmltaXRpdmUgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtcHJpbWl0aXZlJztcbmltcG9ydCB7IHVzZUNhbGxiYWNrUmVmIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYnO1xuaW1wb3J0IHsgdXNlQ29udHJvbGxhYmxlU3RhdGUgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtdXNlLWNvbnRyb2xsYWJsZS1zdGF0ZSc7XG5pbXBvcnQgeyB1c2VEaXJlY3Rpb24gfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtZGlyZWN0aW9uJztcblxuaW1wb3J0IHR5cGUgeyBTY29wZSB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC1jb250ZXh0JztcblxuY29uc3QgRU5UUllfRk9DVVMgPSAncm92aW5nRm9jdXNHcm91cC5vbkVudHJ5Rm9jdXMnO1xuY29uc3QgRVZFTlRfT1BUSU9OUyA9IHsgYnViYmxlczogZmFsc2UsIGNhbmNlbGFibGU6IHRydWUgfTtcblxuLyogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICogUm92aW5nRm9jdXNHcm91cFxuICogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xuXG5jb25zdCBHUk9VUF9OQU1FID0gJ1JvdmluZ0ZvY3VzR3JvdXAnO1xuXG50eXBlIEl0ZW1EYXRhID0geyBpZDogc3RyaW5nOyBmb2N1c2FibGU6IGJvb2xlYW47IGFjdGl2ZTogYm9vbGVhbiB9O1xuY29uc3QgW0NvbGxlY3Rpb24sIHVzZUNvbGxlY3Rpb24sIGNyZWF0ZUNvbGxlY3Rpb25TY29wZV0gPSBjcmVhdGVDb2xsZWN0aW9uPFxuICBIVE1MU3BhbkVsZW1lbnQsXG4gIEl0ZW1EYXRhXG4+KEdST1VQX05BTUUpO1xuXG50eXBlIFNjb3BlZFByb3BzPFA+ID0gUCAmIHsgX19zY29wZVJvdmluZ0ZvY3VzR3JvdXA/OiBTY29wZSB9O1xuY29uc3QgW2NyZWF0ZVJvdmluZ0ZvY3VzR3JvdXBDb250ZXh0LCBjcmVhdGVSb3ZpbmdGb2N1c0dyb3VwU2NvcGVdID0gY3JlYXRlQ29udGV4dFNjb3BlKFxuICBHUk9VUF9OQU1FLFxuICBbY3JlYXRlQ29sbGVjdGlvblNjb3BlXVxuKTtcblxudHlwZSBPcmllbnRhdGlvbiA9IFJlYWN0LkFyaWFBdHRyaWJ1dGVzWydhcmlhLW9yaWVudGF0aW9uJ107XG50eXBlIERpcmVjdGlvbiA9ICdsdHInIHwgJ3J0bCc7XG5cbmludGVyZmFjZSBSb3ZpbmdGb2N1c0dyb3VwT3B0aW9ucyB7XG4gIC8qKlxuICAgKiBUaGUgb3JpZW50YXRpb24gb2YgdGhlIGdyb3VwLlxuICAgKiBNYWlubHkgc28gYXJyb3cgbmF2aWdhdGlvbiBpcyBkb25lIGFjY29yZGluZ2x5IChsZWZ0ICYgcmlnaHQgdnMuIHVwICYgZG93bilcbiAgICovXG4gIG9yaWVudGF0aW9uPzogT3JpZW50YXRpb247XG4gIC8qKlxuICAgKiBUaGUgZGlyZWN0aW9uIG9mIG5hdmlnYXRpb24gYmV0d2VlbiBpdGVtcy5cbiAgICovXG4gIGRpcj86IERpcmVjdGlvbjtcbiAgLyoqXG4gICAqIFdoZXRoZXIga2V5Ym9hcmQgbmF2aWdhdGlvbiBzaG91bGQgbG9vcCBhcm91bmRcbiAgICogQGRlZmF1bHRWYWx1ZSBmYWxzZVxuICAgKi9cbiAgbG9vcD86IGJvb2xlYW47XG59XG5cbnR5cGUgUm92aW5nQ29udGV4dFZhbHVlID0gUm92aW5nRm9jdXNHcm91cE9wdGlvbnMgJiB7XG4gIGN1cnJlbnRUYWJTdG9wSWQ6IHN0cmluZyB8IG51bGw7XG4gIG9uSXRlbUZvY3VzKHRhYlN0b3BJZDogc3RyaW5nKTogdm9pZDtcbiAgb25JdGVtU2hpZnRUYWIoKTogdm9pZDtcbiAgb25Gb2N1c2FibGVJdGVtQWRkKCk6IHZvaWQ7XG4gIG9uRm9jdXNhYmxlSXRlbVJlbW92ZSgpOiB2b2lkO1xufTtcblxuY29uc3QgW1JvdmluZ0ZvY3VzUHJvdmlkZXIsIHVzZVJvdmluZ0ZvY3VzQ29udGV4dF0gPVxuICBjcmVhdGVSb3ZpbmdGb2N1c0dyb3VwQ29udGV4dDxSb3ZpbmdDb250ZXh0VmFsdWU+KEdST1VQX05BTUUpO1xuXG50eXBlIFJvdmluZ0ZvY3VzR3JvdXBFbGVtZW50ID0gUm92aW5nRm9jdXNHcm91cEltcGxFbGVtZW50O1xuaW50ZXJmYWNlIFJvdmluZ0ZvY3VzR3JvdXBQcm9wcyBleHRlbmRzIFJvdmluZ0ZvY3VzR3JvdXBJbXBsUHJvcHMge31cblxuY29uc3QgUm92aW5nRm9jdXNHcm91cCA9IFJlYWN0LmZvcndhcmRSZWY8Um92aW5nRm9jdXNHcm91cEVsZW1lbnQsIFJvdmluZ0ZvY3VzR3JvdXBQcm9wcz4oXG4gIChwcm9wczogU2NvcGVkUHJvcHM8Um92aW5nRm9jdXNHcm91cFByb3BzPiwgZm9yd2FyZGVkUmVmKSA9PiB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxDb2xsZWN0aW9uLlByb3ZpZGVyIHNjb3BlPXtwcm9wcy5fX3Njb3BlUm92aW5nRm9jdXNHcm91cH0+XG4gICAgICAgIDxDb2xsZWN0aW9uLlNsb3Qgc2NvcGU9e3Byb3BzLl9fc2NvcGVSb3ZpbmdGb2N1c0dyb3VwfT5cbiAgICAgICAgICA8Um92aW5nRm9jdXNHcm91cEltcGwgey4uLnByb3BzfSByZWY9e2ZvcndhcmRlZFJlZn0gLz5cbiAgICAgICAgPC9Db2xsZWN0aW9uLlNsb3Q+XG4gICAgICA8L0NvbGxlY3Rpb24uUHJvdmlkZXI+XG4gICAgKTtcbiAgfVxuKTtcblxuUm92aW5nRm9jdXNHcm91cC5kaXNwbGF5TmFtZSA9IEdST1VQX05BTUU7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxudHlwZSBSb3ZpbmdGb2N1c0dyb3VwSW1wbEVsZW1lbnQgPSBSZWFjdC5Db21wb25lbnRSZWY8dHlwZW9mIFByaW1pdGl2ZS5kaXY+O1xudHlwZSBQcmltaXRpdmVEaXZQcm9wcyA9IFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgUHJpbWl0aXZlLmRpdj47XG5pbnRlcmZhY2UgUm92aW5nRm9jdXNHcm91cEltcGxQcm9wc1xuICBleHRlbmRzIE9taXQ8UHJpbWl0aXZlRGl2UHJvcHMsICdkaXInPixcbiAgICBSb3ZpbmdGb2N1c0dyb3VwT3B0aW9ucyB7XG4gIGN1cnJlbnRUYWJTdG9wSWQ/OiBzdHJpbmcgfCBudWxsO1xuICBkZWZhdWx0Q3VycmVudFRhYlN0b3BJZD86IHN0cmluZztcbiAgb25DdXJyZW50VGFiU3RvcElkQ2hhbmdlPzogKHRhYlN0b3BJZDogc3RyaW5nIHwgbnVsbCkgPT4gdm9pZDtcbiAgb25FbnRyeUZvY3VzPzogKGV2ZW50OiBFdmVudCkgPT4gdm9pZDtcbiAgcHJldmVudFNjcm9sbE9uRW50cnlGb2N1cz86IGJvb2xlYW47XG59XG5cbmNvbnN0IFJvdmluZ0ZvY3VzR3JvdXBJbXBsID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUm92aW5nRm9jdXNHcm91cEltcGxFbGVtZW50LFxuICBSb3ZpbmdGb2N1c0dyb3VwSW1wbFByb3BzXG4+KChwcm9wczogU2NvcGVkUHJvcHM8Um92aW5nRm9jdXNHcm91cEltcGxQcm9wcz4sIGZvcndhcmRlZFJlZikgPT4ge1xuICBjb25zdCB7XG4gICAgX19zY29wZVJvdmluZ0ZvY3VzR3JvdXAsXG4gICAgb3JpZW50YXRpb24sXG4gICAgbG9vcCA9IGZhbHNlLFxuICAgIGRpcixcbiAgICBjdXJyZW50VGFiU3RvcElkOiBjdXJyZW50VGFiU3RvcElkUHJvcCxcbiAgICBkZWZhdWx0Q3VycmVudFRhYlN0b3BJZCxcbiAgICBvbkN1cnJlbnRUYWJTdG9wSWRDaGFuZ2UsXG4gICAgb25FbnRyeUZvY3VzLFxuICAgIHByZXZlbnRTY3JvbGxPbkVudHJ5Rm9jdXMgPSBmYWxzZSxcbiAgICAuLi5ncm91cFByb3BzXG4gIH0gPSBwcm9wcztcbiAgY29uc3QgcmVmID0gUmVhY3QudXNlUmVmPFJvdmluZ0ZvY3VzR3JvdXBJbXBsRWxlbWVudD4obnVsbCk7XG4gIGNvbnN0IGNvbXBvc2VkUmVmcyA9IHVzZUNvbXBvc2VkUmVmcyhmb3J3YXJkZWRSZWYsIHJlZik7XG4gIGNvbnN0IGRpcmVjdGlvbiA9IHVzZURpcmVjdGlvbihkaXIpO1xuICBjb25zdCBbY3VycmVudFRhYlN0b3BJZCwgc2V0Q3VycmVudFRhYlN0b3BJZF0gPSB1c2VDb250cm9sbGFibGVTdGF0ZSh7XG4gICAgcHJvcDogY3VycmVudFRhYlN0b3BJZFByb3AsXG4gICAgZGVmYXVsdFByb3A6IGRlZmF1bHRDdXJyZW50VGFiU3RvcElkID8/IG51bGwsXG4gICAgb25DaGFuZ2U6IG9uQ3VycmVudFRhYlN0b3BJZENoYW5nZSxcbiAgICBjYWxsZXI6IEdST1VQX05BTUUsXG4gIH0pO1xuICBjb25zdCBbaXNUYWJiaW5nQmFja091dCwgc2V0SXNUYWJiaW5nQmFja091dF0gPSBSZWFjdC51c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IGhhbmRsZUVudHJ5Rm9jdXMgPSB1c2VDYWxsYmFja1JlZihvbkVudHJ5Rm9jdXMpO1xuICBjb25zdCBnZXRJdGVtcyA9IHVzZUNvbGxlY3Rpb24oX19zY29wZVJvdmluZ0ZvY3VzR3JvdXApO1xuICBjb25zdCBpc0NsaWNrRm9jdXNSZWYgPSBSZWFjdC51c2VSZWYoZmFsc2UpO1xuICBjb25zdCBbZm9jdXNhYmxlSXRlbXNDb3VudCwgc2V0Rm9jdXNhYmxlSXRlbXNDb3VudF0gPSBSZWFjdC51c2VTdGF0ZSgwKTtcblxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IG5vZGUgPSByZWYuY3VycmVudDtcbiAgICBpZiAobm9kZSkge1xuICAgICAgbm9kZS5hZGRFdmVudExpc3RlbmVyKEVOVFJZX0ZPQ1VTLCBoYW5kbGVFbnRyeUZvY3VzKTtcbiAgICAgIHJldHVybiAoKSA9PiBub2RlLnJlbW92ZUV2ZW50TGlzdGVuZXIoRU5UUllfRk9DVVMsIGhhbmRsZUVudHJ5Rm9jdXMpO1xuICAgIH1cbiAgfSwgW2hhbmRsZUVudHJ5Rm9jdXNdKTtcblxuICByZXR1cm4gKFxuICAgIDxSb3ZpbmdGb2N1c1Byb3ZpZGVyXG4gICAgICBzY29wZT17X19zY29wZVJvdmluZ0ZvY3VzR3JvdXB9XG4gICAgICBvcmllbnRhdGlvbj17b3JpZW50YXRpb259XG4gICAgICBkaXI9e2RpcmVjdGlvbn1cbiAgICAgIGxvb3A9e2xvb3B9XG4gICAgICBjdXJyZW50VGFiU3RvcElkPXtjdXJyZW50VGFiU3RvcElkfVxuICAgICAgb25JdGVtRm9jdXM9e1JlYWN0LnVzZUNhbGxiYWNrKFxuICAgICAgICAodGFiU3RvcElkKSA9PiBzZXRDdXJyZW50VGFiU3RvcElkKHRhYlN0b3BJZCksXG4gICAgICAgIFtzZXRDdXJyZW50VGFiU3RvcElkXVxuICAgICAgKX1cbiAgICAgIG9uSXRlbVNoaWZ0VGFiPXtSZWFjdC51c2VDYWxsYmFjaygoKSA9PiBzZXRJc1RhYmJpbmdCYWNrT3V0KHRydWUpLCBbXSl9XG4gICAgICBvbkZvY3VzYWJsZUl0ZW1BZGQ9e1JlYWN0LnVzZUNhbGxiYWNrKFxuICAgICAgICAoKSA9PiBzZXRGb2N1c2FibGVJdGVtc0NvdW50KChwcmV2Q291bnQpID0+IHByZXZDb3VudCArIDEpLFxuICAgICAgICBbXVxuICAgICAgKX1cbiAgICAgIG9uRm9jdXNhYmxlSXRlbVJlbW92ZT17UmVhY3QudXNlQ2FsbGJhY2soXG4gICAgICAgICgpID0+IHNldEZvY3VzYWJsZUl0ZW1zQ291bnQoKHByZXZDb3VudCkgPT4gcHJldkNvdW50IC0gMSksXG4gICAgICAgIFtdXG4gICAgICApfVxuICAgID5cbiAgICAgIDxQcmltaXRpdmUuZGl2XG4gICAgICAgIHRhYkluZGV4PXtpc1RhYmJpbmdCYWNrT3V0IHx8IGZvY3VzYWJsZUl0ZW1zQ291bnQgPT09IDAgPyAtMSA6IDB9XG4gICAgICAgIGRhdGEtb3JpZW50YXRpb249e29yaWVudGF0aW9ufVxuICAgICAgICB7Li4uZ3JvdXBQcm9wc31cbiAgICAgICAgcmVmPXtjb21wb3NlZFJlZnN9XG4gICAgICAgIHN0eWxlPXt7IG91dGxpbmU6ICdub25lJywgLi4ucHJvcHMuc3R5bGUgfX1cbiAgICAgICAgb25Nb3VzZURvd249e2NvbXBvc2VFdmVudEhhbmRsZXJzKHByb3BzLm9uTW91c2VEb3duLCAoKSA9PiB7XG4gICAgICAgICAgaXNDbGlja0ZvY3VzUmVmLmN1cnJlbnQgPSB0cnVlO1xuICAgICAgICB9KX1cbiAgICAgICAgb25Gb2N1cz17Y29tcG9zZUV2ZW50SGFuZGxlcnMocHJvcHMub25Gb2N1cywgKGV2ZW50KSA9PiB7XG4gICAgICAgICAgLy8gV2Ugbm9ybWFsbHkgd291bGRuJ3QgbmVlZCB0aGlzIGNoZWNrLCBiZWNhdXNlIHdlIGFscmVhZHkgY2hlY2tcbiAgICAgICAgICAvLyB0aGF0IHRoZSBmb2N1cyBpcyBvbiB0aGUgY3VycmVudCB0YXJnZXQgYW5kIG5vdCBidWJibGluZyB0byBpdC5cbiAgICAgICAgICAvLyBXZSBkbyB0aGlzIGJlY2F1c2UgU2FmYXJpIGRvZXNuJ3QgZm9jdXMgYnV0dG9ucyB3aGVuIGNsaWNrZWQsIGFuZFxuICAgICAgICAgIC8vIGluc3RlYWQsIHRoZSB3cmFwcGVyIHdpbGwgZ2V0IGZvY3VzZWQgYW5kIG5vdCB0aHJvdWdoIGEgYnViYmxpbmcgZXZlbnQuXG4gICAgICAgICAgY29uc3QgaXNLZXlib2FyZEZvY3VzID0gIWlzQ2xpY2tGb2N1c1JlZi5jdXJyZW50O1xuXG4gICAgICAgICAgaWYgKGV2ZW50LnRhcmdldCA9PT0gZXZlbnQuY3VycmVudFRhcmdldCAmJiBpc0tleWJvYXJkRm9jdXMgJiYgIWlzVGFiYmluZ0JhY2tPdXQpIHtcbiAgICAgICAgICAgIGNvbnN0IGVudHJ5Rm9jdXNFdmVudCA9IG5ldyBDdXN0b21FdmVudChFTlRSWV9GT0NVUywgRVZFTlRfT1BUSU9OUyk7XG4gICAgICAgICAgICBldmVudC5jdXJyZW50VGFyZ2V0LmRpc3BhdGNoRXZlbnQoZW50cnlGb2N1c0V2ZW50KTtcblxuICAgICAgICAgICAgaWYgKCFlbnRyeUZvY3VzRXZlbnQuZGVmYXVsdFByZXZlbnRlZCkge1xuICAgICAgICAgICAgICBjb25zdCBpdGVtcyA9IGdldEl0ZW1zKCkuZmlsdGVyKChpdGVtKSA9PiBpdGVtLmZvY3VzYWJsZSk7XG4gICAgICAgICAgICAgIGNvbnN0IGFjdGl2ZUl0ZW0gPSBpdGVtcy5maW5kKChpdGVtKSA9PiBpdGVtLmFjdGl2ZSk7XG4gICAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRJdGVtID0gaXRlbXMuZmluZCgoaXRlbSkgPT4gaXRlbS5pZCA9PT0gY3VycmVudFRhYlN0b3BJZCk7XG4gICAgICAgICAgICAgIGNvbnN0IGNhbmRpZGF0ZUl0ZW1zID0gW2FjdGl2ZUl0ZW0sIGN1cnJlbnRJdGVtLCAuLi5pdGVtc10uZmlsdGVyKFxuICAgICAgICAgICAgICAgIEJvb2xlYW5cbiAgICAgICAgICAgICAgKSBhcyB0eXBlb2YgaXRlbXM7XG4gICAgICAgICAgICAgIGNvbnN0IGNhbmRpZGF0ZU5vZGVzID0gY2FuZGlkYXRlSXRlbXMubWFwKChpdGVtKSA9PiBpdGVtLnJlZi5jdXJyZW50ISk7XG4gICAgICAgICAgICAgIGZvY3VzRmlyc3QoY2FuZGlkYXRlTm9kZXMsIHByZXZlbnRTY3JvbGxPbkVudHJ5Rm9jdXMpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cblxuICAgICAgICAgIGlzQ2xpY2tGb2N1c1JlZi5jdXJyZW50ID0gZmFsc2U7XG4gICAgICAgIH0pfVxuICAgICAgICBvbkJsdXI9e2NvbXBvc2VFdmVudEhhbmRsZXJzKHByb3BzLm9uQmx1ciwgKCkgPT4gc2V0SXNUYWJiaW5nQmFja091dChmYWxzZSkpfVxuICAgICAgLz5cbiAgICA8L1JvdmluZ0ZvY3VzUHJvdmlkZXI+XG4gICk7XG59KTtcblxuLyogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICogUm92aW5nRm9jdXNHcm91cEl0ZW1cbiAqIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuY29uc3QgSVRFTV9OQU1FID0gJ1JvdmluZ0ZvY3VzR3JvdXBJdGVtJztcblxudHlwZSBSb3ZpbmdGb2N1c0l0ZW1FbGVtZW50ID0gUmVhY3QuQ29tcG9uZW50UmVmPHR5cGVvZiBQcmltaXRpdmUuc3Bhbj47XG50eXBlIFByaW1pdGl2ZVNwYW5Qcm9wcyA9IFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgUHJpbWl0aXZlLnNwYW4+O1xuaW50ZXJmYWNlIFJvdmluZ0ZvY3VzSXRlbVByb3BzIGV4dGVuZHMgT21pdDxQcmltaXRpdmVTcGFuUHJvcHMsICdjaGlsZHJlbic+IHtcbiAgdGFiU3RvcElkPzogc3RyaW5nO1xuICBmb2N1c2FibGU/OiBib29sZWFuO1xuICBhY3RpdmU/OiBib29sZWFuO1xuICBjaGlsZHJlbj86XG4gICAgfCBSZWFjdC5SZWFjdE5vZGVcbiAgICB8ICgocHJvcHM6IHsgaGFzVGFiU3RvcDogYm9vbGVhbjsgaXNDdXJyZW50VGFiU3RvcDogYm9vbGVhbiB9KSA9PiBSZWFjdC5SZWFjdE5vZGUpO1xufVxuXG5jb25zdCBSb3ZpbmdGb2N1c0dyb3VwSXRlbSA9IFJlYWN0LmZvcndhcmRSZWY8Um92aW5nRm9jdXNJdGVtRWxlbWVudCwgUm92aW5nRm9jdXNJdGVtUHJvcHM+KFxuICAocHJvcHM6IFNjb3BlZFByb3BzPFJvdmluZ0ZvY3VzSXRlbVByb3BzPiwgZm9yd2FyZGVkUmVmKSA9PiB7XG4gICAgY29uc3Qge1xuICAgICAgX19zY29wZVJvdmluZ0ZvY3VzR3JvdXAsXG4gICAgICBmb2N1c2FibGUgPSB0cnVlLFxuICAgICAgYWN0aXZlID0gZmFsc2UsXG4gICAgICB0YWJTdG9wSWQsXG4gICAgICBjaGlsZHJlbixcbiAgICAgIC4uLml0ZW1Qcm9wc1xuICAgIH0gPSBwcm9wcztcbiAgICBjb25zdCBhdXRvSWQgPSB1c2VJZCgpO1xuICAgIGNvbnN0IGlkID0gdGFiU3RvcElkIHx8IGF1dG9JZDtcbiAgICBjb25zdCBjb250ZXh0ID0gdXNlUm92aW5nRm9jdXNDb250ZXh0KElURU1fTkFNRSwgX19zY29wZVJvdmluZ0ZvY3VzR3JvdXApO1xuICAgIGNvbnN0IGlzQ3VycmVudFRhYlN0b3AgPSBjb250ZXh0LmN1cnJlbnRUYWJTdG9wSWQgPT09IGlkO1xuICAgIGNvbnN0IGdldEl0ZW1zID0gdXNlQ29sbGVjdGlvbihfX3Njb3BlUm92aW5nRm9jdXNHcm91cCk7XG5cbiAgICBjb25zdCB7IG9uRm9jdXNhYmxlSXRlbUFkZCwgb25Gb2N1c2FibGVJdGVtUmVtb3ZlLCBjdXJyZW50VGFiU3RvcElkIH0gPSBjb250ZXh0O1xuXG4gICAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgIGlmIChmb2N1c2FibGUpIHtcbiAgICAgICAgb25Gb2N1c2FibGVJdGVtQWRkKCk7XG4gICAgICAgIHJldHVybiAoKSA9PiBvbkZvY3VzYWJsZUl0ZW1SZW1vdmUoKTtcbiAgICAgIH1cbiAgICB9LCBbZm9jdXNhYmxlLCBvbkZvY3VzYWJsZUl0ZW1BZGQsIG9uRm9jdXNhYmxlSXRlbVJlbW92ZV0pO1xuXG4gICAgcmV0dXJuIChcbiAgICAgIDxDb2xsZWN0aW9uLkl0ZW1TbG90XG4gICAgICAgIHNjb3BlPXtfX3Njb3BlUm92aW5nRm9jdXNHcm91cH1cbiAgICAgICAgaWQ9e2lkfVxuICAgICAgICBmb2N1c2FibGU9e2ZvY3VzYWJsZX1cbiAgICAgICAgYWN0aXZlPXthY3RpdmV9XG4gICAgICA+XG4gICAgICAgIDxQcmltaXRpdmUuc3BhblxuICAgICAgICAgIHRhYkluZGV4PXtpc0N1cnJlbnRUYWJTdG9wID8gMCA6IC0xfVxuICAgICAgICAgIGRhdGEtb3JpZW50YXRpb249e2NvbnRleHQub3JpZW50YXRpb259XG4gICAgICAgICAgey4uLml0ZW1Qcm9wc31cbiAgICAgICAgICByZWY9e2ZvcndhcmRlZFJlZn1cbiAgICAgICAgICBvbk1vdXNlRG93bj17Y29tcG9zZUV2ZW50SGFuZGxlcnMocHJvcHMub25Nb3VzZURvd24sIChldmVudCkgPT4ge1xuICAgICAgICAgICAgLy8gV2UgcHJldmVudCBmb2N1c2luZyBub24tZm9jdXNhYmxlIGl0ZW1zIG9uIGBtb3VzZWRvd25gLlxuICAgICAgICAgICAgLy8gRXZlbiB0aG91Z2ggdGhlIGl0ZW0gaGFzIHRhYkluZGV4PXstMX0sIHRoYXQgb25seSBtZWFucyB0YWtlIGl0IG91dCBvZiB0aGUgdGFiIG9yZGVyLlxuICAgICAgICAgICAgaWYgKCFmb2N1c2FibGUpIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICAvLyBTYWZhcmkgZG9lc24ndCBmb2N1cyBhIGJ1dHRvbiB3aGVuIGNsaWNrZWQgc28gd2UgcnVuIG91ciBsb2dpYyBvbiBtb3VzZWRvd24gYWxzb1xuICAgICAgICAgICAgZWxzZSBjb250ZXh0Lm9uSXRlbUZvY3VzKGlkKTtcbiAgICAgICAgICB9KX1cbiAgICAgICAgICBvbkZvY3VzPXtjb21wb3NlRXZlbnRIYW5kbGVycyhwcm9wcy5vbkZvY3VzLCAoKSA9PiBjb250ZXh0Lm9uSXRlbUZvY3VzKGlkKSl9XG4gICAgICAgICAgb25LZXlEb3duPXtjb21wb3NlRXZlbnRIYW5kbGVycyhwcm9wcy5vbktleURvd24sIChldmVudCkgPT4ge1xuICAgICAgICAgICAgaWYgKGV2ZW50LmtleSA9PT0gJ1RhYicgJiYgZXZlbnQuc2hpZnRLZXkpIHtcbiAgICAgICAgICAgICAgY29udGV4dC5vbkl0ZW1TaGlmdFRhYigpO1xuICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIGlmIChldmVudC50YXJnZXQgIT09IGV2ZW50LmN1cnJlbnRUYXJnZXQpIHJldHVybjtcblxuICAgICAgICAgICAgY29uc3QgZm9jdXNJbnRlbnQgPSBnZXRGb2N1c0ludGVudChldmVudCwgY29udGV4dC5vcmllbnRhdGlvbiwgY29udGV4dC5kaXIpO1xuXG4gICAgICAgICAgICBpZiAoZm9jdXNJbnRlbnQgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICBpZiAoZXZlbnQubWV0YUtleSB8fCBldmVudC5jdHJsS2V5IHx8IGV2ZW50LmFsdEtleSB8fCBldmVudC5zaGlmdEtleSkgcmV0dXJuO1xuICAgICAgICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgICBjb25zdCBpdGVtcyA9IGdldEl0ZW1zKCkuZmlsdGVyKChpdGVtKSA9PiBpdGVtLmZvY3VzYWJsZSk7XG4gICAgICAgICAgICAgIGxldCBjYW5kaWRhdGVOb2RlcyA9IGl0ZW1zLm1hcCgoaXRlbSkgPT4gaXRlbS5yZWYuY3VycmVudCEpO1xuXG4gICAgICAgICAgICAgIGlmIChmb2N1c0ludGVudCA9PT0gJ2xhc3QnKSBjYW5kaWRhdGVOb2Rlcy5yZXZlcnNlKCk7XG4gICAgICAgICAgICAgIGVsc2UgaWYgKGZvY3VzSW50ZW50ID09PSAncHJldicgfHwgZm9jdXNJbnRlbnQgPT09ICduZXh0Jykge1xuICAgICAgICAgICAgICAgIGlmIChmb2N1c0ludGVudCA9PT0gJ3ByZXYnKSBjYW5kaWRhdGVOb2Rlcy5yZXZlcnNlKCk7XG4gICAgICAgICAgICAgICAgY29uc3QgY3VycmVudEluZGV4ID0gY2FuZGlkYXRlTm9kZXMuaW5kZXhPZihldmVudC5jdXJyZW50VGFyZ2V0KTtcbiAgICAgICAgICAgICAgICBjYW5kaWRhdGVOb2RlcyA9IGNvbnRleHQubG9vcFxuICAgICAgICAgICAgICAgICAgPyB3cmFwQXJyYXkoY2FuZGlkYXRlTm9kZXMsIGN1cnJlbnRJbmRleCArIDEpXG4gICAgICAgICAgICAgICAgICA6IGNhbmRpZGF0ZU5vZGVzLnNsaWNlKGN1cnJlbnRJbmRleCArIDEpO1xuICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgICAqIEltcGVyYXRpdmUgZm9jdXMgZHVyaW5nIGtleWRvd24gaXMgcmlza3kgc28gd2UgcHJldmVudCBSZWFjdCdzIGJhdGNoaW5nIHVwZGF0ZXNcbiAgICAgICAgICAgICAgICogdG8gYXZvaWQgcG90ZW50aWFsIGJ1Z3MuIFNlZTogaHR0cHM6Ly9naXRodWIuY29tL2ZhY2Vib29rL3JlYWN0L2lzc3Vlcy8yMDMzMlxuICAgICAgICAgICAgICAgKi9cbiAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiBmb2N1c0ZpcnN0KGNhbmRpZGF0ZU5vZGVzKSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSl9XG4gICAgICAgID5cbiAgICAgICAgICB7dHlwZW9mIGNoaWxkcmVuID09PSAnZnVuY3Rpb24nXG4gICAgICAgICAgICA/IGNoaWxkcmVuKHsgaXNDdXJyZW50VGFiU3RvcCwgaGFzVGFiU3RvcDogY3VycmVudFRhYlN0b3BJZCAhPSBudWxsIH0pXG4gICAgICAgICAgICA6IGNoaWxkcmVufVxuICAgICAgICA8L1ByaW1pdGl2ZS5zcGFuPlxuICAgICAgPC9Db2xsZWN0aW9uLkl0ZW1TbG90PlxuICAgICk7XG4gIH1cbik7XG5cblJvdmluZ0ZvY3VzR3JvdXBJdGVtLmRpc3BsYXlOYW1lID0gSVRFTV9OQU1FO1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbi8vIHByZXR0aWVyLWlnbm9yZVxuY29uc3QgTUFQX0tFWV9UT19GT0NVU19JTlRFTlQ6IFJlY29yZDxzdHJpbmcsIEZvY3VzSW50ZW50PiA9IHtcbiAgQXJyb3dMZWZ0OiAncHJldicsIEFycm93VXA6ICdwcmV2JyxcbiAgQXJyb3dSaWdodDogJ25leHQnLCBBcnJvd0Rvd246ICduZXh0JyxcbiAgUGFnZVVwOiAnZmlyc3QnLCBIb21lOiAnZmlyc3QnLFxuICBQYWdlRG93bjogJ2xhc3QnLCBFbmQ6ICdsYXN0Jyxcbn07XG5cbmZ1bmN0aW9uIGdldERpcmVjdGlvbkF3YXJlS2V5KGtleTogc3RyaW5nLCBkaXI/OiBEaXJlY3Rpb24pIHtcbiAgaWYgKGRpciAhPT0gJ3J0bCcpIHJldHVybiBrZXk7XG4gIHJldHVybiBrZXkgPT09ICdBcnJvd0xlZnQnID8gJ0Fycm93UmlnaHQnIDoga2V5ID09PSAnQXJyb3dSaWdodCcgPyAnQXJyb3dMZWZ0JyA6IGtleTtcbn1cblxudHlwZSBGb2N1c0ludGVudCA9ICdmaXJzdCcgfCAnbGFzdCcgfCAncHJldicgfCAnbmV4dCc7XG5cbmZ1bmN0aW9uIGdldEZvY3VzSW50ZW50KGV2ZW50OiBSZWFjdC5LZXlib2FyZEV2ZW50LCBvcmllbnRhdGlvbj86IE9yaWVudGF0aW9uLCBkaXI/OiBEaXJlY3Rpb24pIHtcbiAgY29uc3Qga2V5ID0gZ2V0RGlyZWN0aW9uQXdhcmVLZXkoZXZlbnQua2V5LCBkaXIpO1xuICBpZiAob3JpZW50YXRpb24gPT09ICd2ZXJ0aWNhbCcgJiYgWydBcnJvd0xlZnQnLCAnQXJyb3dSaWdodCddLmluY2x1ZGVzKGtleSkpIHJldHVybiB1bmRlZmluZWQ7XG4gIGlmIChvcmllbnRhdGlvbiA9PT0gJ2hvcml6b250YWwnICYmIFsnQXJyb3dVcCcsICdBcnJvd0Rvd24nXS5pbmNsdWRlcyhrZXkpKSByZXR1cm4gdW5kZWZpbmVkO1xuICByZXR1cm4gTUFQX0tFWV9UT19GT0NVU19JTlRFTlRba2V5XTtcbn1cblxuZnVuY3Rpb24gZm9jdXNGaXJzdChjYW5kaWRhdGVzOiBIVE1MRWxlbWVudFtdLCBwcmV2ZW50U2Nyb2xsID0gZmFsc2UpIHtcbiAgY29uc3QgUFJFVklPVVNMWV9GT0NVU0VEX0VMRU1FTlQgPSBkb2N1bWVudC5hY3RpdmVFbGVtZW50O1xuICBmb3IgKGNvbnN0IGNhbmRpZGF0ZSBvZiBjYW5kaWRhdGVzKSB7XG4gICAgLy8gaWYgZm9jdXMgaXMgYWxyZWFkeSB3aGVyZSB3ZSB3YW50IHRvIGdvLCB3ZSBkb24ndCB3YW50IHRvIGtlZXAgZ29pbmcgdGhyb3VnaCB0aGUgY2FuZGlkYXRlc1xuICAgIGlmIChjYW5kaWRhdGUgPT09IFBSRVZJT1VTTFlfRk9DVVNFRF9FTEVNRU5UKSByZXR1cm47XG4gICAgY2FuZGlkYXRlLmZvY3VzKHsgcHJldmVudFNjcm9sbCB9KTtcbiAgICBpZiAoZG9jdW1lbnQuYWN0aXZlRWxlbWVudCAhPT0gUFJFVklPVVNMWV9GT0NVU0VEX0VMRU1FTlQpIHJldHVybjtcbiAgfVxufVxuXG4vKipcbiAqIFdyYXBzIGFuIGFycmF5IGFyb3VuZCBpdHNlbGYgYXQgYSBnaXZlbiBzdGFydCBpbmRleFxuICogRXhhbXBsZTogYHdyYXBBcnJheShbJ2EnLCAnYicsICdjJywgJ2QnXSwgMikgPT09IFsnYycsICdkJywgJ2EnLCAnYiddYFxuICovXG5mdW5jdGlvbiB3cmFwQXJyYXk8VD4oYXJyYXk6IFRbXSwgc3RhcnRJbmRleDogbnVtYmVyKSB7XG4gIHJldHVybiBhcnJheS5tYXA8VD4oKF8sIGluZGV4KSA9PiBhcnJheVsoc3RhcnRJbmRleCArIGluZGV4KSAlIGFycmF5Lmxlbmd0aF0hKTtcbn1cblxuY29uc3QgUm9vdCA9IFJvdmluZ0ZvY3VzR3JvdXA7XG5jb25zdCBJdGVtID0gUm92aW5nRm9jdXNHcm91cEl0ZW07XG5cbmV4cG9ydCB7XG4gIGNyZWF0ZVJvdmluZ0ZvY3VzR3JvdXBTY29wZSxcbiAgLy9cbiAgUm92aW5nRm9jdXNHcm91cCxcbiAgUm92aW5nRm9jdXNHcm91cEl0ZW0sXG4gIC8vXG4gIFJvb3QsXG4gIEl0ZW0sXG59O1xuZXhwb3J0IHR5cGUgeyBSb3ZpbmdGb2N1c0dyb3VwUHJvcHMsIFJvdmluZ0ZvY3VzSXRlbVByb3BzIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-roving-focu_5685eaa5a6cad656cb1c43147eb1731f/node_modules/@radix-ui/react-roving-focus/dist/index.mjs\n");

/***/ })

};
;