/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fd9c2a4e3ff9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWJoaWpcXERvd25sb2Fkc1xcUXVhbnRuZXguYWlcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJmZDljMmE0ZTNmZjlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/auth-context */ \"(rsc)/./contexts/auth-context.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"Quant-NEX - AI-Powered Medical Diagnosis Platform\",\n    description: \"Revolutionary AI-powered platform for brain tumor diagnosis, treatment planning, and patient monitoring. Trusted by healthcare professionals worldwide.\",\n    keywords: \"AI medical diagnosis, brain tumor detection, healthcare technology, medical AI, patient monitoring, treatment planning\",\n    authors: [\n        {\n            name: \"Quant-NEX Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    robots: \"index, follow\",\n    openGraph: {\n        title: \"Quant-NEX - AI-Powered Medical Diagnosis Platform\",\n        description: \"Revolutionary AI-powered platform for brain tumor diagnosis, treatment planning, and patient monitoring.\",\n        type: \"website\",\n        url: \"https://quantnex.com\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"Quant-NEX AI Medical Platform\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Quant-NEX - AI-Powered Medical Diagnosis Platform\",\n        description: \"Revolutionary AI-powered platform for brain tumor diagnosis, treatment planning, and patient monitoring.\",\n        images: [\n            \"/og-image.jpg\"\n        ]\n    },\n    generator: 'v0.dev'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\app\\\\layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\app\\\\layout.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#0d9488\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\app\\\\layout.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\app\\\\layout.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} antialiased`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\app\\\\layout.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\app\\\\layout.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\app\\\\layout.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/loading.tsx":
/*!*************************!*\
  !*** ./app/loading.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_page_loading__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/page-loading */ \"(rsc)/./components/ui/page-loading.tsx\");\n\n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_page_loading__WEBPACK_IMPORTED_MODULE_1__.PageLoading, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\app\\\\loading.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbG9hZGluZy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBMEQ7QUFFM0MsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELG9FQUFXQTs7Ozs7QUFDckIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWJoaWpcXERvd25sb2Fkc1xcUXVhbnRuZXguYWlcXGFwcFxcbG9hZGluZy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZUxvYWRpbmcgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3BhZ2UtbG9hZGluZ1wiXHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2FkaW5nKCkge1xyXG4gIHJldHVybiA8UGFnZUxvYWRpbmcgLz5cclxufVxyXG4iXSwibmFtZXMiOlsiUGFnZUxvYWRpbmciLCJMb2FkaW5nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./components/ui/loading-spinner.tsx":
/*!*******************************************!*\
  !*** ./components/ui/loading-spinner.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingSpinner: () => (/* binding */ LoadingSpinner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(rsc)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n\n\nfunction LoadingSpinner({ size = \"default\", text = \"Loading...\" }) {\n    const sizeClasses = {\n        sm: \"h-4 w-4\",\n        default: \"h-8 w-8\",\n        lg: \"h-12 w-12\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center p-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                className: `${sizeClasses[size]} animate-spin text-purple-500`\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\loading-spinner.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-4 text-gray-400\",\n                children: text\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\loading-spinner.tsx\",\n                lineNumber: 16,\n                columnNumber: 16\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\loading-spinner.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9jb21wb25lbnRzL3VpL2xvYWRpbmctc3Bpbm5lci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBc0M7QUFFL0IsU0FBU0MsZUFBZSxFQUM3QkMsT0FBTyxTQUFTLEVBQ2hCQyxPQUFPLFlBQVksRUFDK0I7SUFDbEQsTUFBTUMsY0FBYztRQUNsQkMsSUFBSTtRQUNKQyxTQUFTO1FBQ1RDLElBQUk7SUFDTjtJQUVBLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ1QsbUZBQU9BO2dCQUFDUyxXQUFXLEdBQUdMLFdBQVcsQ0FBQ0YsS0FBSyxDQUFDLDZCQUE2QixDQUFDOzs7Ozs7WUFDdEVDLHNCQUFRLDhEQUFDTztnQkFBRUQsV0FBVTswQkFBc0JOOzs7Ozs7Ozs7Ozs7QUFHbEQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWJoaWpcXERvd25sb2Fkc1xcUXVhbnRuZXguYWlcXGNvbXBvbmVudHNcXHVpXFxsb2FkaW5nLXNwaW5uZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IExvYWRlcjIgfSBmcm9tIFwibHVjaWRlLXJlYWN0XCJcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBMb2FkaW5nU3Bpbm5lcih7XHJcbiAgc2l6ZSA9IFwiZGVmYXVsdFwiLFxyXG4gIHRleHQgPSBcIkxvYWRpbmcuLi5cIixcclxufTogeyBzaXplPzogXCJzbVwiIHwgXCJkZWZhdWx0XCIgfCBcImxnXCI7IHRleHQ/OiBzdHJpbmcgfSkge1xyXG4gIGNvbnN0IHNpemVDbGFzc2VzID0ge1xyXG4gICAgc206IFwiaC00IHctNFwiLFxyXG4gICAgZGVmYXVsdDogXCJoLTggdy04XCIsXHJcbiAgICBsZzogXCJoLTEyIHctMTJcIixcclxuICB9XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHAtOFwiPlxyXG4gICAgICA8TG9hZGVyMiBjbGFzc05hbWU9e2Ake3NpemVDbGFzc2VzW3NpemVdfSBhbmltYXRlLXNwaW4gdGV4dC1wdXJwbGUtNTAwYH0gLz5cclxuICAgICAge3RleHQgJiYgPHAgY2xhc3NOYW1lPVwibXQtNCB0ZXh0LWdyYXktNDAwXCI+e3RleHR9PC9wPn1cclxuICAgIDwvZGl2PlxyXG4gIClcclxufVxyXG4iXSwibmFtZXMiOlsiTG9hZGVyMiIsIkxvYWRpbmdTcGlubmVyIiwic2l6ZSIsInRleHQiLCJzaXplQ2xhc3NlcyIsInNtIiwiZGVmYXVsdCIsImxnIiwiZGl2IiwiY2xhc3NOYW1lIiwicCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./components/ui/loading-spinner.tsx\n");

/***/ }),

/***/ "(rsc)/./components/ui/page-loading.tsx":
/*!****************************************!*\
  !*** ./components/ui/page-loading.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PageLoading: () => (/* binding */ PageLoading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _loading_spinner__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./loading-spinner */ \"(rsc)/./components/ui/loading-spinner.tsx\");\n\n\nfunction PageLoading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-black/80 p-8 rounded-xl border border-white/10 shadow-xl\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_loading_spinner__WEBPACK_IMPORTED_MODULE_1__.LoadingSpinner, {\n                size: \"lg\",\n                text: \"Loading page...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\page-loading.tsx\",\n                lineNumber: 7,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\page-loading.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\components\\\\ui\\\\page-loading.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9jb21wb25lbnRzL3VpL3BhZ2UtbG9hZGluZy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBa0Q7QUFFM0MsU0FBU0M7SUFDZCxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0gsNERBQWNBO2dCQUFDSSxNQUFLO2dCQUFLQyxNQUFLOzs7Ozs7Ozs7Ozs7Ozs7O0FBSXZDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFiaGlqXFxEb3dubG9hZHNcXFF1YW50bmV4LmFpXFxjb21wb25lbnRzXFx1aVxccGFnZS1sb2FkaW5nLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBMb2FkaW5nU3Bpbm5lciB9IGZyb20gXCIuL2xvYWRpbmctc3Bpbm5lclwiXHJcblxyXG5leHBvcnQgZnVuY3Rpb24gUGFnZUxvYWRpbmcoKSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBiZy1ibGFjay81MCBiYWNrZHJvcC1ibHVyLXNtIHotNTBcIj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ibGFjay84MCBwLTggcm91bmRlZC14bCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIHNoYWRvdy14bFwiPlxyXG4gICAgICAgIDxMb2FkaW5nU3Bpbm5lciBzaXplPVwibGdcIiB0ZXh0PVwiTG9hZGluZyBwYWdlLi4uXCIgLz5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApXHJcbn1cclxuIl0sIm5hbWVzIjpbIkxvYWRpbmdTcGlubmVyIiwiUGFnZUxvYWRpbmciLCJkaXYiLCJjbGFzc05hbWUiLCJzaXplIiwidGV4dCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./components/ui/page-loading.tsx\n");

/***/ }),

/***/ "(rsc)/./contexts/auth-context.tsx":
/*!***********************************!*\
  !*** ./contexts/auth-context.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Downloads\\Quantnex.ai\\contexts\\auth-context.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Downloads\\Quantnex.ai\\contexts\\auth-context.tsx",
"useAuth",
);

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2F.pnpm%2Fnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CAbhij%5CDownloads%5CQuantnex.ai%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbhij%5CDownloads%5CQuantnex.ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2F.pnpm%2Fnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CAbhij%5CDownloads%5CQuantnex.ai%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbhij%5CDownloads%5CQuantnex.ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/module.compiled.js?5dcf\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/loading.tsx */ \"(rsc)/./app/loading.tsx\"));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\app\\\\layout.tsx\"],\n'loading': [module2, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\app\\\\loading.tsx\"],\n'not-found': [module3, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module4, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module5, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2F.pnpm%2Fnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CAbhij%5CDownloads%5CQuantnex.ai%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbhij%5CDownloads%5CQuantnex.ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/auth-context.tsx */ \"(rsc)/./contexts/auth-context.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfQGJhYmVsK2NvcmVANy4yX2FmNDYyZGEwYjQ0YTY0ZWU0YWM3YzYyY2I2ZjIyNmQ4L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQWJoaWolNUMlNUNEb3dubG9hZHMlNUMlNUNRdWFudG5leC5haSU1QyU1Q2NvbnRleHRzJTVDJTVDYXV0aC1jb250ZXh0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkF1dGhQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBYmhpaiU1QyU1Q0Rvd25sb2FkcyU1QyU1Q1F1YW50bmV4LmFpJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDLnBucG0lNUMlNUNuZXh0JTQwMTUuMi40XyU0MGJhYmVsJTJCY29yZSU0MDcuMl9hZjQ2MmRhMGI0NGE2NGVlNGFjN2M2MmNiNmYyMjZkOCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBYmhpaiU1QyU1Q0Rvd25sb2FkcyU1QyU1Q1F1YW50bmV4LmFpJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUEySSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcQWJoaWpcXFxcRG93bmxvYWRzXFxcXFF1YW50bmV4LmFpXFxcXGNvbnRleHRzXFxcXGF1dGgtY29udGV4dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \**************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./contexts/auth-context.tsx":
/*!***********************************!*\
  !*** ./contexts/auth-context.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_indian_backend_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/indian-backend-service */ \"(ssr)/./lib/indian-backend-service.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Check for existing user session\n            const checkAuth = {\n                \"AuthProvider.useEffect.checkAuth\": async ()=>{\n                    try {\n                        const currentUser = await _lib_indian_backend_service__WEBPACK_IMPORTED_MODULE_2__.indianBackendService.getCurrentUser();\n                        if (currentUser) {\n                            setUser(currentUser);\n                        }\n                    } catch (error) {\n                        console.error(\"Auth check error:\", error);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkAuth\"];\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = (userData)=>{\n        setUser(userData);\n        if (false) {}\n    };\n    const logout = async ()=>{\n        try {\n            await _lib_indian_backend_service__WEBPACK_IMPORTED_MODULE_2__.indianBackendService.logout();\n            setUser(null);\n            if (false) {}\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        }\n    };\n    const value = {\n        user,\n        isLoading,\n        login,\n        logout,\n        isAuthenticated: !!user\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Quantnex.ai\\\\contexts\\\\auth-context.tsx\",\n        lineNumber: 67,\n        columnNumber: 10\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/auth-context.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/indian-backend-service.ts":
/*!***************************************!*\
  !*** ./lib/indian-backend-service.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   indianBackendService: () => (/* binding */ indianBackendService)\n/* harmony export */ });\n// Enhanced Indian Backend Service with proper error handling\n// Demo users for fallback authentication\nconst DEMO_USERS = [\n    {\n        id: \"demo-1\",\n        email: \"<EMAIL>\",\n        name: \"Dr. Priya Patel\",\n        role: \"Oncologist\",\n        hospital: \"Tata Memorial Hospital\",\n        department: \"Neuro-Oncology\"\n    },\n    {\n        id: \"demo-2\",\n        email: \"<EMAIL>\",\n        name: \"Dr. Amit Gupta\",\n        role: \"Surgeon\",\n        hospital: \"AIIMS Delhi\",\n        department: \"Cancer Surgery\"\n    },\n    {\n        id: \"demo-3\",\n        email: \"<EMAIL>\",\n        name: \"Rajesh Kumar\",\n        role: \"Administrator\",\n        hospital: \"Apollo Hospital\",\n        department: \"Administration\"\n    }\n];\nclass IndianBackendService {\n    constructor(){\n        this.baseUrl = process.env.NEXT_PUBLIC_API_URL || \"https://api.quantnex.in\";\n        this.isDemoMode = false;\n        // Check if we're in demo mode (development or Firebase not configured)\n        this.isDemoMode =  true || 0;\n    }\n    async login(credentials) {\n        try {\n            // If Firebase is not configured or we're in demo mode, use demo authentication\n            if (this.isDemoMode) {\n                return this.demoLogin(credentials);\n            }\n            // Try Firebase authentication first\n            try {\n                const { signInWithEmailAndPassword } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/tslib@2.8.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+auth@1.10.8_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+util@1.12.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+component@0.6.18\"), __webpack_require__.e(\"vendor-chunks/idb@7.1.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+logger@0.4.4\"), __webpack_require__.e(\"vendor-chunks/firebase@11.10.0\")]).then(__webpack_require__.bind(__webpack_require__, /*! firebase/auth */ \"(ssr)/./node_modules/.pnpm/firebase@11.10.0/node_modules/firebase/auth/dist/index.mjs\"));\n                const { auth } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/tslib@2.8.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+auth@1.10.8_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+util@1.12.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+component@0.6.18\"), __webpack_require__.e(\"vendor-chunks/idb@7.1.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+logger@0.4.4\"), __webpack_require__.e(\"vendor-chunks/firebase@11.10.0\"), __webpack_require__.e(\"vendor-chunks/@grpc+grpc-js@1.9.15\"), __webpack_require__.e(\"vendor-chunks/protobufjs@7.5.3\"), __webpack_require__.e(\"vendor-chunks/@grpc+proto-loader@0.7.15\"), __webpack_require__.e(\"vendor-chunks/@firebase+webchannel-wrapper@1.0.3\"), __webpack_require__.e(\"vendor-chunks/@firebase+storage@0.13.14_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+firestore@4.8.0_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/long@5.3.2\"), __webpack_require__.e(\"vendor-chunks/lodash.camelcase@4.3.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+utf8@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+pool@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+path@1.1.2\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+inquire@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+float@1.0.2\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+fetch@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+eventemitter@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+codegen@2.0.4\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+base64@1.1.2\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+aspromise@1.1.2\"), __webpack_require__.e(\"_ssr_lib_firebase_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! ./firebase */ \"(ssr)/./lib/firebase.ts\"));\n                const userCredential = await signInWithEmailAndPassword(auth, credentials.email, credentials.password);\n                return {\n                    success: true,\n                    user: {\n                        id: userCredential.user.uid,\n                        email: userCredential.user.email || \"\",\n                        name: userCredential.user.displayName || \"User\",\n                        role: \"Doctor\"\n                    },\n                    token: await userCredential.user.getIdToken()\n                };\n            } catch (firebaseError) {\n                console.warn(\"Firebase authentication failed, falling back to demo mode:\", firebaseError.message);\n                // If Firebase fails, fall back to demo mode\n                return this.demoLogin(credentials);\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            // Final fallback to demo mode\n            return this.demoLogin(credentials);\n        }\n    }\n    async demoLogin(credentials) {\n        // Simulate API delay\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        // Check demo credentials\n        const user = DEMO_USERS.find((u)=>u.email === credentials.email);\n        if (user && (credentials.password === \"demo123\" || credentials.password === \"password\")) {\n            return {\n                success: true,\n                user,\n                token: `demo-token-${user.id}-${Date.now()}`\n            };\n        }\n        // If no match found, provide helpful demo credentials\n        return {\n            success: false,\n            error: `Invalid credentials. Demo accounts available:\n      \n      • <EMAIL> / demo123\n      • <EMAIL> / demo123  \n      • <EMAIL> / demo123\n      \n      Or use any email with password: demo123`\n        };\n    }\n    async googleSignIn() {\n        try {\n            if (this.isDemoMode) {\n                // Demo Google sign-in\n                await new Promise((resolve)=>setTimeout(resolve, 1500));\n                return {\n                    success: true,\n                    user: {\n                        id: \"google-demo-1\",\n                        email: \"<EMAIL>\",\n                        name: \"Dr. Kavya Sharma\",\n                        role: \"Radiologist\",\n                        hospital: \"Apollo Hospital\",\n                        department: \"Medical Imaging\"\n                    },\n                    token: `google-demo-token-${Date.now()}`\n                };\n            }\n            // Try Firebase Google authentication\n            try {\n                const { signInWithPopup, GoogleAuthProvider } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/tslib@2.8.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+auth@1.10.8_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+util@1.12.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+component@0.6.18\"), __webpack_require__.e(\"vendor-chunks/idb@7.1.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+logger@0.4.4\"), __webpack_require__.e(\"vendor-chunks/firebase@11.10.0\")]).then(__webpack_require__.bind(__webpack_require__, /*! firebase/auth */ \"(ssr)/./node_modules/.pnpm/firebase@11.10.0/node_modules/firebase/auth/dist/index.mjs\"));\n                const { auth } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/tslib@2.8.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+auth@1.10.8_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+util@1.12.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+component@0.6.18\"), __webpack_require__.e(\"vendor-chunks/idb@7.1.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+logger@0.4.4\"), __webpack_require__.e(\"vendor-chunks/firebase@11.10.0\"), __webpack_require__.e(\"vendor-chunks/@grpc+grpc-js@1.9.15\"), __webpack_require__.e(\"vendor-chunks/protobufjs@7.5.3\"), __webpack_require__.e(\"vendor-chunks/@grpc+proto-loader@0.7.15\"), __webpack_require__.e(\"vendor-chunks/@firebase+webchannel-wrapper@1.0.3\"), __webpack_require__.e(\"vendor-chunks/@firebase+storage@0.13.14_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+firestore@4.8.0_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/long@5.3.2\"), __webpack_require__.e(\"vendor-chunks/lodash.camelcase@4.3.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+utf8@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+pool@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+path@1.1.2\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+inquire@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+float@1.0.2\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+fetch@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+eventemitter@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+codegen@2.0.4\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+base64@1.1.2\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+aspromise@1.1.2\"), __webpack_require__.e(\"_ssr_lib_firebase_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! ./firebase */ \"(ssr)/./lib/firebase.ts\"));\n                const provider = new GoogleAuthProvider();\n                provider.addScope(\"email\");\n                provider.addScope(\"profile\");\n                const result = await signInWithPopup(auth, provider);\n                return {\n                    success: true,\n                    user: {\n                        id: result.user.uid,\n                        email: result.user.email || \"\",\n                        name: result.user.displayName || \"User\",\n                        role: \"Doctor\"\n                    },\n                    token: await result.user.getIdToken()\n                };\n            } catch (firebaseError) {\n                console.warn(\"Firebase Google sign-in failed, using demo mode:\", firebaseError.message);\n                // Fallback to demo Google sign-in\n                return {\n                    success: true,\n                    user: {\n                        id: \"google-demo-fallback\",\n                        email: \"<EMAIL>\",\n                        name: \"Dr. Arjun Singh\",\n                        role: \"Oncologist\",\n                        hospital: \"Fortis Hospital\",\n                        department: \"Medical Oncology\"\n                    },\n                    token: `google-demo-fallback-${Date.now()}`\n                };\n            }\n        } catch (error) {\n            console.error(\"Google sign-in error:\", error);\n            return {\n                success: false,\n                error: \"Google sign-in failed. Please try email/password login or contact support.\"\n            };\n        }\n    }\n    async logout() {\n        try {\n            if (!this.isDemoMode) {\n                const { signOut } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/tslib@2.8.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+auth@1.10.8_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+util@1.12.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+component@0.6.18\"), __webpack_require__.e(\"vendor-chunks/idb@7.1.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+logger@0.4.4\"), __webpack_require__.e(\"vendor-chunks/firebase@11.10.0\")]).then(__webpack_require__.bind(__webpack_require__, /*! firebase/auth */ \"(ssr)/./node_modules/.pnpm/firebase@11.10.0/node_modules/firebase/auth/dist/index.mjs\"));\n                const { auth } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/tslib@2.8.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+auth@1.10.8_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+util@1.12.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+component@0.6.18\"), __webpack_require__.e(\"vendor-chunks/idb@7.1.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+logger@0.4.4\"), __webpack_require__.e(\"vendor-chunks/firebase@11.10.0\"), __webpack_require__.e(\"vendor-chunks/@grpc+grpc-js@1.9.15\"), __webpack_require__.e(\"vendor-chunks/protobufjs@7.5.3\"), __webpack_require__.e(\"vendor-chunks/@grpc+proto-loader@0.7.15\"), __webpack_require__.e(\"vendor-chunks/@firebase+webchannel-wrapper@1.0.3\"), __webpack_require__.e(\"vendor-chunks/@firebase+storage@0.13.14_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+firestore@4.8.0_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/long@5.3.2\"), __webpack_require__.e(\"vendor-chunks/lodash.camelcase@4.3.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+utf8@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+pool@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+path@1.1.2\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+inquire@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+float@1.0.2\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+fetch@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+eventemitter@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+codegen@2.0.4\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+base64@1.1.2\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+aspromise@1.1.2\"), __webpack_require__.e(\"_ssr_lib_firebase_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! ./firebase */ \"(ssr)/./lib/firebase.ts\"));\n                await signOut(auth);\n            }\n            // Clear any stored tokens\n            if (false) {}\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n            return {\n                success: true\n            } // Always succeed for logout\n            ;\n        }\n    }\n    async getCurrentUser() {\n        try {\n            if (this.isDemoMode) {\n                // Return demo user if token exists\n                const token =  false ? 0 : null;\n                if (token && token.startsWith(\"demo-token\")) {\n                    return DEMO_USERS[0] // Return first demo user\n                    ;\n                }\n                return null;\n            }\n            const { auth } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/tslib@2.8.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+auth@1.10.8_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+util@1.12.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+component@0.6.18\"), __webpack_require__.e(\"vendor-chunks/idb@7.1.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+logger@0.4.4\"), __webpack_require__.e(\"vendor-chunks/firebase@11.10.0\"), __webpack_require__.e(\"vendor-chunks/@grpc+grpc-js@1.9.15\"), __webpack_require__.e(\"vendor-chunks/protobufjs@7.5.3\"), __webpack_require__.e(\"vendor-chunks/@grpc+proto-loader@0.7.15\"), __webpack_require__.e(\"vendor-chunks/@firebase+webchannel-wrapper@1.0.3\"), __webpack_require__.e(\"vendor-chunks/@firebase+storage@0.13.14_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+firestore@4.8.0_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/long@5.3.2\"), __webpack_require__.e(\"vendor-chunks/lodash.camelcase@4.3.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+utf8@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+pool@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+path@1.1.2\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+inquire@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+float@1.0.2\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+fetch@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+eventemitter@1.1.0\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+codegen@2.0.4\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+base64@1.1.2\"), __webpack_require__.e(\"vendor-chunks/@protobufjs+aspromise@1.1.2\"), __webpack_require__.e(\"_ssr_lib_firebase_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! ./firebase */ \"(ssr)/./lib/firebase.ts\"));\n            const { onAuthStateChanged } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/tslib@2.8.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+auth@1.10.8_@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+util@1.12.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+app@0.13.2\"), __webpack_require__.e(\"vendor-chunks/@firebase+component@0.6.18\"), __webpack_require__.e(\"vendor-chunks/idb@7.1.1\"), __webpack_require__.e(\"vendor-chunks/@firebase+logger@0.4.4\"), __webpack_require__.e(\"vendor-chunks/firebase@11.10.0\")]).then(__webpack_require__.bind(__webpack_require__, /*! firebase/auth */ \"(ssr)/./node_modules/.pnpm/firebase@11.10.0/node_modules/firebase/auth/dist/index.mjs\"));\n            return new Promise((resolve)=>{\n                const unsubscribe = onAuthStateChanged(auth, (user)=>{\n                    unsubscribe();\n                    if (user) {\n                        resolve({\n                            id: user.uid,\n                            email: user.email || \"\",\n                            name: user.displayName || \"User\",\n                            role: \"Doctor\"\n                        });\n                    } else {\n                        resolve(null);\n                    }\n                });\n            });\n        } catch (error) {\n            console.error(\"Get current user error:\", error);\n            return null;\n        }\n    }\n    // Patient management methods\n    async getPatients() {\n        // Simulate API call\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        return {\n            success: true,\n            data: [\n                {\n                    id: \"P001\",\n                    name: \"Rajesh Kumar Sharma\",\n                    age: 45,\n                    diagnosis: \"Glioblastoma\",\n                    status: \"active\"\n                },\n                {\n                    id: \"P002\",\n                    name: \"Sunita Devi Gupta\",\n                    age: 52,\n                    diagnosis: \"Breast Cancer\",\n                    status: \"stable\"\n                }\n            ]\n        };\n    }\n    async getDiagnosisData() {\n        await new Promise((resolve)=>setTimeout(resolve, 300));\n        return {\n            success: true,\n            data: {\n                totalScans: 1247,\n                pendingReviews: 23,\n                aiAccuracy: 94.2,\n                criticalCases: 8\n            }\n        };\n    }\n    async getHospitalData() {\n        await new Promise((resolve)=>setTimeout(resolve, 400));\n        return {\n            success: true,\n            data: [\n                {\n                    name: \"Tata Memorial Hospital\",\n                    location: \"Mumbai, Maharashtra\",\n                    patients: 1250,\n                    capacity: 1500\n                },\n                {\n                    name: \"AIIMS Delhi\",\n                    location: \"New Delhi\",\n                    patients: 2100,\n                    capacity: 2500\n                }\n            ]\n        };\n    }\n    // Check if service is in demo mode\n    isDemoModeActive() {\n        return this.isDemoMode;\n    }\n    // Get demo credentials for UI display\n    getDemoCredentials() {\n        return {\n            email: \"<EMAIL>\",\n            password: \"demo123\",\n            alternatives: [\n                \"<EMAIL> / demo123\",\n                \"<EMAIL> / demo123\"\n            ]\n        };\n    }\n}\n// Export singleton instance\nconst indianBackendService = new IndianBackendService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvaW5kaWFuLWJhY2tlbmQtc2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkRBQTZEO0FBc0I3RCx5Q0FBeUM7QUFDekMsTUFBTUEsYUFBcUI7SUFDekI7UUFDRUMsSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxVQUFVO1FBQ1ZDLFlBQVk7SUFDZDtJQUNBO1FBQ0VMLElBQUk7UUFDSkMsT0FBTztRQUNQQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsVUFBVTtRQUNWQyxZQUFZO0lBQ2Q7SUFDQTtRQUNFTCxJQUFJO1FBQ0pDLE9BQU87UUFDUEMsTUFBTTtRQUNOQyxNQUFNO1FBQ05DLFVBQVU7UUFDVkMsWUFBWTtJQUNkO0NBQ0Q7QUFFRCxNQUFNQztJQUlKQyxhQUFjO2FBSE5DLFVBQVVDLFFBQVFDLEdBQUcsQ0FBQ0MsbUJBQW1CLElBQUk7YUFDN0NDLGFBQWE7UUFHbkIsdUVBQXVFO1FBQ3ZFLElBQUksQ0FBQ0EsVUFBVSxHQUFHSCxLQUFzQyxJQUFJLENBQXlDO0lBQ3ZHO0lBRUEsTUFBTUssTUFBTUMsV0FBNkIsRUFBeUI7UUFDaEUsSUFBSTtZQUNGLCtFQUErRTtZQUMvRSxJQUFJLElBQUksQ0FBQ0gsVUFBVSxFQUFFO2dCQUNuQixPQUFPLElBQUksQ0FBQ0ksU0FBUyxDQUFDRDtZQUN4QjtZQUVBLG9DQUFvQztZQUNwQyxJQUFJO2dCQUNGLE1BQU0sRUFBRUUsMEJBQTBCLEVBQUUsR0FBRyxNQUFNLDZxQkFBdUI7Z0JBQ3BFLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEdBQUcsTUFBTSwyMERBQW9CO2dCQUUzQyxNQUFNQyxpQkFBaUIsTUFBTUYsMkJBQTJCQyxNQUFNSCxZQUFZZCxLQUFLLEVBQUVjLFlBQVlLLFFBQVE7Z0JBRXJHLE9BQU87b0JBQ0xDLFNBQVM7b0JBQ1RDLE1BQU07d0JBQ0p0QixJQUFJbUIsZUFBZUcsSUFBSSxDQUFDQyxHQUFHO3dCQUMzQnRCLE9BQU9rQixlQUFlRyxJQUFJLENBQUNyQixLQUFLLElBQUk7d0JBQ3BDQyxNQUFNaUIsZUFBZUcsSUFBSSxDQUFDRSxXQUFXLElBQUk7d0JBQ3pDckIsTUFBTTtvQkFDUjtvQkFDQXNCLE9BQU8sTUFBTU4sZUFBZUcsSUFBSSxDQUFDSSxVQUFVO2dCQUM3QztZQUNGLEVBQUUsT0FBT0MsZUFBb0I7Z0JBQzNCQyxRQUFRQyxJQUFJLENBQUMsOERBQThERixjQUFjRyxPQUFPO2dCQUVoRyw0Q0FBNEM7Z0JBQzVDLE9BQU8sSUFBSSxDQUFDZCxTQUFTLENBQUNEO1lBQ3hCO1FBQ0YsRUFBRSxPQUFPZ0IsT0FBWTtZQUNuQkgsUUFBUUcsS0FBSyxDQUFDLGdCQUFnQkE7WUFFOUIsOEJBQThCO1lBQzlCLE9BQU8sSUFBSSxDQUFDZixTQUFTLENBQUNEO1FBQ3hCO0lBQ0Y7SUFFQSxNQUFjQyxVQUFVRCxXQUE2QixFQUF5QjtRQUM1RSxxQkFBcUI7UUFDckIsTUFBTSxJQUFJaUIsUUFBUSxDQUFDQyxVQUFZQyxXQUFXRCxTQUFTO1FBRW5ELHlCQUF5QjtRQUN6QixNQUFNWCxPQUFPdkIsV0FBV29DLElBQUksQ0FBQyxDQUFDQyxJQUFNQSxFQUFFbkMsS0FBSyxLQUFLYyxZQUFZZCxLQUFLO1FBRWpFLElBQUlxQixRQUFTUCxDQUFBQSxZQUFZSyxRQUFRLEtBQUssYUFBYUwsWUFBWUssUUFBUSxLQUFLLFVBQVMsR0FBSTtZQUN2RixPQUFPO2dCQUNMQyxTQUFTO2dCQUNUQztnQkFDQUcsT0FBTyxDQUFDLFdBQVcsRUFBRUgsS0FBS3RCLEVBQUUsQ0FBQyxDQUFDLEVBQUVxQyxLQUFLQyxHQUFHLElBQUk7WUFDOUM7UUFDRjtRQUVBLHNEQUFzRDtRQUN0RCxPQUFPO1lBQ0xqQixTQUFTO1lBQ1RVLE9BQU8sQ0FBQzs7Ozs7OzZDQU0rQixDQUFDO1FBQzFDO0lBQ0Y7SUFFQSxNQUFNUSxlQUFzQztRQUMxQyxJQUFJO1lBQ0YsSUFBSSxJQUFJLENBQUMzQixVQUFVLEVBQUU7Z0JBQ25CLHNCQUFzQjtnQkFDdEIsTUFBTSxJQUFJb0IsUUFBUSxDQUFDQyxVQUFZQyxXQUFXRCxTQUFTO2dCQUVuRCxPQUFPO29CQUNMWixTQUFTO29CQUNUQyxNQUFNO3dCQUNKdEIsSUFBSTt3QkFDSkMsT0FBTzt3QkFDUEMsTUFBTTt3QkFDTkMsTUFBTTt3QkFDTkMsVUFBVTt3QkFDVkMsWUFBWTtvQkFDZDtvQkFDQW9CLE9BQU8sQ0FBQyxrQkFBa0IsRUFBRVksS0FBS0MsR0FBRyxJQUFJO2dCQUMxQztZQUNGO1lBRUEscUNBQXFDO1lBQ3JDLElBQUk7Z0JBQ0YsTUFBTSxFQUFFRSxlQUFlLEVBQUVDLGtCQUFrQixFQUFFLEdBQUcsTUFBTSw2cUJBQXVCO2dCQUM3RSxNQUFNLEVBQUV2QixJQUFJLEVBQUUsR0FBRyxNQUFNLDIwREFBb0I7Z0JBRTNDLE1BQU13QixXQUFXLElBQUlEO2dCQUNyQkMsU0FBU0MsUUFBUSxDQUFDO2dCQUNsQkQsU0FBU0MsUUFBUSxDQUFDO2dCQUVsQixNQUFNQyxTQUFTLE1BQU1KLGdCQUFnQnRCLE1BQU13QjtnQkFFM0MsT0FBTztvQkFDTHJCLFNBQVM7b0JBQ1RDLE1BQU07d0JBQ0p0QixJQUFJNEMsT0FBT3RCLElBQUksQ0FBQ0MsR0FBRzt3QkFDbkJ0QixPQUFPMkMsT0FBT3RCLElBQUksQ0FBQ3JCLEtBQUssSUFBSTt3QkFDNUJDLE1BQU0wQyxPQUFPdEIsSUFBSSxDQUFDRSxXQUFXLElBQUk7d0JBQ2pDckIsTUFBTTtvQkFDUjtvQkFDQXNCLE9BQU8sTUFBTW1CLE9BQU90QixJQUFJLENBQUNJLFVBQVU7Z0JBQ3JDO1lBQ0YsRUFBRSxPQUFPQyxlQUFvQjtnQkFDM0JDLFFBQVFDLElBQUksQ0FBQyxvREFBb0RGLGNBQWNHLE9BQU87Z0JBRXRGLGtDQUFrQztnQkFDbEMsT0FBTztvQkFDTFQsU0FBUztvQkFDVEMsTUFBTTt3QkFDSnRCLElBQUk7d0JBQ0pDLE9BQU87d0JBQ1BDLE1BQU07d0JBQ05DLE1BQU07d0JBQ05DLFVBQVU7d0JBQ1ZDLFlBQVk7b0JBQ2Q7b0JBQ0FvQixPQUFPLENBQUMscUJBQXFCLEVBQUVZLEtBQUtDLEdBQUcsSUFBSTtnQkFDN0M7WUFDRjtRQUNGLEVBQUUsT0FBT1AsT0FBWTtZQUNuQkgsUUFBUUcsS0FBSyxDQUFDLHlCQUF5QkE7WUFFdkMsT0FBTztnQkFDTFYsU0FBUztnQkFDVFUsT0FBTztZQUNUO1FBQ0Y7SUFDRjtJQUVBLE1BQU1jLFNBQXdDO1FBQzVDLElBQUk7WUFDRixJQUFJLENBQUMsSUFBSSxDQUFDakMsVUFBVSxFQUFFO2dCQUNwQixNQUFNLEVBQUVrQyxPQUFPLEVBQUUsR0FBRyxNQUFNLDZxQkFBdUI7Z0JBQ2pELE1BQU0sRUFBRTVCLElBQUksRUFBRSxHQUFHLE1BQU0sMjBEQUFvQjtnQkFDM0MsTUFBTTRCLFFBQVE1QjtZQUNoQjtZQUVBLDBCQUEwQjtZQUMxQixJQUFJLEtBQTZCLEVBQUUsRUFHbEM7WUFFRCxPQUFPO2dCQUFFRyxTQUFTO1lBQUs7UUFDekIsRUFBRSxPQUFPVSxPQUFPO1lBQ2RILFFBQVFHLEtBQUssQ0FBQyxpQkFBaUJBO1lBQy9CLE9BQU87Z0JBQUVWLFNBQVM7WUFBSyxFQUFFLDRCQUE0Qjs7UUFDdkQ7SUFDRjtJQUVBLE1BQU00QixpQkFBdUM7UUFDM0MsSUFBSTtZQUNGLElBQUksSUFBSSxDQUFDckMsVUFBVSxFQUFFO2dCQUNuQixtQ0FBbUM7Z0JBQ25DLE1BQU1hLFFBQVEsTUFBNkIsR0FBR3NCLENBQXNDLEdBQUc7Z0JBQ3ZGLElBQUl0QixTQUFTQSxNQUFNMEIsVUFBVSxDQUFDLGVBQWU7b0JBQzNDLE9BQU9wRCxVQUFVLENBQUMsRUFBRSxDQUFDLHlCQUF5Qjs7Z0JBQ2hEO2dCQUNBLE9BQU87WUFDVDtZQUVBLE1BQU0sRUFBRW1CLElBQUksRUFBRSxHQUFHLE1BQU0sMjBEQUFvQjtZQUMzQyxNQUFNLEVBQUVrQyxrQkFBa0IsRUFBRSxHQUFHLE1BQU0sNnFCQUF1QjtZQUU1RCxPQUFPLElBQUlwQixRQUFRLENBQUNDO2dCQUNsQixNQUFNb0IsY0FBY0QsbUJBQW1CbEMsTUFBTSxDQUFDSTtvQkFDNUMrQjtvQkFDQSxJQUFJL0IsTUFBTTt3QkFDUlcsUUFBUTs0QkFDTmpDLElBQUlzQixLQUFLQyxHQUFHOzRCQUNadEIsT0FBT3FCLEtBQUtyQixLQUFLLElBQUk7NEJBQ3JCQyxNQUFNb0IsS0FBS0UsV0FBVyxJQUFJOzRCQUMxQnJCLE1BQU07d0JBQ1I7b0JBQ0YsT0FBTzt3QkFDTDhCLFFBQVE7b0JBQ1Y7Z0JBQ0Y7WUFDRjtRQUNGLEVBQUUsT0FBT0YsT0FBTztZQUNkSCxRQUFRRyxLQUFLLENBQUMsMkJBQTJCQTtZQUN6QyxPQUFPO1FBQ1Q7SUFDRjtJQUVBLDZCQUE2QjtJQUM3QixNQUFNdUIsY0FBYztRQUNsQixvQkFBb0I7UUFDcEIsTUFBTSxJQUFJdEIsUUFBUSxDQUFDQyxVQUFZQyxXQUFXRCxTQUFTO1FBRW5ELE9BQU87WUFDTFosU0FBUztZQUNUa0MsTUFBTTtnQkFDSjtvQkFDRXZELElBQUk7b0JBQ0pFLE1BQU07b0JBQ05zRCxLQUFLO29CQUNMQyxXQUFXO29CQUNYQyxRQUFRO2dCQUNWO2dCQUNBO29CQUNFMUQsSUFBSTtvQkFDSkUsTUFBTTtvQkFDTnNELEtBQUs7b0JBQ0xDLFdBQVc7b0JBQ1hDLFFBQVE7Z0JBQ1Y7YUFDRDtRQUNIO0lBQ0Y7SUFFQSxNQUFNQyxtQkFBbUI7UUFDdkIsTUFBTSxJQUFJM0IsUUFBUSxDQUFDQyxVQUFZQyxXQUFXRCxTQUFTO1FBRW5ELE9BQU87WUFDTFosU0FBUztZQUNUa0MsTUFBTTtnQkFDSkssWUFBWTtnQkFDWkMsZ0JBQWdCO2dCQUNoQkMsWUFBWTtnQkFDWkMsZUFBZTtZQUNqQjtRQUNGO0lBQ0Y7SUFFQSxNQUFNQyxrQkFBa0I7UUFDdEIsTUFBTSxJQUFJaEMsUUFBUSxDQUFDQyxVQUFZQyxXQUFXRCxTQUFTO1FBRW5ELE9BQU87WUFDTFosU0FBUztZQUNUa0MsTUFBTTtnQkFDSjtvQkFDRXJELE1BQU07b0JBQ04rRCxVQUFVO29CQUNWQyxVQUFVO29CQUNWQyxVQUFVO2dCQUNaO2dCQUNBO29CQUNFakUsTUFBTTtvQkFDTitELFVBQVU7b0JBQ1ZDLFVBQVU7b0JBQ1ZDLFVBQVU7Z0JBQ1o7YUFDRDtRQUNIO0lBQ0Y7SUFFQSxtQ0FBbUM7SUFDbkNDLG1CQUE0QjtRQUMxQixPQUFPLElBQUksQ0FBQ3hELFVBQVU7SUFDeEI7SUFFQSxzQ0FBc0M7SUFDdEN5RCxxQkFBcUI7UUFDbkIsT0FBTztZQUNMcEUsT0FBTztZQUNQbUIsVUFBVTtZQUNWa0QsY0FBYztnQkFBQztnQkFBa0M7YUFBK0I7UUFDbEY7SUFDRjtBQUNGO0FBRUEsNEJBQTRCO0FBQ3JCLE1BQU1DLHVCQUF1QixJQUFJakUsdUJBQXNCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFiaGlqXFxEb3dubG9hZHNcXFF1YW50bmV4LmFpXFxsaWJcXGluZGlhbi1iYWNrZW5kLXNlcnZpY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRW5oYW5jZWQgSW5kaWFuIEJhY2tlbmQgU2VydmljZSB3aXRoIHByb3BlciBlcnJvciBoYW5kbGluZ1xyXG5pbnRlcmZhY2UgTG9naW5DcmVkZW50aWFscyB7XHJcbiAgZW1haWw6IHN0cmluZ1xyXG4gIHBhc3N3b3JkOiBzdHJpbmdcclxufVxyXG5cclxuaW50ZXJmYWNlIFVzZXIge1xyXG4gIGlkOiBzdHJpbmdcclxuICBlbWFpbDogc3RyaW5nXHJcbiAgbmFtZTogc3RyaW5nXHJcbiAgcm9sZTogc3RyaW5nXHJcbiAgaG9zcGl0YWw/OiBzdHJpbmdcclxuICBkZXBhcnRtZW50Pzogc3RyaW5nXHJcbn1cclxuXHJcbmludGVyZmFjZSBBdXRoUmVzcG9uc2Uge1xyXG4gIHN1Y2Nlc3M6IGJvb2xlYW5cclxuICB1c2VyPzogVXNlclxyXG4gIHRva2VuPzogc3RyaW5nXHJcbiAgZXJyb3I/OiBzdHJpbmdcclxufVxyXG5cclxuLy8gRGVtbyB1c2VycyBmb3IgZmFsbGJhY2sgYXV0aGVudGljYXRpb25cclxuY29uc3QgREVNT19VU0VSUzogVXNlcltdID0gW1xyXG4gIHtcclxuICAgIGlkOiBcImRlbW8tMVwiLFxyXG4gICAgZW1haWw6IFwiZHIucHJpeWFAcXVhbnRuZXguY29tXCIsXHJcbiAgICBuYW1lOiBcIkRyLiBQcml5YSBQYXRlbFwiLFxyXG4gICAgcm9sZTogXCJPbmNvbG9naXN0XCIsXHJcbiAgICBob3NwaXRhbDogXCJUYXRhIE1lbW9yaWFsIEhvc3BpdGFsXCIsXHJcbiAgICBkZXBhcnRtZW50OiBcIk5ldXJvLU9uY29sb2d5XCIsXHJcbiAgfSxcclxuICB7XHJcbiAgICBpZDogXCJkZW1vLTJcIixcclxuICAgIGVtYWlsOiBcImRyLmFtaXRAcXVhbnRuZXguY29tXCIsXHJcbiAgICBuYW1lOiBcIkRyLiBBbWl0IEd1cHRhXCIsXHJcbiAgICByb2xlOiBcIlN1cmdlb25cIixcclxuICAgIGhvc3BpdGFsOiBcIkFJSU1TIERlbGhpXCIsXHJcbiAgICBkZXBhcnRtZW50OiBcIkNhbmNlciBTdXJnZXJ5XCIsXHJcbiAgfSxcclxuICB7XHJcbiAgICBpZDogXCJkZW1vLTNcIixcclxuICAgIGVtYWlsOiBcImFkbWluQHF1YW50bmV4LmNvbVwiLFxyXG4gICAgbmFtZTogXCJSYWplc2ggS3VtYXJcIixcclxuICAgIHJvbGU6IFwiQWRtaW5pc3RyYXRvclwiLFxyXG4gICAgaG9zcGl0YWw6IFwiQXBvbGxvIEhvc3BpdGFsXCIsXHJcbiAgICBkZXBhcnRtZW50OiBcIkFkbWluaXN0cmF0aW9uXCIsXHJcbiAgfSxcclxuXVxyXG5cclxuY2xhc3MgSW5kaWFuQmFja2VuZFNlcnZpY2Uge1xyXG4gIHByaXZhdGUgYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkwgfHwgXCJodHRwczovL2FwaS5xdWFudG5leC5pblwiXHJcbiAgcHJpdmF0ZSBpc0RlbW9Nb2RlID0gZmFsc2VcclxuXHJcbiAgY29uc3RydWN0b3IoKSB7XHJcbiAgICAvLyBDaGVjayBpZiB3ZSdyZSBpbiBkZW1vIG1vZGUgKGRldmVsb3BtZW50IG9yIEZpcmViYXNlIG5vdCBjb25maWd1cmVkKVxyXG4gICAgdGhpcy5pc0RlbW9Nb2RlID0gcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwiZGV2ZWxvcG1lbnRcIiB8fCAhcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRklSRUJBU0VfQVBJX0tFWVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgbG9naW4oY3JlZGVudGlhbHM6IExvZ2luQ3JlZGVudGlhbHMpOiBQcm9taXNlPEF1dGhSZXNwb25zZT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgLy8gSWYgRmlyZWJhc2UgaXMgbm90IGNvbmZpZ3VyZWQgb3Igd2UncmUgaW4gZGVtbyBtb2RlLCB1c2UgZGVtbyBhdXRoZW50aWNhdGlvblxyXG4gICAgICBpZiAodGhpcy5pc0RlbW9Nb2RlKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuZGVtb0xvZ2luKGNyZWRlbnRpYWxzKVxyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBUcnkgRmlyZWJhc2UgYXV0aGVudGljYXRpb24gZmlyc3RcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zdCB7IHNpZ25JbldpdGhFbWFpbEFuZFBhc3N3b3JkIH0gPSBhd2FpdCBpbXBvcnQoXCJmaXJlYmFzZS9hdXRoXCIpXHJcbiAgICAgICAgY29uc3QgeyBhdXRoIH0gPSBhd2FpdCBpbXBvcnQoXCIuL2ZpcmViYXNlXCIpXHJcblxyXG4gICAgICAgIGNvbnN0IHVzZXJDcmVkZW50aWFsID0gYXdhaXQgc2lnbkluV2l0aEVtYWlsQW5kUGFzc3dvcmQoYXV0aCwgY3JlZGVudGlhbHMuZW1haWwsIGNyZWRlbnRpYWxzLnBhc3N3b3JkKVxyXG5cclxuICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgc3VjY2VzczogdHJ1ZSxcclxuICAgICAgICAgIHVzZXI6IHtcclxuICAgICAgICAgICAgaWQ6IHVzZXJDcmVkZW50aWFsLnVzZXIudWlkLFxyXG4gICAgICAgICAgICBlbWFpbDogdXNlckNyZWRlbnRpYWwudXNlci5lbWFpbCB8fCBcIlwiLFxyXG4gICAgICAgICAgICBuYW1lOiB1c2VyQ3JlZGVudGlhbC51c2VyLmRpc3BsYXlOYW1lIHx8IFwiVXNlclwiLFxyXG4gICAgICAgICAgICByb2xlOiBcIkRvY3RvclwiLFxyXG4gICAgICAgICAgfSxcclxuICAgICAgICAgIHRva2VuOiBhd2FpdCB1c2VyQ3JlZGVudGlhbC51c2VyLmdldElkVG9rZW4oKSxcclxuICAgICAgICB9XHJcbiAgICAgIH0gY2F0Y2ggKGZpcmViYXNlRXJyb3I6IGFueSkge1xyXG4gICAgICAgIGNvbnNvbGUud2FybihcIkZpcmViYXNlIGF1dGhlbnRpY2F0aW9uIGZhaWxlZCwgZmFsbGluZyBiYWNrIHRvIGRlbW8gbW9kZTpcIiwgZmlyZWJhc2VFcnJvci5tZXNzYWdlKVxyXG5cclxuICAgICAgICAvLyBJZiBGaXJlYmFzZSBmYWlscywgZmFsbCBiYWNrIHRvIGRlbW8gbW9kZVxyXG4gICAgICAgIHJldHVybiB0aGlzLmRlbW9Mb2dpbihjcmVkZW50aWFscylcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiTG9naW4gZXJyb3I6XCIsIGVycm9yKVxyXG5cclxuICAgICAgLy8gRmluYWwgZmFsbGJhY2sgdG8gZGVtbyBtb2RlXHJcbiAgICAgIHJldHVybiB0aGlzLmRlbW9Mb2dpbihjcmVkZW50aWFscylcclxuICAgIH1cclxuICB9XHJcblxyXG4gIHByaXZhdGUgYXN5bmMgZGVtb0xvZ2luKGNyZWRlbnRpYWxzOiBMb2dpbkNyZWRlbnRpYWxzKTogUHJvbWlzZTxBdXRoUmVzcG9uc2U+IHtcclxuICAgIC8vIFNpbXVsYXRlIEFQSSBkZWxheVxyXG4gICAgYXdhaXQgbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwMCkpXHJcblxyXG4gICAgLy8gQ2hlY2sgZGVtbyBjcmVkZW50aWFsc1xyXG4gICAgY29uc3QgdXNlciA9IERFTU9fVVNFUlMuZmluZCgodSkgPT4gdS5lbWFpbCA9PT0gY3JlZGVudGlhbHMuZW1haWwpXHJcblxyXG4gICAgaWYgKHVzZXIgJiYgKGNyZWRlbnRpYWxzLnBhc3N3b3JkID09PSBcImRlbW8xMjNcIiB8fCBjcmVkZW50aWFscy5wYXNzd29yZCA9PT0gXCJwYXNzd29yZFwiKSkge1xyXG4gICAgICByZXR1cm4ge1xyXG4gICAgICAgIHN1Y2Nlc3M6IHRydWUsXHJcbiAgICAgICAgdXNlcixcclxuICAgICAgICB0b2tlbjogYGRlbW8tdG9rZW4tJHt1c2VyLmlkfS0ke0RhdGUubm93KCl9YCxcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIElmIG5vIG1hdGNoIGZvdW5kLCBwcm92aWRlIGhlbHBmdWwgZGVtbyBjcmVkZW50aWFsc1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgc3VjY2VzczogZmFsc2UsXHJcbiAgICAgIGVycm9yOiBgSW52YWxpZCBjcmVkZW50aWFscy4gRGVtbyBhY2NvdW50cyBhdmFpbGFibGU6XHJcbiAgICAgIFxyXG4gICAgICDigKIgZHIucHJpeWFAcXVhbnRuZXguY29tIC8gZGVtbzEyM1xyXG4gICAgICDigKIgZHIuYW1pdEBxdWFudG5leC5jb20gLyBkZW1vMTIzICBcclxuICAgICAg4oCiIGFkbWluQHF1YW50bmV4LmNvbSAvIGRlbW8xMjNcclxuICAgICAgXHJcbiAgICAgIE9yIHVzZSBhbnkgZW1haWwgd2l0aCBwYXNzd29yZDogZGVtbzEyM2AsXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBhc3luYyBnb29nbGVTaWduSW4oKTogUHJvbWlzZTxBdXRoUmVzcG9uc2U+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGlmICh0aGlzLmlzRGVtb01vZGUpIHtcclxuICAgICAgICAvLyBEZW1vIEdvb2dsZSBzaWduLWluXHJcbiAgICAgICAgYXdhaXQgbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTUwMCkpXHJcblxyXG4gICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxyXG4gICAgICAgICAgdXNlcjoge1xyXG4gICAgICAgICAgICBpZDogXCJnb29nbGUtZGVtby0xXCIsXHJcbiAgICAgICAgICAgIGVtYWlsOiBcImRlbW8udXNlckBnbWFpbC5jb21cIixcclxuICAgICAgICAgICAgbmFtZTogXCJEci4gS2F2eWEgU2hhcm1hXCIsXHJcbiAgICAgICAgICAgIHJvbGU6IFwiUmFkaW9sb2dpc3RcIixcclxuICAgICAgICAgICAgaG9zcGl0YWw6IFwiQXBvbGxvIEhvc3BpdGFsXCIsXHJcbiAgICAgICAgICAgIGRlcGFydG1lbnQ6IFwiTWVkaWNhbCBJbWFnaW5nXCIsXHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgICAgdG9rZW46IGBnb29nbGUtZGVtby10b2tlbi0ke0RhdGUubm93KCl9YCxcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFRyeSBGaXJlYmFzZSBHb29nbGUgYXV0aGVudGljYXRpb25cclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zdCB7IHNpZ25JbldpdGhQb3B1cCwgR29vZ2xlQXV0aFByb3ZpZGVyIH0gPSBhd2FpdCBpbXBvcnQoXCJmaXJlYmFzZS9hdXRoXCIpXHJcbiAgICAgICAgY29uc3QgeyBhdXRoIH0gPSBhd2FpdCBpbXBvcnQoXCIuL2ZpcmViYXNlXCIpXHJcblxyXG4gICAgICAgIGNvbnN0IHByb3ZpZGVyID0gbmV3IEdvb2dsZUF1dGhQcm92aWRlcigpXHJcbiAgICAgICAgcHJvdmlkZXIuYWRkU2NvcGUoXCJlbWFpbFwiKVxyXG4gICAgICAgIHByb3ZpZGVyLmFkZFNjb3BlKFwicHJvZmlsZVwiKVxyXG5cclxuICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBzaWduSW5XaXRoUG9wdXAoYXV0aCwgcHJvdmlkZXIpXHJcblxyXG4gICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxyXG4gICAgICAgICAgdXNlcjoge1xyXG4gICAgICAgICAgICBpZDogcmVzdWx0LnVzZXIudWlkLFxyXG4gICAgICAgICAgICBlbWFpbDogcmVzdWx0LnVzZXIuZW1haWwgfHwgXCJcIixcclxuICAgICAgICAgICAgbmFtZTogcmVzdWx0LnVzZXIuZGlzcGxheU5hbWUgfHwgXCJVc2VyXCIsXHJcbiAgICAgICAgICAgIHJvbGU6IFwiRG9jdG9yXCIsXHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgICAgdG9rZW46IGF3YWl0IHJlc3VsdC51c2VyLmdldElkVG9rZW4oKSxcclxuICAgICAgICB9XHJcbiAgICAgIH0gY2F0Y2ggKGZpcmViYXNlRXJyb3I6IGFueSkge1xyXG4gICAgICAgIGNvbnNvbGUud2FybihcIkZpcmViYXNlIEdvb2dsZSBzaWduLWluIGZhaWxlZCwgdXNpbmcgZGVtbyBtb2RlOlwiLCBmaXJlYmFzZUVycm9yLm1lc3NhZ2UpXHJcblxyXG4gICAgICAgIC8vIEZhbGxiYWNrIHRvIGRlbW8gR29vZ2xlIHNpZ24taW5cclxuICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgc3VjY2VzczogdHJ1ZSxcclxuICAgICAgICAgIHVzZXI6IHtcclxuICAgICAgICAgICAgaWQ6IFwiZ29vZ2xlLWRlbW8tZmFsbGJhY2tcIixcclxuICAgICAgICAgICAgZW1haWw6IFwiZGVtby5nb29nbGVAcXVhbnRuZXguY29tXCIsXHJcbiAgICAgICAgICAgIG5hbWU6IFwiRHIuIEFyanVuIFNpbmdoXCIsXHJcbiAgICAgICAgICAgIHJvbGU6IFwiT25jb2xvZ2lzdFwiLFxyXG4gICAgICAgICAgICBob3NwaXRhbDogXCJGb3J0aXMgSG9zcGl0YWxcIixcclxuICAgICAgICAgICAgZGVwYXJ0bWVudDogXCJNZWRpY2FsIE9uY29sb2d5XCIsXHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgICAgdG9rZW46IGBnb29nbGUtZGVtby1mYWxsYmFjay0ke0RhdGUubm93KCl9YCxcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkdvb2dsZSBzaWduLWluIGVycm9yOlwiLCBlcnJvcilcclxuXHJcbiAgICAgIHJldHVybiB7XHJcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXHJcbiAgICAgICAgZXJyb3I6IFwiR29vZ2xlIHNpZ24taW4gZmFpbGVkLiBQbGVhc2UgdHJ5IGVtYWlsL3Bhc3N3b3JkIGxvZ2luIG9yIGNvbnRhY3Qgc3VwcG9ydC5cIixcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgbG9nb3V0KCk6IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuIH0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGlmICghdGhpcy5pc0RlbW9Nb2RlKSB7XHJcbiAgICAgICAgY29uc3QgeyBzaWduT3V0IH0gPSBhd2FpdCBpbXBvcnQoXCJmaXJlYmFzZS9hdXRoXCIpXHJcbiAgICAgICAgY29uc3QgeyBhdXRoIH0gPSBhd2FpdCBpbXBvcnQoXCIuL2ZpcmViYXNlXCIpXHJcbiAgICAgICAgYXdhaXQgc2lnbk91dChhdXRoKVxyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBDbGVhciBhbnkgc3RvcmVkIHRva2Vuc1xyXG4gICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gXCJ1bmRlZmluZWRcIikge1xyXG4gICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKFwicXVhbnRuZXgtdG9rZW5cIilcclxuICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShcInF1YW50bmV4LXVzZXJcIilcclxuICAgICAgfVxyXG5cclxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiTG9nb3V0IGVycm9yOlwiLCBlcnJvcilcclxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSB9IC8vIEFsd2F5cyBzdWNjZWVkIGZvciBsb2dvdXRcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGFzeW5jIGdldEN1cnJlbnRVc2VyKCk6IFByb21pc2U8VXNlciB8IG51bGw+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGlmICh0aGlzLmlzRGVtb01vZGUpIHtcclxuICAgICAgICAvLyBSZXR1cm4gZGVtbyB1c2VyIGlmIHRva2VuIGV4aXN0c1xyXG4gICAgICAgIGNvbnN0IHRva2VuID0gdHlwZW9mIHdpbmRvdyAhPT0gXCJ1bmRlZmluZWRcIiA/IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwicXVhbnRuZXgtdG9rZW5cIikgOiBudWxsXHJcbiAgICAgICAgaWYgKHRva2VuICYmIHRva2VuLnN0YXJ0c1dpdGgoXCJkZW1vLXRva2VuXCIpKSB7XHJcbiAgICAgICAgICByZXR1cm4gREVNT19VU0VSU1swXSAvLyBSZXR1cm4gZmlyc3QgZGVtbyB1c2VyXHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiBudWxsXHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IHsgYXV0aCB9ID0gYXdhaXQgaW1wb3J0KFwiLi9maXJlYmFzZVwiKVxyXG4gICAgICBjb25zdCB7IG9uQXV0aFN0YXRlQ2hhbmdlZCB9ID0gYXdhaXQgaW1wb3J0KFwiZmlyZWJhc2UvYXV0aFwiKVxyXG5cclxuICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlKSA9PiB7XHJcbiAgICAgICAgY29uc3QgdW5zdWJzY3JpYmUgPSBvbkF1dGhTdGF0ZUNoYW5nZWQoYXV0aCwgKHVzZXIpID0+IHtcclxuICAgICAgICAgIHVuc3Vic2NyaWJlKClcclxuICAgICAgICAgIGlmICh1c2VyKSB7XHJcbiAgICAgICAgICAgIHJlc29sdmUoe1xyXG4gICAgICAgICAgICAgIGlkOiB1c2VyLnVpZCxcclxuICAgICAgICAgICAgICBlbWFpbDogdXNlci5lbWFpbCB8fCBcIlwiLFxyXG4gICAgICAgICAgICAgIG5hbWU6IHVzZXIuZGlzcGxheU5hbWUgfHwgXCJVc2VyXCIsXHJcbiAgICAgICAgICAgICAgcm9sZTogXCJEb2N0b3JcIixcclxuICAgICAgICAgICAgfSlcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIHJlc29sdmUobnVsbClcclxuICAgICAgICAgIH1cclxuICAgICAgICB9KVxyXG4gICAgICB9KVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkdldCBjdXJyZW50IHVzZXIgZXJyb3I6XCIsIGVycm9yKVxyXG4gICAgICByZXR1cm4gbnVsbFxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gUGF0aWVudCBtYW5hZ2VtZW50IG1ldGhvZHNcclxuICBhc3luYyBnZXRQYXRpZW50cygpIHtcclxuICAgIC8vIFNpbXVsYXRlIEFQSSBjYWxsXHJcbiAgICBhd2FpdCBuZXcgUHJvbWlzZSgocmVzb2x2ZSkgPT4gc2V0VGltZW91dChyZXNvbHZlLCA1MDApKVxyXG5cclxuICAgIHJldHVybiB7XHJcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXHJcbiAgICAgIGRhdGE6IFtcclxuICAgICAgICB7XHJcbiAgICAgICAgICBpZDogXCJQMDAxXCIsXHJcbiAgICAgICAgICBuYW1lOiBcIlJhamVzaCBLdW1hciBTaGFybWFcIixcclxuICAgICAgICAgIGFnZTogNDUsXHJcbiAgICAgICAgICBkaWFnbm9zaXM6IFwiR2xpb2JsYXN0b21hXCIsXHJcbiAgICAgICAgICBzdGF0dXM6IFwiYWN0aXZlXCIsXHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICBpZDogXCJQMDAyXCIsXHJcbiAgICAgICAgICBuYW1lOiBcIlN1bml0YSBEZXZpIEd1cHRhXCIsXHJcbiAgICAgICAgICBhZ2U6IDUyLFxyXG4gICAgICAgICAgZGlhZ25vc2lzOiBcIkJyZWFzdCBDYW5jZXJcIixcclxuICAgICAgICAgIHN0YXR1czogXCJzdGFibGVcIixcclxuICAgICAgICB9LFxyXG4gICAgICBdLFxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgZ2V0RGlhZ25vc2lzRGF0YSgpIHtcclxuICAgIGF3YWl0IG5ldyBQcm9taXNlKChyZXNvbHZlKSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDMwMCkpXHJcblxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgc3VjY2VzczogdHJ1ZSxcclxuICAgICAgZGF0YToge1xyXG4gICAgICAgIHRvdGFsU2NhbnM6IDEyNDcsXHJcbiAgICAgICAgcGVuZGluZ1Jldmlld3M6IDIzLFxyXG4gICAgICAgIGFpQWNjdXJhY3k6IDk0LjIsXHJcbiAgICAgICAgY3JpdGljYWxDYXNlczogOCxcclxuICAgICAgfSxcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGFzeW5jIGdldEhvc3BpdGFsRGF0YSgpIHtcclxuICAgIGF3YWl0IG5ldyBQcm9taXNlKChyZXNvbHZlKSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDQwMCkpXHJcblxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgc3VjY2VzczogdHJ1ZSxcclxuICAgICAgZGF0YTogW1xyXG4gICAgICAgIHtcclxuICAgICAgICAgIG5hbWU6IFwiVGF0YSBNZW1vcmlhbCBIb3NwaXRhbFwiLFxyXG4gICAgICAgICAgbG9jYXRpb246IFwiTXVtYmFpLCBNYWhhcmFzaHRyYVwiLFxyXG4gICAgICAgICAgcGF0aWVudHM6IDEyNTAsXHJcbiAgICAgICAgICBjYXBhY2l0eTogMTUwMCxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgIG5hbWU6IFwiQUlJTVMgRGVsaGlcIixcclxuICAgICAgICAgIGxvY2F0aW9uOiBcIk5ldyBEZWxoaVwiLFxyXG4gICAgICAgICAgcGF0aWVudHM6IDIxMDAsXHJcbiAgICAgICAgICBjYXBhY2l0eTogMjUwMCxcclxuICAgICAgICB9LFxyXG4gICAgICBdLFxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gQ2hlY2sgaWYgc2VydmljZSBpcyBpbiBkZW1vIG1vZGVcclxuICBpc0RlbW9Nb2RlQWN0aXZlKCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIHRoaXMuaXNEZW1vTW9kZVxyXG4gIH1cclxuXHJcbiAgLy8gR2V0IGRlbW8gY3JlZGVudGlhbHMgZm9yIFVJIGRpc3BsYXlcclxuICBnZXREZW1vQ3JlZGVudGlhbHMoKSB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBlbWFpbDogXCJkci5wcml5YUBxdWFudG5leC5jb21cIixcclxuICAgICAgcGFzc3dvcmQ6IFwiZGVtbzEyM1wiLFxyXG4gICAgICBhbHRlcm5hdGl2ZXM6IFtcImRyLmFtaXRAcXVhbnRuZXguY29tIC8gZGVtbzEyM1wiLCBcImFkbWluQHF1YW50bmV4LmNvbSAvIGRlbW8xMjNcIl0sXHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyBFeHBvcnQgc2luZ2xldG9uIGluc3RhbmNlXHJcbmV4cG9ydCBjb25zdCBpbmRpYW5CYWNrZW5kU2VydmljZSA9IG5ldyBJbmRpYW5CYWNrZW5kU2VydmljZSgpXHJcblxyXG4vLyBFeHBvcnQgdHlwZXNcclxuZXhwb3J0IHR5cGUgeyBMb2dpbkNyZWRlbnRpYWxzLCBVc2VyLCBBdXRoUmVzcG9uc2UgfVxyXG4iXSwibmFtZXMiOlsiREVNT19VU0VSUyIsImlkIiwiZW1haWwiLCJuYW1lIiwicm9sZSIsImhvc3BpdGFsIiwiZGVwYXJ0bWVudCIsIkluZGlhbkJhY2tlbmRTZXJ2aWNlIiwiY29uc3RydWN0b3IiLCJiYXNlVXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9VUkwiLCJpc0RlbW9Nb2RlIiwiTkVYVF9QVUJMSUNfRklSRUJBU0VfQVBJX0tFWSIsImxvZ2luIiwiY3JlZGVudGlhbHMiLCJkZW1vTG9naW4iLCJzaWduSW5XaXRoRW1haWxBbmRQYXNzd29yZCIsImF1dGgiLCJ1c2VyQ3JlZGVudGlhbCIsInBhc3N3b3JkIiwic3VjY2VzcyIsInVzZXIiLCJ1aWQiLCJkaXNwbGF5TmFtZSIsInRva2VuIiwiZ2V0SWRUb2tlbiIsImZpcmViYXNlRXJyb3IiLCJjb25zb2xlIiwid2FybiIsIm1lc3NhZ2UiLCJlcnJvciIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0VGltZW91dCIsImZpbmQiLCJ1IiwiRGF0ZSIsIm5vdyIsImdvb2dsZVNpZ25JbiIsInNpZ25JbldpdGhQb3B1cCIsIkdvb2dsZUF1dGhQcm92aWRlciIsInByb3ZpZGVyIiwiYWRkU2NvcGUiLCJyZXN1bHQiLCJsb2dvdXQiLCJzaWduT3V0IiwibG9jYWxTdG9yYWdlIiwicmVtb3ZlSXRlbSIsImdldEN1cnJlbnRVc2VyIiwiZ2V0SXRlbSIsInN0YXJ0c1dpdGgiLCJvbkF1dGhTdGF0ZUNoYW5nZWQiLCJ1bnN1YnNjcmliZSIsImdldFBhdGllbnRzIiwiZGF0YSIsImFnZSIsImRpYWdub3NpcyIsInN0YXR1cyIsImdldERpYWdub3Npc0RhdGEiLCJ0b3RhbFNjYW5zIiwicGVuZGluZ1Jldmlld3MiLCJhaUFjY3VyYWN5IiwiY3JpdGljYWxDYXNlcyIsImdldEhvc3BpdGFsRGF0YSIsImxvY2F0aW9uIiwicGF0aWVudHMiLCJjYXBhY2l0eSIsImlzRGVtb01vZGVBY3RpdmUiLCJnZXREZW1vQ3JlZGVudGlhbHMiLCJhbHRlcm5hdGl2ZXMiLCJpbmRpYW5CYWNrZW5kU2VydmljZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./lib/indian-backend-service.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/auth-context.tsx */ \"(ssr)/./contexts/auth-context.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfQGJhYmVsK2NvcmVANy4yX2FmNDYyZGEwYjQ0YTY0ZWU0YWM3YzYyY2I2ZjIyNmQ4L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQWJoaWolNUMlNUNEb3dubG9hZHMlNUMlNUNRdWFudG5leC5haSU1QyU1Q2NvbnRleHRzJTVDJTVDYXV0aC1jb250ZXh0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkF1dGhQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBYmhpaiU1QyU1Q0Rvd25sb2FkcyU1QyU1Q1F1YW50bmV4LmFpJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDLnBucG0lNUMlNUNuZXh0JTQwMTUuMi40XyU0MGJhYmVsJTJCY29yZSU0MDcuMl9hZjQ2MmRhMGI0NGE2NGVlNGFjN2M2MmNiNmYyMjZkOCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNBYmhpaiU1QyU1Q0Rvd25sb2FkcyU1QyU1Q1F1YW50bmV4LmFpJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUEySSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcQWJoaWpcXFxcRG93bmxvYWRzXFxcXFF1YW50bmV4LmFpXFxcXGNvbnRleHRzXFxcXGF1dGgtY29udGV4dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAbhij%5C%5CDownloads%5C%5CQuantnex.ai%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \**************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8","vendor-chunks/lucide-react@0.454.0_react@19.0.0","vendor-chunks/@swc+helpers@0.5.15"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.2.4_@babel+core@7.2_af462da0b44a64ee4ac7c62cb6f226d8/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2F.pnpm%2Fnext%4015.2.4_%40babel%2Bcore%407.2_af462da0b44a64ee4ac7c62cb6f226d8%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CAbhij%5CDownloads%5CQuantnex.ai%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbhij%5CDownloads%5CQuantnex.ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();