"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@firebase+storage@0.13.14_@firebase+app@0.13.2";
exports.ids = ["vendor-chunks/@firebase+storage@0.13.14_@firebase+app@0.13.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@firebase+storage@0.13.14_@firebase+app@0.13.2/node_modules/@firebase/storage/dist/node-esm/index.node.esm.js":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@firebase+storage@0.13.14_@firebase+app@0.13.2/node_modules/@firebase/storage/dist/node-esm/index.node.esm.js ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StorageError: () => (/* binding */ StorageError),\n/* harmony export */   StorageErrorCode: () => (/* binding */ StorageErrorCode),\n/* harmony export */   StringFormat: () => (/* binding */ StringFormat),\n/* harmony export */   _FbsBlob: () => (/* binding */ FbsBlob),\n/* harmony export */   _Location: () => (/* binding */ Location),\n/* harmony export */   _TaskEvent: () => (/* binding */ TaskEvent),\n/* harmony export */   _TaskState: () => (/* binding */ TaskState),\n/* harmony export */   _UploadTask: () => (/* binding */ UploadTask),\n/* harmony export */   _dataFromString: () => (/* binding */ dataFromString),\n/* harmony export */   _getChild: () => (/* binding */ _getChild),\n/* harmony export */   _invalidArgument: () => (/* binding */ invalidArgument),\n/* harmony export */   _invalidRootOperation: () => (/* binding */ invalidRootOperation),\n/* harmony export */   connectStorageEmulator: () => (/* binding */ connectStorageEmulator),\n/* harmony export */   deleteObject: () => (/* binding */ deleteObject),\n/* harmony export */   getBlob: () => (/* binding */ getBlob),\n/* harmony export */   getBytes: () => (/* binding */ getBytes),\n/* harmony export */   getDownloadURL: () => (/* binding */ getDownloadURL),\n/* harmony export */   getMetadata: () => (/* binding */ getMetadata),\n/* harmony export */   getStorage: () => (/* binding */ getStorage),\n/* harmony export */   getStream: () => (/* binding */ getStream),\n/* harmony export */   list: () => (/* binding */ list),\n/* harmony export */   listAll: () => (/* binding */ listAll),\n/* harmony export */   ref: () => (/* binding */ ref),\n/* harmony export */   updateMetadata: () => (/* binding */ updateMetadata),\n/* harmony export */   uploadBytes: () => (/* binding */ uploadBytes),\n/* harmony export */   uploadBytesResumable: () => (/* binding */ uploadBytesResumable),\n/* harmony export */   uploadString: () => (/* binding */ uploadString)\n/* harmony export */ });\n/* harmony import */ var _firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @firebase/app */ \"(ssr)/./node_modules/.pnpm/@firebase+app@0.13.2/node_modules/@firebase/app/dist/esm/index.esm2017.js\");\n/* harmony import */ var _firebase_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @firebase/util */ \"(ssr)/./node_modules/.pnpm/@firebase+util@1.12.1/node_modules/@firebase/util/dist/node-esm/index.node.esm.js\");\n/* harmony import */ var _firebase_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @firebase/component */ \"(ssr)/./node_modules/.pnpm/@firebase+component@0.6.18/node_modules/@firebase/component/dist/esm/index.esm2017.js\");\n\n\n\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Constants used in the Firebase Storage library.\n */\n/**\n * Domain name for firebase storage.\n */\nconst DEFAULT_HOST = 'firebasestorage.googleapis.com';\n/**\n * The key in Firebase config json for the storage bucket.\n */\nconst CONFIG_STORAGE_BUCKET_KEY = 'storageBucket';\n/**\n * 2 minutes\n *\n * The timeout for all operations except upload.\n */\nconst DEFAULT_MAX_OPERATION_RETRY_TIME = 2 * 60 * 1000;\n/**\n * 10 minutes\n *\n * The timeout for upload.\n */\nconst DEFAULT_MAX_UPLOAD_RETRY_TIME = 10 * 60 * 1000;\n/**\n * 1 second\n */\nconst DEFAULT_MIN_SLEEP_TIME_MILLIS = 1000;\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * An error returned by the Firebase Storage SDK.\n * @public\n */\nclass StorageError extends _firebase_util__WEBPACK_IMPORTED_MODULE_1__.FirebaseError {\n    /**\n     * @param code - A `StorageErrorCode` string to be prefixed with 'storage/' and\n     *  added to the end of the message.\n     * @param message  - Error message.\n     * @param status_ - Corresponding HTTP Status Code\n     */\n    constructor(code, message, status_ = 0) {\n        super(prependCode(code), `Firebase Storage: ${message} (${prependCode(code)})`);\n        this.status_ = status_;\n        /**\n         * Stores custom error data unique to the `StorageError`.\n         */\n        this.customData = { serverResponse: null };\n        this._baseMessage = this.message;\n        // Without this, `instanceof StorageError`, in tests for example,\n        // returns false.\n        Object.setPrototypeOf(this, StorageError.prototype);\n    }\n    get status() {\n        return this.status_;\n    }\n    set status(status) {\n        this.status_ = status;\n    }\n    /**\n     * Compares a `StorageErrorCode` against this error's code, filtering out the prefix.\n     */\n    _codeEquals(code) {\n        return prependCode(code) === this.code;\n    }\n    /**\n     * Optional response message that was added by the server.\n     */\n    get serverResponse() {\n        return this.customData.serverResponse;\n    }\n    set serverResponse(serverResponse) {\n        this.customData.serverResponse = serverResponse;\n        if (this.customData.serverResponse) {\n            this.message = `${this._baseMessage}\\n${this.customData.serverResponse}`;\n        }\n        else {\n            this.message = this._baseMessage;\n        }\n    }\n}\n/**\n * @public\n * Error codes that can be attached to `StorageError` objects.\n */\nvar StorageErrorCode;\n(function (StorageErrorCode) {\n    // Shared between all platforms\n    StorageErrorCode[\"UNKNOWN\"] = \"unknown\";\n    StorageErrorCode[\"OBJECT_NOT_FOUND\"] = \"object-not-found\";\n    StorageErrorCode[\"BUCKET_NOT_FOUND\"] = \"bucket-not-found\";\n    StorageErrorCode[\"PROJECT_NOT_FOUND\"] = \"project-not-found\";\n    StorageErrorCode[\"QUOTA_EXCEEDED\"] = \"quota-exceeded\";\n    StorageErrorCode[\"UNAUTHENTICATED\"] = \"unauthenticated\";\n    StorageErrorCode[\"UNAUTHORIZED\"] = \"unauthorized\";\n    StorageErrorCode[\"UNAUTHORIZED_APP\"] = \"unauthorized-app\";\n    StorageErrorCode[\"RETRY_LIMIT_EXCEEDED\"] = \"retry-limit-exceeded\";\n    StorageErrorCode[\"INVALID_CHECKSUM\"] = \"invalid-checksum\";\n    StorageErrorCode[\"CANCELED\"] = \"canceled\";\n    // JS specific\n    StorageErrorCode[\"INVALID_EVENT_NAME\"] = \"invalid-event-name\";\n    StorageErrorCode[\"INVALID_URL\"] = \"invalid-url\";\n    StorageErrorCode[\"INVALID_DEFAULT_BUCKET\"] = \"invalid-default-bucket\";\n    StorageErrorCode[\"NO_DEFAULT_BUCKET\"] = \"no-default-bucket\";\n    StorageErrorCode[\"CANNOT_SLICE_BLOB\"] = \"cannot-slice-blob\";\n    StorageErrorCode[\"SERVER_FILE_WRONG_SIZE\"] = \"server-file-wrong-size\";\n    StorageErrorCode[\"NO_DOWNLOAD_URL\"] = \"no-download-url\";\n    StorageErrorCode[\"INVALID_ARGUMENT\"] = \"invalid-argument\";\n    StorageErrorCode[\"INVALID_ARGUMENT_COUNT\"] = \"invalid-argument-count\";\n    StorageErrorCode[\"APP_DELETED\"] = \"app-deleted\";\n    StorageErrorCode[\"INVALID_ROOT_OPERATION\"] = \"invalid-root-operation\";\n    StorageErrorCode[\"INVALID_FORMAT\"] = \"invalid-format\";\n    StorageErrorCode[\"INTERNAL_ERROR\"] = \"internal-error\";\n    StorageErrorCode[\"UNSUPPORTED_ENVIRONMENT\"] = \"unsupported-environment\";\n})(StorageErrorCode || (StorageErrorCode = {}));\nfunction prependCode(code) {\n    return 'storage/' + code;\n}\nfunction unknown() {\n    const message = 'An unknown error occurred, please check the error payload for ' +\n        'server response.';\n    return new StorageError(StorageErrorCode.UNKNOWN, message);\n}\nfunction objectNotFound(path) {\n    return new StorageError(StorageErrorCode.OBJECT_NOT_FOUND, \"Object '\" + path + \"' does not exist.\");\n}\nfunction quotaExceeded(bucket) {\n    return new StorageError(StorageErrorCode.QUOTA_EXCEEDED, \"Quota for bucket '\" +\n        bucket +\n        \"' exceeded, please view quota on \" +\n        'https://firebase.google.com/pricing/.');\n}\nfunction unauthenticated() {\n    const message = 'User is not authenticated, please authenticate using Firebase ' +\n        'Authentication and try again.';\n    return new StorageError(StorageErrorCode.UNAUTHENTICATED, message);\n}\nfunction unauthorizedApp() {\n    return new StorageError(StorageErrorCode.UNAUTHORIZED_APP, 'This app does not have permission to access Firebase Storage on this project.');\n}\nfunction unauthorized(path) {\n    return new StorageError(StorageErrorCode.UNAUTHORIZED, \"User does not have permission to access '\" + path + \"'.\");\n}\nfunction retryLimitExceeded() {\n    return new StorageError(StorageErrorCode.RETRY_LIMIT_EXCEEDED, 'Max retry time for operation exceeded, please try again.');\n}\nfunction canceled() {\n    return new StorageError(StorageErrorCode.CANCELED, 'User canceled the upload/download.');\n}\nfunction invalidUrl(url) {\n    return new StorageError(StorageErrorCode.INVALID_URL, \"Invalid URL '\" + url + \"'.\");\n}\nfunction invalidDefaultBucket(bucket) {\n    return new StorageError(StorageErrorCode.INVALID_DEFAULT_BUCKET, \"Invalid default bucket '\" + bucket + \"'.\");\n}\nfunction noDefaultBucket() {\n    return new StorageError(StorageErrorCode.NO_DEFAULT_BUCKET, 'No default bucket ' +\n        \"found. Did you set the '\" +\n        CONFIG_STORAGE_BUCKET_KEY +\n        \"' property when initializing the app?\");\n}\nfunction cannotSliceBlob() {\n    return new StorageError(StorageErrorCode.CANNOT_SLICE_BLOB, 'Cannot slice blob for upload. Please retry the upload.');\n}\nfunction serverFileWrongSize() {\n    return new StorageError(StorageErrorCode.SERVER_FILE_WRONG_SIZE, 'Server recorded incorrect upload file size, please retry the upload.');\n}\nfunction noDownloadURL() {\n    return new StorageError(StorageErrorCode.NO_DOWNLOAD_URL, 'The given file does not have any download URLs.');\n}\n/**\n * @internal\n */\nfunction invalidArgument(message) {\n    return new StorageError(StorageErrorCode.INVALID_ARGUMENT, message);\n}\nfunction appDeleted() {\n    return new StorageError(StorageErrorCode.APP_DELETED, 'The Firebase app was deleted.');\n}\n/**\n * @param name - The name of the operation that was invalid.\n *\n * @internal\n */\nfunction invalidRootOperation(name) {\n    return new StorageError(StorageErrorCode.INVALID_ROOT_OPERATION, \"The operation '\" +\n        name +\n        \"' cannot be performed on a root reference, create a non-root \" +\n        \"reference using child, such as .child('file.png').\");\n}\n/**\n * @param format - The format that was not valid.\n * @param message - A message describing the format violation.\n */\nfunction invalidFormat(format, message) {\n    return new StorageError(StorageErrorCode.INVALID_FORMAT, \"String does not match format '\" + format + \"': \" + message);\n}\n/**\n * @param message - A message describing the internal error.\n */\nfunction internalError(message) {\n    throw new StorageError(StorageErrorCode.INTERNAL_ERROR, 'Internal error: ' + message);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Firebase Storage location data.\n *\n * @internal\n */\nclass Location {\n    constructor(bucket, path) {\n        this.bucket = bucket;\n        this.path_ = path;\n    }\n    get path() {\n        return this.path_;\n    }\n    get isRoot() {\n        return this.path.length === 0;\n    }\n    fullServerUrl() {\n        const encode = encodeURIComponent;\n        return '/b/' + encode(this.bucket) + '/o/' + encode(this.path);\n    }\n    bucketOnlyServerUrl() {\n        const encode = encodeURIComponent;\n        return '/b/' + encode(this.bucket) + '/o';\n    }\n    static makeFromBucketSpec(bucketString, host) {\n        let bucketLocation;\n        try {\n            bucketLocation = Location.makeFromUrl(bucketString, host);\n        }\n        catch (e) {\n            // Not valid URL, use as-is. This lets you put bare bucket names in\n            // config.\n            return new Location(bucketString, '');\n        }\n        if (bucketLocation.path === '') {\n            return bucketLocation;\n        }\n        else {\n            throw invalidDefaultBucket(bucketString);\n        }\n    }\n    static makeFromUrl(url, host) {\n        let location = null;\n        const bucketDomain = '([A-Za-z0-9.\\\\-_]+)';\n        function gsModify(loc) {\n            if (loc.path.charAt(loc.path.length - 1) === '/') {\n                loc.path_ = loc.path_.slice(0, -1);\n            }\n        }\n        const gsPath = '(/(.*))?$';\n        const gsRegex = new RegExp('^gs://' + bucketDomain + gsPath, 'i');\n        const gsIndices = { bucket: 1, path: 3 };\n        function httpModify(loc) {\n            loc.path_ = decodeURIComponent(loc.path);\n        }\n        const version = 'v[A-Za-z0-9_]+';\n        const firebaseStorageHost = host.replace(/[.]/g, '\\\\.');\n        const firebaseStoragePath = '(/([^?#]*).*)?$';\n        const firebaseStorageRegExp = new RegExp(`^https?://${firebaseStorageHost}/${version}/b/${bucketDomain}/o${firebaseStoragePath}`, 'i');\n        const firebaseStorageIndices = { bucket: 1, path: 3 };\n        const cloudStorageHost = host === DEFAULT_HOST\n            ? '(?:storage.googleapis.com|storage.cloud.google.com)'\n            : host;\n        const cloudStoragePath = '([^?#]*)';\n        const cloudStorageRegExp = new RegExp(`^https?://${cloudStorageHost}/${bucketDomain}/${cloudStoragePath}`, 'i');\n        const cloudStorageIndices = { bucket: 1, path: 2 };\n        const groups = [\n            { regex: gsRegex, indices: gsIndices, postModify: gsModify },\n            {\n                regex: firebaseStorageRegExp,\n                indices: firebaseStorageIndices,\n                postModify: httpModify\n            },\n            {\n                regex: cloudStorageRegExp,\n                indices: cloudStorageIndices,\n                postModify: httpModify\n            }\n        ];\n        for (let i = 0; i < groups.length; i++) {\n            const group = groups[i];\n            const captures = group.regex.exec(url);\n            if (captures) {\n                const bucketValue = captures[group.indices.bucket];\n                let pathValue = captures[group.indices.path];\n                if (!pathValue) {\n                    pathValue = '';\n                }\n                location = new Location(bucketValue, pathValue);\n                group.postModify(location);\n                break;\n            }\n        }\n        if (location == null) {\n            throw invalidUrl(url);\n        }\n        return location;\n    }\n}\n\n/**\n * A request whose promise always fails.\n */\nclass FailRequest {\n    constructor(error) {\n        this.promise_ = Promise.reject(error);\n    }\n    /** @inheritDoc */\n    getPromise() {\n        return this.promise_;\n    }\n    /** @inheritDoc */\n    cancel(_appDelete = false) { }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Accepts a callback for an action to perform (`doRequest`),\n * and then a callback for when the backoff has completed (`backoffCompleteCb`).\n * The callback sent to start requires an argument to call (`onRequestComplete`).\n * When `start` calls `doRequest`, it passes a callback for when the request has\n * completed, `onRequestComplete`. Based on this, the backoff continues, with\n * another call to `doRequest` and the above loop continues until the timeout\n * is hit, or a successful response occurs.\n * @description\n * @param doRequest Callback to perform request\n * @param backoffCompleteCb Callback to call when backoff has been completed\n */\nfunction start(doRequest, \n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nbackoffCompleteCb, timeout) {\n    // TODO(andysoto): make this code cleaner (probably refactor into an actual\n    // type instead of a bunch of functions with state shared in the closure)\n    let waitSeconds = 1;\n    // Would type this as \"number\" but that doesn't work for Node so ¯\\_(ツ)_/¯\n    // TODO: find a way to exclude Node type definition for storage because storage only works in browser\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    let retryTimeoutId = null;\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    let globalTimeoutId = null;\n    let hitTimeout = false;\n    let cancelState = 0;\n    function canceled() {\n        return cancelState === 2;\n    }\n    let triggeredCallback = false;\n    function triggerCallback(...args) {\n        if (!triggeredCallback) {\n            triggeredCallback = true;\n            backoffCompleteCb.apply(null, args);\n        }\n    }\n    function callWithDelay(millis) {\n        retryTimeoutId = setTimeout(() => {\n            retryTimeoutId = null;\n            doRequest(responseHandler, canceled());\n        }, millis);\n    }\n    function clearGlobalTimeout() {\n        if (globalTimeoutId) {\n            clearTimeout(globalTimeoutId);\n        }\n    }\n    function responseHandler(success, ...args) {\n        if (triggeredCallback) {\n            clearGlobalTimeout();\n            return;\n        }\n        if (success) {\n            clearGlobalTimeout();\n            triggerCallback.call(null, success, ...args);\n            return;\n        }\n        const mustStop = canceled() || hitTimeout;\n        if (mustStop) {\n            clearGlobalTimeout();\n            triggerCallback.call(null, success, ...args);\n            return;\n        }\n        if (waitSeconds < 64) {\n            /* TODO(andysoto): don't back off so quickly if we know we're offline. */\n            waitSeconds *= 2;\n        }\n        let waitMillis;\n        if (cancelState === 1) {\n            cancelState = 2;\n            waitMillis = 0;\n        }\n        else {\n            waitMillis = (waitSeconds + Math.random()) * 1000;\n        }\n        callWithDelay(waitMillis);\n    }\n    let stopped = false;\n    function stop(wasTimeout) {\n        if (stopped) {\n            return;\n        }\n        stopped = true;\n        clearGlobalTimeout();\n        if (triggeredCallback) {\n            return;\n        }\n        if (retryTimeoutId !== null) {\n            if (!wasTimeout) {\n                cancelState = 2;\n            }\n            clearTimeout(retryTimeoutId);\n            callWithDelay(0);\n        }\n        else {\n            if (!wasTimeout) {\n                cancelState = 1;\n            }\n        }\n    }\n    callWithDelay(0);\n    globalTimeoutId = setTimeout(() => {\n        hitTimeout = true;\n        stop(true);\n    }, timeout);\n    return stop;\n}\n/**\n * Stops the retry loop from repeating.\n * If the function is currently \"in between\" retries, it is invoked immediately\n * with the second parameter as \"true\". Otherwise, it will be invoked once more\n * after the current invocation finishes iff the current invocation would have\n * triggered another retry.\n */\nfunction stop(id) {\n    id(false);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction isJustDef(p) {\n    return p !== void 0;\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction isFunction(p) {\n    return typeof p === 'function';\n}\nfunction isNonArrayObject(p) {\n    return typeof p === 'object' && !Array.isArray(p);\n}\nfunction isString(p) {\n    return typeof p === 'string' || p instanceof String;\n}\nfunction isNativeBlob(p) {\n    return isNativeBlobDefined() && p instanceof Blob;\n}\nfunction isNativeBlobDefined() {\n    return typeof Blob !== 'undefined';\n}\nfunction validateNumber(argument, minValue, maxValue, value) {\n    if (value < minValue) {\n        throw invalidArgument(`Invalid value for '${argument}'. Expected ${minValue} or greater.`);\n    }\n    if (value > maxValue) {\n        throw invalidArgument(`Invalid value for '${argument}'. Expected ${maxValue} or less.`);\n    }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction makeUrl(urlPart, host, protocol) {\n    let origin = host;\n    if (protocol == null) {\n        origin = `https://${host}`;\n    }\n    return `${protocol}://${origin}/v0${urlPart}`;\n}\nfunction makeQueryString(params) {\n    const encode = encodeURIComponent;\n    let queryPart = '?';\n    for (const key in params) {\n        if (params.hasOwnProperty(key)) {\n            const nextPart = encode(key) + '=' + encode(params[key]);\n            queryPart = queryPart + nextPart + '&';\n        }\n    }\n    // Chop off the extra '&' or '?' on the end\n    queryPart = queryPart.slice(0, -1);\n    return queryPart;\n}\n\n/**\n * Error codes for requests made by the XhrIo wrapper.\n */\nvar ErrorCode;\n(function (ErrorCode) {\n    ErrorCode[ErrorCode[\"NO_ERROR\"] = 0] = \"NO_ERROR\";\n    ErrorCode[ErrorCode[\"NETWORK_ERROR\"] = 1] = \"NETWORK_ERROR\";\n    ErrorCode[ErrorCode[\"ABORT\"] = 2] = \"ABORT\";\n})(ErrorCode || (ErrorCode = {}));\n\n/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Checks the status code to see if the action should be retried.\n *\n * @param status Current HTTP status code returned by server.\n * @param additionalRetryCodes additional retry codes to check against\n */\nfunction isRetryStatusCode(status, additionalRetryCodes) {\n    // The codes for which to retry came from this page:\n    // https://cloud.google.com/storage/docs/exponential-backoff\n    const isFiveHundredCode = status >= 500 && status < 600;\n    const extraRetryCodes = [\n        // Request Timeout: web server didn't receive full request in time.\n        408,\n        // Too Many Requests: you're getting rate-limited, basically.\n        429\n    ];\n    const isExtraRetryCode = extraRetryCodes.indexOf(status) !== -1;\n    const isAdditionalRetryCode = additionalRetryCodes.indexOf(status) !== -1;\n    return isFiveHundredCode || isExtraRetryCode || isAdditionalRetryCode;\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Handles network logic for all Storage Requests, including error reporting and\n * retries with backoff.\n *\n * @param I - the type of the backend's network response.\n * @param - O the output type used by the rest of the SDK. The conversion\n * happens in the specified `callback_`.\n */\nclass NetworkRequest {\n    constructor(url_, method_, headers_, body_, successCodes_, additionalRetryCodes_, callback_, errorCallback_, timeout_, progressCallback_, connectionFactory_, retry = true, isUsingEmulator = false) {\n        this.url_ = url_;\n        this.method_ = method_;\n        this.headers_ = headers_;\n        this.body_ = body_;\n        this.successCodes_ = successCodes_;\n        this.additionalRetryCodes_ = additionalRetryCodes_;\n        this.callback_ = callback_;\n        this.errorCallback_ = errorCallback_;\n        this.timeout_ = timeout_;\n        this.progressCallback_ = progressCallback_;\n        this.connectionFactory_ = connectionFactory_;\n        this.retry = retry;\n        this.isUsingEmulator = isUsingEmulator;\n        this.pendingConnection_ = null;\n        this.backoffId_ = null;\n        this.canceled_ = false;\n        this.appDelete_ = false;\n        this.promise_ = new Promise((resolve, reject) => {\n            this.resolve_ = resolve;\n            this.reject_ = reject;\n            this.start_();\n        });\n    }\n    /**\n     * Actually starts the retry loop.\n     */\n    start_() {\n        const doTheRequest = (backoffCallback, canceled) => {\n            if (canceled) {\n                backoffCallback(false, new RequestEndStatus(false, null, true));\n                return;\n            }\n            const connection = this.connectionFactory_();\n            this.pendingConnection_ = connection;\n            const progressListener = progressEvent => {\n                const loaded = progressEvent.loaded;\n                const total = progressEvent.lengthComputable ? progressEvent.total : -1;\n                if (this.progressCallback_ !== null) {\n                    this.progressCallback_(loaded, total);\n                }\n            };\n            if (this.progressCallback_ !== null) {\n                connection.addUploadProgressListener(progressListener);\n            }\n            // connection.send() never rejects, so we don't need to have a error handler or use catch on the returned promise.\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            connection\n                .send(this.url_, this.method_, this.isUsingEmulator, this.body_, this.headers_)\n                .then(() => {\n                if (this.progressCallback_ !== null) {\n                    connection.removeUploadProgressListener(progressListener);\n                }\n                this.pendingConnection_ = null;\n                const hitServer = connection.getErrorCode() === ErrorCode.NO_ERROR;\n                const status = connection.getStatus();\n                if (!hitServer ||\n                    (isRetryStatusCode(status, this.additionalRetryCodes_) &&\n                        this.retry)) {\n                    const wasCanceled = connection.getErrorCode() === ErrorCode.ABORT;\n                    backoffCallback(false, new RequestEndStatus(false, null, wasCanceled));\n                    return;\n                }\n                const successCode = this.successCodes_.indexOf(status) !== -1;\n                backoffCallback(true, new RequestEndStatus(successCode, connection));\n            });\n        };\n        /**\n         * @param requestWentThrough - True if the request eventually went\n         *     through, false if it hit the retry limit or was canceled.\n         */\n        const backoffDone = (requestWentThrough, status) => {\n            const resolve = this.resolve_;\n            const reject = this.reject_;\n            const connection = status.connection;\n            if (status.wasSuccessCode) {\n                try {\n                    const result = this.callback_(connection, connection.getResponse());\n                    if (isJustDef(result)) {\n                        resolve(result);\n                    }\n                    else {\n                        resolve();\n                    }\n                }\n                catch (e) {\n                    reject(e);\n                }\n            }\n            else {\n                if (connection !== null) {\n                    const err = unknown();\n                    err.serverResponse = connection.getErrorText();\n                    if (this.errorCallback_) {\n                        reject(this.errorCallback_(connection, err));\n                    }\n                    else {\n                        reject(err);\n                    }\n                }\n                else {\n                    if (status.canceled) {\n                        const err = this.appDelete_ ? appDeleted() : canceled();\n                        reject(err);\n                    }\n                    else {\n                        const err = retryLimitExceeded();\n                        reject(err);\n                    }\n                }\n            }\n        };\n        if (this.canceled_) {\n            backoffDone(false, new RequestEndStatus(false, null, true));\n        }\n        else {\n            this.backoffId_ = start(doTheRequest, backoffDone, this.timeout_);\n        }\n    }\n    /** @inheritDoc */\n    getPromise() {\n        return this.promise_;\n    }\n    /** @inheritDoc */\n    cancel(appDelete) {\n        this.canceled_ = true;\n        this.appDelete_ = appDelete || false;\n        if (this.backoffId_ !== null) {\n            stop(this.backoffId_);\n        }\n        if (this.pendingConnection_ !== null) {\n            this.pendingConnection_.abort();\n        }\n    }\n}\n/**\n * A collection of information about the result of a network request.\n * @param opt_canceled - Defaults to false.\n */\nclass RequestEndStatus {\n    constructor(wasSuccessCode, connection, canceled) {\n        this.wasSuccessCode = wasSuccessCode;\n        this.connection = connection;\n        this.canceled = !!canceled;\n    }\n}\nfunction addAuthHeader_(headers, authToken) {\n    if (authToken !== null && authToken.length > 0) {\n        headers['Authorization'] = 'Firebase ' + authToken;\n    }\n}\nfunction addVersionHeader_(headers, firebaseVersion) {\n    headers['X-Firebase-Storage-Version'] =\n        'webjs/' + (firebaseVersion !== null && firebaseVersion !== void 0 ? firebaseVersion : 'AppManager');\n}\nfunction addGmpidHeader_(headers, appId) {\n    if (appId) {\n        headers['X-Firebase-GMPID'] = appId;\n    }\n}\nfunction addAppCheckHeader_(headers, appCheckToken) {\n    if (appCheckToken !== null) {\n        headers['X-Firebase-AppCheck'] = appCheckToken;\n    }\n}\nfunction makeRequest(requestInfo, appId, authToken, appCheckToken, requestFactory, firebaseVersion, retry = true, isUsingEmulator = false) {\n    const queryPart = makeQueryString(requestInfo.urlParams);\n    const url = requestInfo.url + queryPart;\n    const headers = Object.assign({}, requestInfo.headers);\n    addGmpidHeader_(headers, appId);\n    addAuthHeader_(headers, authToken);\n    addVersionHeader_(headers, firebaseVersion);\n    addAppCheckHeader_(headers, appCheckToken);\n    return new NetworkRequest(url, requestInfo.method, headers, requestInfo.body, requestInfo.successCodes, requestInfo.additionalRetryCodes, requestInfo.handler, requestInfo.errorHandler, requestInfo.timeout, requestInfo.progressCallback, requestFactory, retry, isUsingEmulator);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction getBlobBuilder() {\n    if (typeof BlobBuilder !== 'undefined') {\n        return BlobBuilder;\n    }\n    else if (typeof WebKitBlobBuilder !== 'undefined') {\n        return WebKitBlobBuilder;\n    }\n    else {\n        return undefined;\n    }\n}\n/**\n * Concatenates one or more values together and converts them to a Blob.\n *\n * @param args The values that will make up the resulting blob.\n * @return The blob.\n */\nfunction getBlob$1(...args) {\n    const BlobBuilder = getBlobBuilder();\n    if (BlobBuilder !== undefined) {\n        const bb = new BlobBuilder();\n        for (let i = 0; i < args.length; i++) {\n            bb.append(args[i]);\n        }\n        return bb.getBlob();\n    }\n    else {\n        if (isNativeBlobDefined()) {\n            return new Blob(args);\n        }\n        else {\n            throw new StorageError(StorageErrorCode.UNSUPPORTED_ENVIRONMENT, \"This browser doesn't seem to support creating Blobs\");\n        }\n    }\n}\n/**\n * Slices the blob. The returned blob contains data from the start byte\n * (inclusive) till the end byte (exclusive). Negative indices cannot be used.\n *\n * @param blob The blob to be sliced.\n * @param start Index of the starting byte.\n * @param end Index of the ending byte.\n * @return The blob slice or null if not supported.\n */\nfunction sliceBlob(blob, start, end) {\n    if (blob.webkitSlice) {\n        return blob.webkitSlice(start, end);\n    }\n    else if (blob.mozSlice) {\n        return blob.mozSlice(start, end);\n    }\n    else if (blob.slice) {\n        return blob.slice(start, end);\n    }\n    return null;\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/** Converts a Base64 encoded string to a binary string. */\nfunction decodeBase64(encoded) {\n    // Node actually doesn't validate base64 strings.\n    // A quick sanity check that is not a fool-proof validation\n    if (/[^-A-Za-z0-9+/=]/.test(encoded)) {\n        throw invalidFormat('base64', 'Invalid character found');\n    }\n    return Buffer.from(encoded, 'base64').toString('binary');\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * An enumeration of the possible string formats for upload.\n * @public\n */\nconst StringFormat = {\n    /**\n     * Indicates the string should be interpreted \"raw\", that is, as normal text.\n     * The string will be interpreted as UTF-16, then uploaded as a UTF-8 byte\n     * sequence.\n     * Example: The string 'Hello! \\\\ud83d\\\\ude0a' becomes the byte sequence\n     * 48 65 6c 6c 6f 21 20 f0 9f 98 8a\n     */\n    RAW: 'raw',\n    /**\n     * Indicates the string should be interpreted as base64-encoded data.\n     * Padding characters (trailing '='s) are optional.\n     * Example: The string 'rWmO++E6t7/rlw==' becomes the byte sequence\n     * ad 69 8e fb e1 3a b7 bf eb 97\n     */\n    BASE64: 'base64',\n    /**\n     * Indicates the string should be interpreted as base64url-encoded data.\n     * Padding characters (trailing '='s) are optional.\n     * Example: The string 'rWmO--E6t7_rlw==' becomes the byte sequence\n     * ad 69 8e fb e1 3a b7 bf eb 97\n     */\n    BASE64URL: 'base64url',\n    /**\n     * Indicates the string is a data URL, such as one obtained from\n     * canvas.toDataURL().\n     * Example: the string 'data:application/octet-stream;base64,aaaa'\n     * becomes the byte sequence\n     * 69 a6 9a\n     * (the content-type \"application/octet-stream\" is also applied, but can\n     * be overridden in the metadata object).\n     */\n    DATA_URL: 'data_url'\n};\nclass StringData {\n    constructor(data, contentType) {\n        this.data = data;\n        this.contentType = contentType || null;\n    }\n}\n/**\n * @internal\n */\nfunction dataFromString(format, stringData) {\n    switch (format) {\n        case StringFormat.RAW:\n            return new StringData(utf8Bytes_(stringData));\n        case StringFormat.BASE64:\n        case StringFormat.BASE64URL:\n            return new StringData(base64Bytes_(format, stringData));\n        case StringFormat.DATA_URL:\n            return new StringData(dataURLBytes_(stringData), dataURLContentType_(stringData));\n        // do nothing\n    }\n    // assert(false);\n    throw unknown();\n}\nfunction utf8Bytes_(value) {\n    const b = [];\n    for (let i = 0; i < value.length; i++) {\n        let c = value.charCodeAt(i);\n        if (c <= 127) {\n            b.push(c);\n        }\n        else {\n            if (c <= 2047) {\n                b.push(192 | (c >> 6), 128 | (c & 63));\n            }\n            else {\n                if ((c & 64512) === 55296) {\n                    // The start of a surrogate pair.\n                    const valid = i < value.length - 1 && (value.charCodeAt(i + 1) & 64512) === 56320;\n                    if (!valid) {\n                        // The second surrogate wasn't there.\n                        b.push(239, 191, 189);\n                    }\n                    else {\n                        const hi = c;\n                        const lo = value.charCodeAt(++i);\n                        c = 65536 | ((hi & 1023) << 10) | (lo & 1023);\n                        b.push(240 | (c >> 18), 128 | ((c >> 12) & 63), 128 | ((c >> 6) & 63), 128 | (c & 63));\n                    }\n                }\n                else {\n                    if ((c & 64512) === 56320) {\n                        // Invalid low surrogate.\n                        b.push(239, 191, 189);\n                    }\n                    else {\n                        b.push(224 | (c >> 12), 128 | ((c >> 6) & 63), 128 | (c & 63));\n                    }\n                }\n            }\n        }\n    }\n    return new Uint8Array(b);\n}\nfunction percentEncodedBytes_(value) {\n    let decoded;\n    try {\n        decoded = decodeURIComponent(value);\n    }\n    catch (e) {\n        throw invalidFormat(StringFormat.DATA_URL, 'Malformed data URL.');\n    }\n    return utf8Bytes_(decoded);\n}\nfunction base64Bytes_(format, value) {\n    switch (format) {\n        case StringFormat.BASE64: {\n            const hasMinus = value.indexOf('-') !== -1;\n            const hasUnder = value.indexOf('_') !== -1;\n            if (hasMinus || hasUnder) {\n                const invalidChar = hasMinus ? '-' : '_';\n                throw invalidFormat(format, \"Invalid character '\" +\n                    invalidChar +\n                    \"' found: is it base64url encoded?\");\n            }\n            break;\n        }\n        case StringFormat.BASE64URL: {\n            const hasPlus = value.indexOf('+') !== -1;\n            const hasSlash = value.indexOf('/') !== -1;\n            if (hasPlus || hasSlash) {\n                const invalidChar = hasPlus ? '+' : '/';\n                throw invalidFormat(format, \"Invalid character '\" + invalidChar + \"' found: is it base64 encoded?\");\n            }\n            value = value.replace(/-/g, '+').replace(/_/g, '/');\n            break;\n        }\n        // do nothing\n    }\n    let bytes;\n    try {\n        bytes = decodeBase64(value);\n    }\n    catch (e) {\n        if (e.message.includes('polyfill')) {\n            throw e;\n        }\n        throw invalidFormat(format, 'Invalid character found');\n    }\n    const array = new Uint8Array(bytes.length);\n    for (let i = 0; i < bytes.length; i++) {\n        array[i] = bytes.charCodeAt(i);\n    }\n    return array;\n}\nclass DataURLParts {\n    constructor(dataURL) {\n        this.base64 = false;\n        this.contentType = null;\n        const matches = dataURL.match(/^data:([^,]+)?,/);\n        if (matches === null) {\n            throw invalidFormat(StringFormat.DATA_URL, \"Must be formatted 'data:[<mediatype>][;base64],<data>\");\n        }\n        const middle = matches[1] || null;\n        if (middle != null) {\n            this.base64 = endsWith(middle, ';base64');\n            this.contentType = this.base64\n                ? middle.substring(0, middle.length - ';base64'.length)\n                : middle;\n        }\n        this.rest = dataURL.substring(dataURL.indexOf(',') + 1);\n    }\n}\nfunction dataURLBytes_(dataUrl) {\n    const parts = new DataURLParts(dataUrl);\n    if (parts.base64) {\n        return base64Bytes_(StringFormat.BASE64, parts.rest);\n    }\n    else {\n        return percentEncodedBytes_(parts.rest);\n    }\n}\nfunction dataURLContentType_(dataUrl) {\n    const parts = new DataURLParts(dataUrl);\n    return parts.contentType;\n}\nfunction endsWith(s, end) {\n    const longEnough = s.length >= end.length;\n    if (!longEnough) {\n        return false;\n    }\n    return s.substring(s.length - end.length) === end;\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @param opt_elideCopy - If true, doesn't copy mutable input data\n *     (e.g. Uint8Arrays). Pass true only if you know the objects will not be\n *     modified after this blob's construction.\n *\n * @internal\n */\nclass FbsBlob {\n    constructor(data, elideCopy) {\n        let size = 0;\n        let blobType = '';\n        if (isNativeBlob(data)) {\n            this.data_ = data;\n            size = data.size;\n            blobType = data.type;\n        }\n        else if (data instanceof ArrayBuffer) {\n            if (elideCopy) {\n                this.data_ = new Uint8Array(data);\n            }\n            else {\n                this.data_ = new Uint8Array(data.byteLength);\n                this.data_.set(new Uint8Array(data));\n            }\n            size = this.data_.length;\n        }\n        else if (data instanceof Uint8Array) {\n            if (elideCopy) {\n                this.data_ = data;\n            }\n            else {\n                this.data_ = new Uint8Array(data.length);\n                this.data_.set(data);\n            }\n            size = data.length;\n        }\n        this.size_ = size;\n        this.type_ = blobType;\n    }\n    size() {\n        return this.size_;\n    }\n    type() {\n        return this.type_;\n    }\n    slice(startByte, endByte) {\n        if (isNativeBlob(this.data_)) {\n            const realBlob = this.data_;\n            const sliced = sliceBlob(realBlob, startByte, endByte);\n            if (sliced === null) {\n                return null;\n            }\n            return new FbsBlob(sliced);\n        }\n        else {\n            const slice = new Uint8Array(this.data_.buffer, startByte, endByte - startByte);\n            return new FbsBlob(slice, true);\n        }\n    }\n    static getBlob(...args) {\n        if (isNativeBlobDefined()) {\n            const blobby = args.map((val) => {\n                if (val instanceof FbsBlob) {\n                    return val.data_;\n                }\n                else {\n                    return val;\n                }\n            });\n            return new FbsBlob(getBlob$1.apply(null, blobby));\n        }\n        else {\n            const uint8Arrays = args.map((val) => {\n                if (isString(val)) {\n                    return dataFromString(StringFormat.RAW, val).data;\n                }\n                else {\n                    // Blobs don't exist, so this has to be a Uint8Array.\n                    return val.data_;\n                }\n            });\n            let finalLength = 0;\n            uint8Arrays.forEach((array) => {\n                finalLength += array.byteLength;\n            });\n            const merged = new Uint8Array(finalLength);\n            let index = 0;\n            uint8Arrays.forEach((array) => {\n                for (let i = 0; i < array.length; i++) {\n                    merged[index++] = array[i];\n                }\n            });\n            return new FbsBlob(merged, true);\n        }\n    }\n    uploadData() {\n        return this.data_;\n    }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Returns the Object resulting from parsing the given JSON, or null if the\n * given string does not represent a JSON object.\n */\nfunction jsonObjectOrNull(s) {\n    let obj;\n    try {\n        obj = JSON.parse(s);\n    }\n    catch (e) {\n        return null;\n    }\n    if (isNonArrayObject(obj)) {\n        return obj;\n    }\n    else {\n        return null;\n    }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Contains helper methods for manipulating paths.\n */\n/**\n * @return Null if the path is already at the root.\n */\nfunction parent(path) {\n    if (path.length === 0) {\n        return null;\n    }\n    const index = path.lastIndexOf('/');\n    if (index === -1) {\n        return '';\n    }\n    const newPath = path.slice(0, index);\n    return newPath;\n}\nfunction child(path, childPath) {\n    const canonicalChildPath = childPath\n        .split('/')\n        .filter(component => component.length > 0)\n        .join('/');\n    if (path.length === 0) {\n        return canonicalChildPath;\n    }\n    else {\n        return path + '/' + canonicalChildPath;\n    }\n}\n/**\n * Returns the last component of a path.\n * '/foo/bar' -> 'bar'\n * '/foo/bar/baz/' -> 'baz/'\n * '/a' -> 'a'\n */\nfunction lastComponent(path) {\n    const index = path.lastIndexOf('/', path.length - 2);\n    if (index === -1) {\n        return path;\n    }\n    else {\n        return path.slice(index + 1);\n    }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction noXform_(metadata, value) {\n    return value;\n}\nclass Mapping {\n    constructor(server, local, writable, xform) {\n        this.server = server;\n        this.local = local || server;\n        this.writable = !!writable;\n        this.xform = xform || noXform_;\n    }\n}\nlet mappings_ = null;\nfunction xformPath(fullPath) {\n    if (!isString(fullPath) || fullPath.length < 2) {\n        return fullPath;\n    }\n    else {\n        return lastComponent(fullPath);\n    }\n}\nfunction getMappings() {\n    if (mappings_) {\n        return mappings_;\n    }\n    const mappings = [];\n    mappings.push(new Mapping('bucket'));\n    mappings.push(new Mapping('generation'));\n    mappings.push(new Mapping('metageneration'));\n    mappings.push(new Mapping('name', 'fullPath', true));\n    function mappingsXformPath(_metadata, fullPath) {\n        return xformPath(fullPath);\n    }\n    const nameMapping = new Mapping('name');\n    nameMapping.xform = mappingsXformPath;\n    mappings.push(nameMapping);\n    /**\n     * Coerces the second param to a number, if it is defined.\n     */\n    function xformSize(_metadata, size) {\n        if (size !== undefined) {\n            return Number(size);\n        }\n        else {\n            return size;\n        }\n    }\n    const sizeMapping = new Mapping('size');\n    sizeMapping.xform = xformSize;\n    mappings.push(sizeMapping);\n    mappings.push(new Mapping('timeCreated'));\n    mappings.push(new Mapping('updated'));\n    mappings.push(new Mapping('md5Hash', null, true));\n    mappings.push(new Mapping('cacheControl', null, true));\n    mappings.push(new Mapping('contentDisposition', null, true));\n    mappings.push(new Mapping('contentEncoding', null, true));\n    mappings.push(new Mapping('contentLanguage', null, true));\n    mappings.push(new Mapping('contentType', null, true));\n    mappings.push(new Mapping('metadata', 'customMetadata', true));\n    mappings_ = mappings;\n    return mappings_;\n}\nfunction addRef(metadata, service) {\n    function generateRef() {\n        const bucket = metadata['bucket'];\n        const path = metadata['fullPath'];\n        const loc = new Location(bucket, path);\n        return service._makeStorageReference(loc);\n    }\n    Object.defineProperty(metadata, 'ref', { get: generateRef });\n}\nfunction fromResource(service, resource, mappings) {\n    const metadata = {};\n    metadata['type'] = 'file';\n    const len = mappings.length;\n    for (let i = 0; i < len; i++) {\n        const mapping = mappings[i];\n        metadata[mapping.local] = mapping.xform(metadata, resource[mapping.server]);\n    }\n    addRef(metadata, service);\n    return metadata;\n}\nfunction fromResourceString(service, resourceString, mappings) {\n    const obj = jsonObjectOrNull(resourceString);\n    if (obj === null) {\n        return null;\n    }\n    const resource = obj;\n    return fromResource(service, resource, mappings);\n}\nfunction downloadUrlFromResourceString(metadata, resourceString, host, protocol) {\n    const obj = jsonObjectOrNull(resourceString);\n    if (obj === null) {\n        return null;\n    }\n    if (!isString(obj['downloadTokens'])) {\n        // This can happen if objects are uploaded through GCS and retrieved\n        // through list, so we don't want to throw an Error.\n        return null;\n    }\n    const tokens = obj['downloadTokens'];\n    if (tokens.length === 0) {\n        return null;\n    }\n    const encode = encodeURIComponent;\n    const tokensList = tokens.split(',');\n    const urls = tokensList.map((token) => {\n        const bucket = metadata['bucket'];\n        const path = metadata['fullPath'];\n        const urlPart = '/b/' + encode(bucket) + '/o/' + encode(path);\n        const base = makeUrl(urlPart, host, protocol);\n        const queryString = makeQueryString({\n            alt: 'media',\n            token\n        });\n        return base + queryString;\n    });\n    return urls[0];\n}\nfunction toResourceString(metadata, mappings) {\n    const resource = {};\n    const len = mappings.length;\n    for (let i = 0; i < len; i++) {\n        const mapping = mappings[i];\n        if (mapping.writable) {\n            resource[mapping.server] = metadata[mapping.local];\n        }\n    }\n    return JSON.stringify(resource);\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst PREFIXES_KEY = 'prefixes';\nconst ITEMS_KEY = 'items';\nfunction fromBackendResponse(service, bucket, resource) {\n    const listResult = {\n        prefixes: [],\n        items: [],\n        nextPageToken: resource['nextPageToken']\n    };\n    if (resource[PREFIXES_KEY]) {\n        for (const path of resource[PREFIXES_KEY]) {\n            const pathWithoutTrailingSlash = path.replace(/\\/$/, '');\n            const reference = service._makeStorageReference(new Location(bucket, pathWithoutTrailingSlash));\n            listResult.prefixes.push(reference);\n        }\n    }\n    if (resource[ITEMS_KEY]) {\n        for (const item of resource[ITEMS_KEY]) {\n            const reference = service._makeStorageReference(new Location(bucket, item['name']));\n            listResult.items.push(reference);\n        }\n    }\n    return listResult;\n}\nfunction fromResponseString(service, bucket, resourceString) {\n    const obj = jsonObjectOrNull(resourceString);\n    if (obj === null) {\n        return null;\n    }\n    const resource = obj;\n    return fromBackendResponse(service, bucket, resource);\n}\n\n/**\n * Contains a fully specified request.\n *\n * @param I - the type of the backend's network response.\n * @param O - the output response type used by the rest of the SDK.\n */\nclass RequestInfo {\n    constructor(url, method, \n    /**\n     * Returns the value with which to resolve the request's promise. Only called\n     * if the request is successful. Throw from this function to reject the\n     * returned Request's promise with the thrown error.\n     * Note: The XhrIo passed to this function may be reused after this callback\n     * returns. Do not keep a reference to it in any way.\n     */\n    handler, timeout) {\n        this.url = url;\n        this.method = method;\n        this.handler = handler;\n        this.timeout = timeout;\n        this.urlParams = {};\n        this.headers = {};\n        this.body = null;\n        this.errorHandler = null;\n        /**\n         * Called with the current number of bytes uploaded and total size (-1 if not\n         * computable) of the request body (i.e. used to report upload progress).\n         */\n        this.progressCallback = null;\n        this.successCodes = [200];\n        this.additionalRetryCodes = [];\n    }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Throws the UNKNOWN StorageError if cndn is false.\n */\nfunction handlerCheck(cndn) {\n    if (!cndn) {\n        throw unknown();\n    }\n}\nfunction metadataHandler(service, mappings) {\n    function handler(xhr, text) {\n        const metadata = fromResourceString(service, text, mappings);\n        handlerCheck(metadata !== null);\n        return metadata;\n    }\n    return handler;\n}\nfunction listHandler(service, bucket) {\n    function handler(xhr, text) {\n        const listResult = fromResponseString(service, bucket, text);\n        handlerCheck(listResult !== null);\n        return listResult;\n    }\n    return handler;\n}\nfunction downloadUrlHandler(service, mappings) {\n    function handler(xhr, text) {\n        const metadata = fromResourceString(service, text, mappings);\n        handlerCheck(metadata !== null);\n        return downloadUrlFromResourceString(metadata, text, service.host, service._protocol);\n    }\n    return handler;\n}\nfunction sharedErrorHandler(location) {\n    function errorHandler(xhr, err) {\n        let newErr;\n        if (xhr.getStatus() === 401) {\n            if (\n            // This exact message string is the only consistent part of the\n            // server's error response that identifies it as an App Check error.\n            xhr.getErrorText().includes('Firebase App Check token is invalid')) {\n                newErr = unauthorizedApp();\n            }\n            else {\n                newErr = unauthenticated();\n            }\n        }\n        else {\n            if (xhr.getStatus() === 402) {\n                newErr = quotaExceeded(location.bucket);\n            }\n            else {\n                if (xhr.getStatus() === 403) {\n                    newErr = unauthorized(location.path);\n                }\n                else {\n                    newErr = err;\n                }\n            }\n        }\n        newErr.status = xhr.getStatus();\n        newErr.serverResponse = err.serverResponse;\n        return newErr;\n    }\n    return errorHandler;\n}\nfunction objectErrorHandler(location) {\n    const shared = sharedErrorHandler(location);\n    function errorHandler(xhr, err) {\n        let newErr = shared(xhr, err);\n        if (xhr.getStatus() === 404) {\n            newErr = objectNotFound(location.path);\n        }\n        newErr.serverResponse = err.serverResponse;\n        return newErr;\n    }\n    return errorHandler;\n}\nfunction getMetadata$2(service, location, mappings) {\n    const urlPart = location.fullServerUrl();\n    const url = makeUrl(urlPart, service.host, service._protocol);\n    const method = 'GET';\n    const timeout = service.maxOperationRetryTime;\n    const requestInfo = new RequestInfo(url, method, metadataHandler(service, mappings), timeout);\n    requestInfo.errorHandler = objectErrorHandler(location);\n    return requestInfo;\n}\nfunction list$2(service, location, delimiter, pageToken, maxResults) {\n    const urlParams = {};\n    if (location.isRoot) {\n        urlParams['prefix'] = '';\n    }\n    else {\n        urlParams['prefix'] = location.path + '/';\n    }\n    if (delimiter && delimiter.length > 0) {\n        urlParams['delimiter'] = delimiter;\n    }\n    if (pageToken) {\n        urlParams['pageToken'] = pageToken;\n    }\n    if (maxResults) {\n        urlParams['maxResults'] = maxResults;\n    }\n    const urlPart = location.bucketOnlyServerUrl();\n    const url = makeUrl(urlPart, service.host, service._protocol);\n    const method = 'GET';\n    const timeout = service.maxOperationRetryTime;\n    const requestInfo = new RequestInfo(url, method, listHandler(service, location.bucket), timeout);\n    requestInfo.urlParams = urlParams;\n    requestInfo.errorHandler = sharedErrorHandler(location);\n    return requestInfo;\n}\nfunction getBytes$1(service, location, maxDownloadSizeBytes) {\n    const urlPart = location.fullServerUrl();\n    const url = makeUrl(urlPart, service.host, service._protocol) + '?alt=media';\n    const method = 'GET';\n    const timeout = service.maxOperationRetryTime;\n    const requestInfo = new RequestInfo(url, method, (_, data) => data, timeout);\n    requestInfo.errorHandler = objectErrorHandler(location);\n    if (maxDownloadSizeBytes !== undefined) {\n        requestInfo.headers['Range'] = `bytes=0-${maxDownloadSizeBytes}`;\n        requestInfo.successCodes = [200 /* OK */, 206 /* Partial Content */];\n    }\n    return requestInfo;\n}\nfunction getDownloadUrl(service, location, mappings) {\n    const urlPart = location.fullServerUrl();\n    const url = makeUrl(urlPart, service.host, service._protocol);\n    const method = 'GET';\n    const timeout = service.maxOperationRetryTime;\n    const requestInfo = new RequestInfo(url, method, downloadUrlHandler(service, mappings), timeout);\n    requestInfo.errorHandler = objectErrorHandler(location);\n    return requestInfo;\n}\nfunction updateMetadata$2(service, location, metadata, mappings) {\n    const urlPart = location.fullServerUrl();\n    const url = makeUrl(urlPart, service.host, service._protocol);\n    const method = 'PATCH';\n    const body = toResourceString(metadata, mappings);\n    const headers = { 'Content-Type': 'application/json; charset=utf-8' };\n    const timeout = service.maxOperationRetryTime;\n    const requestInfo = new RequestInfo(url, method, metadataHandler(service, mappings), timeout);\n    requestInfo.headers = headers;\n    requestInfo.body = body;\n    requestInfo.errorHandler = objectErrorHandler(location);\n    return requestInfo;\n}\nfunction deleteObject$2(service, location) {\n    const urlPart = location.fullServerUrl();\n    const url = makeUrl(urlPart, service.host, service._protocol);\n    const method = 'DELETE';\n    const timeout = service.maxOperationRetryTime;\n    function handler(_xhr, _text) { }\n    const requestInfo = new RequestInfo(url, method, handler, timeout);\n    requestInfo.successCodes = [200, 204];\n    requestInfo.errorHandler = objectErrorHandler(location);\n    return requestInfo;\n}\nfunction determineContentType_(metadata, blob) {\n    return ((metadata && metadata['contentType']) ||\n        (blob && blob.type()) ||\n        'application/octet-stream');\n}\nfunction metadataForUpload_(location, blob, metadata) {\n    const metadataClone = Object.assign({}, metadata);\n    metadataClone['fullPath'] = location.path;\n    metadataClone['size'] = blob.size();\n    if (!metadataClone['contentType']) {\n        metadataClone['contentType'] = determineContentType_(null, blob);\n    }\n    return metadataClone;\n}\n/**\n * Prepare RequestInfo for uploads as Content-Type: multipart.\n */\nfunction multipartUpload(service, location, mappings, blob, metadata) {\n    const urlPart = location.bucketOnlyServerUrl();\n    const headers = {\n        'X-Goog-Upload-Protocol': 'multipart'\n    };\n    function genBoundary() {\n        let str = '';\n        for (let i = 0; i < 2; i++) {\n            str = str + Math.random().toString().slice(2);\n        }\n        return str;\n    }\n    const boundary = genBoundary();\n    headers['Content-Type'] = 'multipart/related; boundary=' + boundary;\n    const metadata_ = metadataForUpload_(location, blob, metadata);\n    const metadataString = toResourceString(metadata_, mappings);\n    const preBlobPart = '--' +\n        boundary +\n        '\\r\\n' +\n        'Content-Type: application/json; charset=utf-8\\r\\n\\r\\n' +\n        metadataString +\n        '\\r\\n--' +\n        boundary +\n        '\\r\\n' +\n        'Content-Type: ' +\n        metadata_['contentType'] +\n        '\\r\\n\\r\\n';\n    const postBlobPart = '\\r\\n--' + boundary + '--';\n    const body = FbsBlob.getBlob(preBlobPart, blob, postBlobPart);\n    if (body === null) {\n        throw cannotSliceBlob();\n    }\n    const urlParams = { name: metadata_['fullPath'] };\n    const url = makeUrl(urlPart, service.host, service._protocol);\n    const method = 'POST';\n    const timeout = service.maxUploadRetryTime;\n    const requestInfo = new RequestInfo(url, method, metadataHandler(service, mappings), timeout);\n    requestInfo.urlParams = urlParams;\n    requestInfo.headers = headers;\n    requestInfo.body = body.uploadData();\n    requestInfo.errorHandler = sharedErrorHandler(location);\n    return requestInfo;\n}\n/**\n * @param current The number of bytes that have been uploaded so far.\n * @param total The total number of bytes in the upload.\n * @param opt_finalized True if the server has finished the upload.\n * @param opt_metadata The upload metadata, should\n *     only be passed if opt_finalized is true.\n */\nclass ResumableUploadStatus {\n    constructor(current, total, finalized, metadata) {\n        this.current = current;\n        this.total = total;\n        this.finalized = !!finalized;\n        this.metadata = metadata || null;\n    }\n}\nfunction checkResumeHeader_(xhr, allowed) {\n    let status = null;\n    try {\n        status = xhr.getResponseHeader('X-Goog-Upload-Status');\n    }\n    catch (e) {\n        handlerCheck(false);\n    }\n    const allowedStatus = allowed || ['active'];\n    handlerCheck(!!status && allowedStatus.indexOf(status) !== -1);\n    return status;\n}\nfunction createResumableUpload(service, location, mappings, blob, metadata) {\n    const urlPart = location.bucketOnlyServerUrl();\n    const metadataForUpload = metadataForUpload_(location, blob, metadata);\n    const urlParams = { name: metadataForUpload['fullPath'] };\n    const url = makeUrl(urlPart, service.host, service._protocol);\n    const method = 'POST';\n    const headers = {\n        'X-Goog-Upload-Protocol': 'resumable',\n        'X-Goog-Upload-Command': 'start',\n        'X-Goog-Upload-Header-Content-Length': `${blob.size()}`,\n        'X-Goog-Upload-Header-Content-Type': metadataForUpload['contentType'],\n        'Content-Type': 'application/json; charset=utf-8'\n    };\n    const body = toResourceString(metadataForUpload, mappings);\n    const timeout = service.maxUploadRetryTime;\n    function handler(xhr) {\n        checkResumeHeader_(xhr);\n        let url;\n        try {\n            url = xhr.getResponseHeader('X-Goog-Upload-URL');\n        }\n        catch (e) {\n            handlerCheck(false);\n        }\n        handlerCheck(isString(url));\n        return url;\n    }\n    const requestInfo = new RequestInfo(url, method, handler, timeout);\n    requestInfo.urlParams = urlParams;\n    requestInfo.headers = headers;\n    requestInfo.body = body;\n    requestInfo.errorHandler = sharedErrorHandler(location);\n    return requestInfo;\n}\n/**\n * @param url From a call to fbs.requests.createResumableUpload.\n */\nfunction getResumableUploadStatus(service, location, url, blob) {\n    const headers = { 'X-Goog-Upload-Command': 'query' };\n    function handler(xhr) {\n        const status = checkResumeHeader_(xhr, ['active', 'final']);\n        let sizeString = null;\n        try {\n            sizeString = xhr.getResponseHeader('X-Goog-Upload-Size-Received');\n        }\n        catch (e) {\n            handlerCheck(false);\n        }\n        if (!sizeString) {\n            // null or empty string\n            handlerCheck(false);\n        }\n        const size = Number(sizeString);\n        handlerCheck(!isNaN(size));\n        return new ResumableUploadStatus(size, blob.size(), status === 'final');\n    }\n    const method = 'POST';\n    const timeout = service.maxUploadRetryTime;\n    const requestInfo = new RequestInfo(url, method, handler, timeout);\n    requestInfo.headers = headers;\n    requestInfo.errorHandler = sharedErrorHandler(location);\n    return requestInfo;\n}\n/**\n * Any uploads via the resumable upload API must transfer a number of bytes\n * that is a multiple of this number.\n */\nconst RESUMABLE_UPLOAD_CHUNK_SIZE = 256 * 1024;\n/**\n * @param url From a call to fbs.requests.createResumableUpload.\n * @param chunkSize Number of bytes to upload.\n * @param status The previous status.\n *     If not passed or null, we start from the beginning.\n * @throws fbs.Error If the upload is already complete, the passed in status\n *     has a final size inconsistent with the blob, or the blob cannot be sliced\n *     for upload.\n */\nfunction continueResumableUpload(location, service, url, blob, chunkSize, mappings, status, progressCallback) {\n    // TODO(andysoto): standardize on internal asserts\n    // assert(!(opt_status && opt_status.finalized));\n    const status_ = new ResumableUploadStatus(0, 0);\n    if (status) {\n        status_.current = status.current;\n        status_.total = status.total;\n    }\n    else {\n        status_.current = 0;\n        status_.total = blob.size();\n    }\n    if (blob.size() !== status_.total) {\n        throw serverFileWrongSize();\n    }\n    const bytesLeft = status_.total - status_.current;\n    let bytesToUpload = bytesLeft;\n    if (chunkSize > 0) {\n        bytesToUpload = Math.min(bytesToUpload, chunkSize);\n    }\n    const startByte = status_.current;\n    const endByte = startByte + bytesToUpload;\n    let uploadCommand = '';\n    if (bytesToUpload === 0) {\n        uploadCommand = 'finalize';\n    }\n    else if (bytesLeft === bytesToUpload) {\n        uploadCommand = 'upload, finalize';\n    }\n    else {\n        uploadCommand = 'upload';\n    }\n    const headers = {\n        'X-Goog-Upload-Command': uploadCommand,\n        'X-Goog-Upload-Offset': `${status_.current}`\n    };\n    const body = blob.slice(startByte, endByte);\n    if (body === null) {\n        throw cannotSliceBlob();\n    }\n    function handler(xhr, text) {\n        // TODO(andysoto): Verify the MD5 of each uploaded range:\n        // the 'x-range-md5' header comes back with status code 308 responses.\n        // We'll only be able to bail out though, because you can't re-upload a\n        // range that you previously uploaded.\n        const uploadStatus = checkResumeHeader_(xhr, ['active', 'final']);\n        const newCurrent = status_.current + bytesToUpload;\n        const size = blob.size();\n        let metadata;\n        if (uploadStatus === 'final') {\n            metadata = metadataHandler(service, mappings)(xhr, text);\n        }\n        else {\n            metadata = null;\n        }\n        return new ResumableUploadStatus(newCurrent, size, uploadStatus === 'final', metadata);\n    }\n    const method = 'POST';\n    const timeout = service.maxUploadRetryTime;\n    const requestInfo = new RequestInfo(url, method, handler, timeout);\n    requestInfo.headers = headers;\n    requestInfo.body = body.uploadData();\n    requestInfo.progressCallback = progressCallback || null;\n    requestInfo.errorHandler = sharedErrorHandler(location);\n    return requestInfo;\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * An event that is triggered on a task.\n * @internal\n */\nconst TaskEvent = {\n    /**\n     * For this event,\n     * <ul>\n     *   <li>The `next` function is triggered on progress updates and when the\n     *       task is paused/resumed with an `UploadTaskSnapshot` as the first\n     *       argument.</li>\n     *   <li>The `error` function is triggered if the upload is canceled or fails\n     *       for another reason.</li>\n     *   <li>The `complete` function is triggered if the upload completes\n     *       successfully.</li>\n     * </ul>\n     */\n    STATE_CHANGED: 'state_changed'\n};\n// type keys = keyof TaskState\n/**\n * Represents the current state of a running upload.\n * @internal\n */\nconst TaskState = {\n    /** The task is currently transferring data. */\n    RUNNING: 'running',\n    /** The task was paused by the user. */\n    PAUSED: 'paused',\n    /** The task completed successfully. */\n    SUCCESS: 'success',\n    /** The task was canceled. */\n    CANCELED: 'canceled',\n    /** The task failed with an error. */\n    ERROR: 'error'\n};\nfunction taskStateFromInternalTaskState(state) {\n    switch (state) {\n        case \"running\" /* InternalTaskState.RUNNING */:\n        case \"pausing\" /* InternalTaskState.PAUSING */:\n        case \"canceling\" /* InternalTaskState.CANCELING */:\n            return TaskState.RUNNING;\n        case \"paused\" /* InternalTaskState.PAUSED */:\n            return TaskState.PAUSED;\n        case \"success\" /* InternalTaskState.SUCCESS */:\n            return TaskState.SUCCESS;\n        case \"canceled\" /* InternalTaskState.CANCELED */:\n            return TaskState.CANCELED;\n        case \"error\" /* InternalTaskState.ERROR */:\n            return TaskState.ERROR;\n        default:\n            // TODO(andysoto): assert(false);\n            return TaskState.ERROR;\n    }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nclass Observer {\n    constructor(nextOrObserver, error, complete) {\n        const asFunctions = isFunction(nextOrObserver) || error != null || complete != null;\n        if (asFunctions) {\n            this.next = nextOrObserver;\n            this.error = error !== null && error !== void 0 ? error : undefined;\n            this.complete = complete !== null && complete !== void 0 ? complete : undefined;\n        }\n        else {\n            const observer = nextOrObserver;\n            this.next = observer.next;\n            this.error = observer.error;\n            this.complete = observer.complete;\n        }\n    }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Returns a function that invokes f with its arguments asynchronously as a\n * microtask, i.e. as soon as possible after the current script returns back\n * into browser code.\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction async(f) {\n    return (...argsToForward) => {\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n        Promise.resolve().then(() => f(...argsToForward));\n    };\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/** An override for the text-based Connection. Used in tests. */\nlet textFactoryOverride = null;\n/**\n * Network layer that works in Node.\n *\n * This network implementation should not be used in browsers as it does not\n * support progress updates.\n */\nclass FetchConnection {\n    constructor() {\n        this.errorText_ = '';\n        this.sent_ = false;\n        this.errorCode_ = ErrorCode.NO_ERROR;\n    }\n    async send(url, method, isUsingEmulator, body, headers) {\n        if (this.sent_) {\n            throw internalError('cannot .send() more than once');\n        }\n        this.sent_ = true;\n        try {\n            const response = await newFetch(url, method, isUsingEmulator, headers, body);\n            this.headers_ = response.headers;\n            this.statusCode_ = response.status;\n            this.errorCode_ = ErrorCode.NO_ERROR;\n            this.body_ = await response.arrayBuffer();\n        }\n        catch (e) {\n            this.errorText_ = e === null || e === void 0 ? void 0 : e.message;\n            // emulate XHR which sets status to 0 when encountering a network error\n            this.statusCode_ = 0;\n            this.errorCode_ = ErrorCode.NETWORK_ERROR;\n        }\n    }\n    getErrorCode() {\n        if (this.errorCode_ === undefined) {\n            throw internalError('cannot .getErrorCode() before receiving response');\n        }\n        return this.errorCode_;\n    }\n    getStatus() {\n        if (this.statusCode_ === undefined) {\n            throw internalError('cannot .getStatus() before receiving response');\n        }\n        return this.statusCode_;\n    }\n    getErrorText() {\n        return this.errorText_;\n    }\n    abort() {\n        // Not supported\n    }\n    getResponseHeader(header) {\n        if (!this.headers_) {\n            throw internalError('cannot .getResponseHeader() before receiving response');\n        }\n        return this.headers_.get(header);\n    }\n    addUploadProgressListener(listener) {\n        // Not supported\n    }\n    removeUploadProgressListener(listener) {\n        // Not supported\n    }\n}\nclass FetchTextConnection extends FetchConnection {\n    getResponse() {\n        if (!this.body_) {\n            throw internalError('cannot .getResponse() before receiving response');\n        }\n        return Buffer.from(this.body_).toString('utf-8');\n    }\n}\nfunction newTextConnection() {\n    return textFactoryOverride\n        ? textFactoryOverride()\n        : new FetchTextConnection();\n}\nclass FetchBytesConnection extends FetchConnection {\n    getResponse() {\n        if (!this.body_) {\n            throw internalError('cannot .getResponse() before sending');\n        }\n        return this.body_;\n    }\n}\nfunction newBytesConnection() {\n    return new FetchBytesConnection();\n}\nclass FetchStreamConnection extends FetchConnection {\n    constructor() {\n        super(...arguments);\n        this.stream_ = null;\n    }\n    async send(url, method, isUsingEmulator, body, headers) {\n        if (this.sent_) {\n            throw internalError('cannot .send() more than once');\n        }\n        this.sent_ = true;\n        try {\n            const response = await newFetch(url, method, isUsingEmulator, headers, body);\n            this.headers_ = response.headers;\n            this.statusCode_ = response.status;\n            this.errorCode_ = ErrorCode.NO_ERROR;\n            this.stream_ = response.body;\n        }\n        catch (e) {\n            this.errorText_ = e === null || e === void 0 ? void 0 : e.message;\n            // emulate XHR which sets status to 0 when encountering a network error\n            this.statusCode_ = 0;\n            this.errorCode_ = ErrorCode.NETWORK_ERROR;\n        }\n    }\n    getResponse() {\n        if (!this.stream_) {\n            throw internalError('cannot .getResponse() before sending');\n        }\n        return this.stream_;\n    }\n}\nfunction newFetch(url, method, isUsingEmulator, headers, body) {\n    const fetchArgs = {\n        method,\n        headers: headers || {},\n        body: body\n    };\n    if ((0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.isCloudWorkstation)(url) && isUsingEmulator) {\n        fetchArgs.credentials = 'include';\n    }\n    return fetch(url, fetchArgs);\n}\nfunction newStreamConnection() {\n    return new FetchStreamConnection();\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Represents a blob being uploaded. Can be used to pause/resume/cancel the\n * upload and manage callbacks for various events.\n * @internal\n */\nclass UploadTask {\n    isExponentialBackoffExpired() {\n        return this.sleepTime > this.maxSleepTime;\n    }\n    /**\n     * @param ref - The firebaseStorage.Reference object this task came\n     *     from, untyped to avoid cyclic dependencies.\n     * @param blob - The blob to upload.\n     */\n    constructor(ref, blob, metadata = null) {\n        /**\n         * Number of bytes transferred so far.\n         */\n        this._transferred = 0;\n        this._needToFetchStatus = false;\n        this._needToFetchMetadata = false;\n        this._observers = [];\n        this._error = undefined;\n        this._uploadUrl = undefined;\n        this._request = undefined;\n        this._chunkMultiplier = 1;\n        this._resolve = undefined;\n        this._reject = undefined;\n        this._ref = ref;\n        this._blob = blob;\n        this._metadata = metadata;\n        this._mappings = getMappings();\n        this._resumable = this._shouldDoResumable(this._blob);\n        this._state = \"running\" /* InternalTaskState.RUNNING */;\n        this._errorHandler = error => {\n            this._request = undefined;\n            this._chunkMultiplier = 1;\n            if (error._codeEquals(StorageErrorCode.CANCELED)) {\n                this._needToFetchStatus = true;\n                this.completeTransitions_();\n            }\n            else {\n                const backoffExpired = this.isExponentialBackoffExpired();\n                if (isRetryStatusCode(error.status, [])) {\n                    if (backoffExpired) {\n                        error = retryLimitExceeded();\n                    }\n                    else {\n                        this.sleepTime = Math.max(this.sleepTime * 2, DEFAULT_MIN_SLEEP_TIME_MILLIS);\n                        this._needToFetchStatus = true;\n                        this.completeTransitions_();\n                        return;\n                    }\n                }\n                this._error = error;\n                this._transition(\"error\" /* InternalTaskState.ERROR */);\n            }\n        };\n        this._metadataErrorHandler = error => {\n            this._request = undefined;\n            if (error._codeEquals(StorageErrorCode.CANCELED)) {\n                this.completeTransitions_();\n            }\n            else {\n                this._error = error;\n                this._transition(\"error\" /* InternalTaskState.ERROR */);\n            }\n        };\n        this.sleepTime = 0;\n        this.maxSleepTime = this._ref.storage.maxUploadRetryTime;\n        this._promise = new Promise((resolve, reject) => {\n            this._resolve = resolve;\n            this._reject = reject;\n            this._start();\n        });\n        // Prevent uncaught rejections on the internal promise from bubbling out\n        // to the top level with a dummy handler.\n        this._promise.then(null, () => { });\n    }\n    _makeProgressCallback() {\n        const sizeBefore = this._transferred;\n        return loaded => this._updateProgress(sizeBefore + loaded);\n    }\n    _shouldDoResumable(blob) {\n        return blob.size() > 256 * 1024;\n    }\n    _start() {\n        if (this._state !== \"running\" /* InternalTaskState.RUNNING */) {\n            // This can happen if someone pauses us in a resume callback, for example.\n            return;\n        }\n        if (this._request !== undefined) {\n            return;\n        }\n        if (this._resumable) {\n            if (this._uploadUrl === undefined) {\n                this._createResumable();\n            }\n            else {\n                if (this._needToFetchStatus) {\n                    this._fetchStatus();\n                }\n                else {\n                    if (this._needToFetchMetadata) {\n                        // Happens if we miss the metadata on upload completion.\n                        this._fetchMetadata();\n                    }\n                    else {\n                        this.pendingTimeout = setTimeout(() => {\n                            this.pendingTimeout = undefined;\n                            this._continueUpload();\n                        }, this.sleepTime);\n                    }\n                }\n            }\n        }\n        else {\n            this._oneShotUpload();\n        }\n    }\n    _resolveToken(callback) {\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n        Promise.all([\n            this._ref.storage._getAuthToken(),\n            this._ref.storage._getAppCheckToken()\n        ]).then(([authToken, appCheckToken]) => {\n            switch (this._state) {\n                case \"running\" /* InternalTaskState.RUNNING */:\n                    callback(authToken, appCheckToken);\n                    break;\n                case \"canceling\" /* InternalTaskState.CANCELING */:\n                    this._transition(\"canceled\" /* InternalTaskState.CANCELED */);\n                    break;\n                case \"pausing\" /* InternalTaskState.PAUSING */:\n                    this._transition(\"paused\" /* InternalTaskState.PAUSED */);\n                    break;\n            }\n        });\n    }\n    // TODO(andysoto): assert false\n    _createResumable() {\n        this._resolveToken((authToken, appCheckToken) => {\n            const requestInfo = createResumableUpload(this._ref.storage, this._ref._location, this._mappings, this._blob, this._metadata);\n            const createRequest = this._ref.storage._makeRequest(requestInfo, newTextConnection, authToken, appCheckToken);\n            this._request = createRequest;\n            createRequest.getPromise().then((url) => {\n                this._request = undefined;\n                this._uploadUrl = url;\n                this._needToFetchStatus = false;\n                this.completeTransitions_();\n            }, this._errorHandler);\n        });\n    }\n    _fetchStatus() {\n        // TODO(andysoto): assert(this.uploadUrl_ !== null);\n        const url = this._uploadUrl;\n        this._resolveToken((authToken, appCheckToken) => {\n            const requestInfo = getResumableUploadStatus(this._ref.storage, this._ref._location, url, this._blob);\n            const statusRequest = this._ref.storage._makeRequest(requestInfo, newTextConnection, authToken, appCheckToken);\n            this._request = statusRequest;\n            statusRequest.getPromise().then(status => {\n                status = status;\n                this._request = undefined;\n                this._updateProgress(status.current);\n                this._needToFetchStatus = false;\n                if (status.finalized) {\n                    this._needToFetchMetadata = true;\n                }\n                this.completeTransitions_();\n            }, this._errorHandler);\n        });\n    }\n    _continueUpload() {\n        const chunkSize = RESUMABLE_UPLOAD_CHUNK_SIZE * this._chunkMultiplier;\n        const status = new ResumableUploadStatus(this._transferred, this._blob.size());\n        // TODO(andysoto): assert(this.uploadUrl_ !== null);\n        const url = this._uploadUrl;\n        this._resolveToken((authToken, appCheckToken) => {\n            let requestInfo;\n            try {\n                requestInfo = continueResumableUpload(this._ref._location, this._ref.storage, url, this._blob, chunkSize, this._mappings, status, this._makeProgressCallback());\n            }\n            catch (e) {\n                this._error = e;\n                this._transition(\"error\" /* InternalTaskState.ERROR */);\n                return;\n            }\n            const uploadRequest = this._ref.storage._makeRequest(requestInfo, newTextConnection, authToken, appCheckToken, \n            /*retry=*/ false // Upload requests should not be retried as each retry should be preceded by another query request. Which is handled in this file.\n            );\n            this._request = uploadRequest;\n            uploadRequest.getPromise().then((newStatus) => {\n                this._increaseMultiplier();\n                this._request = undefined;\n                this._updateProgress(newStatus.current);\n                if (newStatus.finalized) {\n                    this._metadata = newStatus.metadata;\n                    this._transition(\"success\" /* InternalTaskState.SUCCESS */);\n                }\n                else {\n                    this.completeTransitions_();\n                }\n            }, this._errorHandler);\n        });\n    }\n    _increaseMultiplier() {\n        const currentSize = RESUMABLE_UPLOAD_CHUNK_SIZE * this._chunkMultiplier;\n        // Max chunk size is 32M.\n        if (currentSize * 2 < 32 * 1024 * 1024) {\n            this._chunkMultiplier *= 2;\n        }\n    }\n    _fetchMetadata() {\n        this._resolveToken((authToken, appCheckToken) => {\n            const requestInfo = getMetadata$2(this._ref.storage, this._ref._location, this._mappings);\n            const metadataRequest = this._ref.storage._makeRequest(requestInfo, newTextConnection, authToken, appCheckToken);\n            this._request = metadataRequest;\n            metadataRequest.getPromise().then(metadata => {\n                this._request = undefined;\n                this._metadata = metadata;\n                this._transition(\"success\" /* InternalTaskState.SUCCESS */);\n            }, this._metadataErrorHandler);\n        });\n    }\n    _oneShotUpload() {\n        this._resolveToken((authToken, appCheckToken) => {\n            const requestInfo = multipartUpload(this._ref.storage, this._ref._location, this._mappings, this._blob, this._metadata);\n            const multipartRequest = this._ref.storage._makeRequest(requestInfo, newTextConnection, authToken, appCheckToken);\n            this._request = multipartRequest;\n            multipartRequest.getPromise().then(metadata => {\n                this._request = undefined;\n                this._metadata = metadata;\n                this._updateProgress(this._blob.size());\n                this._transition(\"success\" /* InternalTaskState.SUCCESS */);\n            }, this._errorHandler);\n        });\n    }\n    _updateProgress(transferred) {\n        const old = this._transferred;\n        this._transferred = transferred;\n        // A progress update can make the \"transferred\" value smaller (e.g. a\n        // partial upload not completed by server, after which the \"transferred\"\n        // value may reset to the value at the beginning of the request).\n        if (this._transferred !== old) {\n            this._notifyObservers();\n        }\n    }\n    _transition(state) {\n        if (this._state === state) {\n            return;\n        }\n        switch (state) {\n            case \"canceling\" /* InternalTaskState.CANCELING */:\n            case \"pausing\" /* InternalTaskState.PAUSING */:\n                // TODO(andysoto):\n                // assert(this.state_ === InternalTaskState.RUNNING ||\n                //        this.state_ === InternalTaskState.PAUSING);\n                this._state = state;\n                if (this._request !== undefined) {\n                    this._request.cancel();\n                }\n                else if (this.pendingTimeout) {\n                    clearTimeout(this.pendingTimeout);\n                    this.pendingTimeout = undefined;\n                    this.completeTransitions_();\n                }\n                break;\n            case \"running\" /* InternalTaskState.RUNNING */:\n                // TODO(andysoto):\n                // assert(this.state_ === InternalTaskState.PAUSED ||\n                //        this.state_ === InternalTaskState.PAUSING);\n                const wasPaused = this._state === \"paused\" /* InternalTaskState.PAUSED */;\n                this._state = state;\n                if (wasPaused) {\n                    this._notifyObservers();\n                    this._start();\n                }\n                break;\n            case \"paused\" /* InternalTaskState.PAUSED */:\n                // TODO(andysoto):\n                // assert(this.state_ === InternalTaskState.PAUSING);\n                this._state = state;\n                this._notifyObservers();\n                break;\n            case \"canceled\" /* InternalTaskState.CANCELED */:\n                // TODO(andysoto):\n                // assert(this.state_ === InternalTaskState.PAUSED ||\n                //        this.state_ === InternalTaskState.CANCELING);\n                this._error = canceled();\n                this._state = state;\n                this._notifyObservers();\n                break;\n            case \"error\" /* InternalTaskState.ERROR */:\n                // TODO(andysoto):\n                // assert(this.state_ === InternalTaskState.RUNNING ||\n                //        this.state_ === InternalTaskState.PAUSING ||\n                //        this.state_ === InternalTaskState.CANCELING);\n                this._state = state;\n                this._notifyObservers();\n                break;\n            case \"success\" /* InternalTaskState.SUCCESS */:\n                // TODO(andysoto):\n                // assert(this.state_ === InternalTaskState.RUNNING ||\n                //        this.state_ === InternalTaskState.PAUSING ||\n                //        this.state_ === InternalTaskState.CANCELING);\n                this._state = state;\n                this._notifyObservers();\n                break;\n        }\n    }\n    completeTransitions_() {\n        switch (this._state) {\n            case \"pausing\" /* InternalTaskState.PAUSING */:\n                this._transition(\"paused\" /* InternalTaskState.PAUSED */);\n                break;\n            case \"canceling\" /* InternalTaskState.CANCELING */:\n                this._transition(\"canceled\" /* InternalTaskState.CANCELED */);\n                break;\n            case \"running\" /* InternalTaskState.RUNNING */:\n                this._start();\n                break;\n        }\n    }\n    /**\n     * A snapshot of the current task state.\n     */\n    get snapshot() {\n        const externalState = taskStateFromInternalTaskState(this._state);\n        return {\n            bytesTransferred: this._transferred,\n            totalBytes: this._blob.size(),\n            state: externalState,\n            metadata: this._metadata,\n            task: this,\n            ref: this._ref\n        };\n    }\n    /**\n     * Adds a callback for an event.\n     * @param type - The type of event to listen for.\n     * @param nextOrObserver -\n     *     The `next` function, which gets called for each item in\n     *     the event stream, or an observer object with some or all of these three\n     *     properties (`next`, `error`, `complete`).\n     * @param error - A function that gets called with a `StorageError`\n     *     if the event stream ends due to an error.\n     * @param completed - A function that gets called if the\n     *     event stream ends normally.\n     * @returns\n     *     If only the event argument is passed, returns a function you can use to\n     *     add callbacks (see the examples above). If more than just the event\n     *     argument is passed, returns a function you can call to unregister the\n     *     callbacks.\n     */\n    on(type, nextOrObserver, error, completed) {\n        // Note: `type` isn't being used. Its type is also incorrect. TaskEvent should not be a string.\n        const observer = new Observer(nextOrObserver || undefined, error || undefined, completed || undefined);\n        this._addObserver(observer);\n        return () => {\n            this._removeObserver(observer);\n        };\n    }\n    /**\n     * This object behaves like a Promise, and resolves with its snapshot data\n     * when the upload completes.\n     * @param onFulfilled - The fulfillment callback. Promise chaining works as normal.\n     * @param onRejected - The rejection callback.\n     */\n    then(onFulfilled, onRejected) {\n        // These casts are needed so that TypeScript can infer the types of the\n        // resulting Promise.\n        return this._promise.then(onFulfilled, onRejected);\n    }\n    /**\n     * Equivalent to calling `then(null, onRejected)`.\n     */\n    catch(onRejected) {\n        return this.then(null, onRejected);\n    }\n    /**\n     * Adds the given observer.\n     */\n    _addObserver(observer) {\n        this._observers.push(observer);\n        this._notifyObserver(observer);\n    }\n    /**\n     * Removes the given observer.\n     */\n    _removeObserver(observer) {\n        const i = this._observers.indexOf(observer);\n        if (i !== -1) {\n            this._observers.splice(i, 1);\n        }\n    }\n    _notifyObservers() {\n        this._finishPromise();\n        const observers = this._observers.slice();\n        observers.forEach(observer => {\n            this._notifyObserver(observer);\n        });\n    }\n    _finishPromise() {\n        if (this._resolve !== undefined) {\n            let triggered = true;\n            switch (taskStateFromInternalTaskState(this._state)) {\n                case TaskState.SUCCESS:\n                    async(this._resolve.bind(null, this.snapshot))();\n                    break;\n                case TaskState.CANCELED:\n                case TaskState.ERROR:\n                    const toCall = this._reject;\n                    async(toCall.bind(null, this._error))();\n                    break;\n                default:\n                    triggered = false;\n                    break;\n            }\n            if (triggered) {\n                this._resolve = undefined;\n                this._reject = undefined;\n            }\n        }\n    }\n    _notifyObserver(observer) {\n        const externalState = taskStateFromInternalTaskState(this._state);\n        switch (externalState) {\n            case TaskState.RUNNING:\n            case TaskState.PAUSED:\n                if (observer.next) {\n                    async(observer.next.bind(observer, this.snapshot))();\n                }\n                break;\n            case TaskState.SUCCESS:\n                if (observer.complete) {\n                    async(observer.complete.bind(observer))();\n                }\n                break;\n            case TaskState.CANCELED:\n            case TaskState.ERROR:\n                if (observer.error) {\n                    async(observer.error.bind(observer, this._error))();\n                }\n                break;\n            default:\n                // TODO(andysoto): assert(false);\n                if (observer.error) {\n                    async(observer.error.bind(observer, this._error))();\n                }\n        }\n    }\n    /**\n     * Resumes a paused task. Has no effect on a currently running or failed task.\n     * @returns True if the operation took effect, false if ignored.\n     */\n    resume() {\n        const valid = this._state === \"paused\" /* InternalTaskState.PAUSED */ ||\n            this._state === \"pausing\" /* InternalTaskState.PAUSING */;\n        if (valid) {\n            this._transition(\"running\" /* InternalTaskState.RUNNING */);\n        }\n        return valid;\n    }\n    /**\n     * Pauses a currently running task. Has no effect on a paused or failed task.\n     * @returns True if the operation took effect, false if ignored.\n     */\n    pause() {\n        const valid = this._state === \"running\" /* InternalTaskState.RUNNING */;\n        if (valid) {\n            this._transition(\"pausing\" /* InternalTaskState.PAUSING */);\n        }\n        return valid;\n    }\n    /**\n     * Cancels a currently running or paused task. Has no effect on a complete or\n     * failed task.\n     * @returns True if the operation took effect, false if ignored.\n     */\n    cancel() {\n        const valid = this._state === \"running\" /* InternalTaskState.RUNNING */ ||\n            this._state === \"pausing\" /* InternalTaskState.PAUSING */;\n        if (valid) {\n            this._transition(\"canceling\" /* InternalTaskState.CANCELING */);\n        }\n        return valid;\n    }\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Provides methods to interact with a bucket in the Firebase Storage service.\n * @internal\n * @param _location - An fbs.location, or the URL at\n *     which to base this object, in one of the following forms:\n *         gs://<bucket>/<object-path>\n *         http[s]://firebasestorage.googleapis.com/\n *                     <api-version>/b/<bucket>/o/<object-path>\n *     Any query or fragment strings will be ignored in the http[s]\n *     format. If no value is passed, the storage object will use a URL based on\n *     the project ID of the base firebase.App instance.\n */\nclass Reference {\n    constructor(_service, location) {\n        this._service = _service;\n        if (location instanceof Location) {\n            this._location = location;\n        }\n        else {\n            this._location = Location.makeFromUrl(location, _service.host);\n        }\n    }\n    /**\n     * Returns the URL for the bucket and path this object references,\n     *     in the form gs://<bucket>/<object-path>\n     * @override\n     */\n    toString() {\n        return 'gs://' + this._location.bucket + '/' + this._location.path;\n    }\n    _newRef(service, location) {\n        return new Reference(service, location);\n    }\n    /**\n     * A reference to the root of this object's bucket.\n     */\n    get root() {\n        const location = new Location(this._location.bucket, '');\n        return this._newRef(this._service, location);\n    }\n    /**\n     * The name of the bucket containing this reference's object.\n     */\n    get bucket() {\n        return this._location.bucket;\n    }\n    /**\n     * The full path of this object.\n     */\n    get fullPath() {\n        return this._location.path;\n    }\n    /**\n     * The short name of this object, which is the last component of the full path.\n     * For example, if fullPath is 'full/path/image.png', name is 'image.png'.\n     */\n    get name() {\n        return lastComponent(this._location.path);\n    }\n    /**\n     * The `StorageService` instance this `StorageReference` is associated with.\n     */\n    get storage() {\n        return this._service;\n    }\n    /**\n     * A `StorageReference` pointing to the parent location of this `StorageReference`, or null if\n     * this reference is the root.\n     */\n    get parent() {\n        const newPath = parent(this._location.path);\n        if (newPath === null) {\n            return null;\n        }\n        const location = new Location(this._location.bucket, newPath);\n        return new Reference(this._service, location);\n    }\n    /**\n     * Utility function to throw an error in methods that do not accept a root reference.\n     */\n    _throwIfRoot(name) {\n        if (this._location.path === '') {\n            throw invalidRootOperation(name);\n        }\n    }\n}\n/**\n * Download the bytes at the object's location.\n * @returns A Promise containing the downloaded bytes.\n */\nfunction getBytesInternal(ref, maxDownloadSizeBytes) {\n    ref._throwIfRoot('getBytes');\n    const requestInfo = getBytes$1(ref.storage, ref._location, maxDownloadSizeBytes);\n    return ref.storage\n        .makeRequestWithTokens(requestInfo, newBytesConnection)\n        .then(bytes => maxDownloadSizeBytes !== undefined\n        ? // GCS may not honor the Range header for small files\n            bytes.slice(0, maxDownloadSizeBytes)\n        : bytes);\n}\n/** Stream the bytes at the object's location. */\nfunction getStreamInternal(ref, maxDownloadSizeBytes) {\n    ref._throwIfRoot('getStream');\n    const requestInfo = getBytes$1(ref.storage, ref._location, maxDownloadSizeBytes);\n    // Transforms the stream so that only `maxDownloadSizeBytes` bytes are piped to the result\n    const newMaxSizeTransform = (n) => {\n        let missingBytes = n;\n        return {\n            transform(chunk, controller) {\n                // GCS may not honor the Range header for small files\n                if (chunk.length < missingBytes) {\n                    controller.enqueue(chunk);\n                    missingBytes -= chunk.length;\n                }\n                else {\n                    controller.enqueue(chunk.slice(0, missingBytes));\n                    controller.terminate();\n                }\n            }\n        };\n    };\n    const result = maxDownloadSizeBytes !== undefined\n        ? new TransformStream(newMaxSizeTransform(maxDownloadSizeBytes))\n        : new TransformStream(); // The default transformer forwards all chunks to its readable side\n    ref.storage\n        .makeRequestWithTokens(requestInfo, newStreamConnection)\n        .then(readableStream => readableStream.pipeThrough(result))\n        .catch(err => result.writable.abort(err));\n    return result.readable;\n}\n/**\n * Uploads data to this object's location.\n * The upload is not resumable.\n *\n * @param ref - StorageReference where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the newly uploaded data.\n * @returns A Promise containing an UploadResult\n */\nfunction uploadBytes$1(ref, data, metadata) {\n    ref._throwIfRoot('uploadBytes');\n    const requestInfo = multipartUpload(ref.storage, ref._location, getMappings(), new FbsBlob(data, true), metadata);\n    return ref.storage\n        .makeRequestWithTokens(requestInfo, newTextConnection)\n        .then(finalMetadata => {\n        return {\n            metadata: finalMetadata,\n            ref\n        };\n    });\n}\n/**\n * Uploads data to this object's location.\n * The upload can be paused and resumed, and exposes progress updates.\n * @public\n * @param ref - StorageReference where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the newly uploaded data.\n * @returns An UploadTask\n */\nfunction uploadBytesResumable$1(ref, data, metadata) {\n    ref._throwIfRoot('uploadBytesResumable');\n    return new UploadTask(ref, new FbsBlob(data), metadata);\n}\n/**\n * Uploads a string to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - StorageReference where string should be uploaded.\n * @param value - The string to upload.\n * @param format - The format of the string to upload.\n * @param metadata - Metadata for the newly uploaded string.\n * @returns A Promise containing an UploadResult\n */\nfunction uploadString$1(ref, value, format = StringFormat.RAW, metadata) {\n    ref._throwIfRoot('uploadString');\n    const data = dataFromString(format, value);\n    const metadataClone = Object.assign({}, metadata);\n    if (metadataClone['contentType'] == null && data.contentType != null) {\n        metadataClone['contentType'] = data.contentType;\n    }\n    return uploadBytes$1(ref, data.data, metadataClone);\n}\n/**\n * List all items (files) and prefixes (folders) under this storage reference.\n *\n * This is a helper method for calling list() repeatedly until there are\n * no more results. The default pagination size is 1000.\n *\n * Note: The results may not be consistent if objects are changed while this\n * operation is running.\n *\n * Warning: listAll may potentially consume too many resources if there are\n * too many results.\n * @public\n * @param ref - StorageReference to get list from.\n *\n * @returns A Promise that resolves with all the items and prefixes under\n *      the current storage reference. `prefixes` contains references to\n *      sub-directories and `items` contains references to objects in this\n *      folder. `nextPageToken` is never returned.\n */\nfunction listAll$1(ref) {\n    const accumulator = {\n        prefixes: [],\n        items: []\n    };\n    return listAllHelper(ref, accumulator).then(() => accumulator);\n}\n/**\n * Separated from listAll because async functions can't use \"arguments\".\n * @param ref\n * @param accumulator\n * @param pageToken\n */\nasync function listAllHelper(ref, accumulator, pageToken) {\n    const opt = {\n        // maxResults is 1000 by default.\n        pageToken\n    };\n    const nextPage = await list$1(ref, opt);\n    accumulator.prefixes.push(...nextPage.prefixes);\n    accumulator.items.push(...nextPage.items);\n    if (nextPage.nextPageToken != null) {\n        await listAllHelper(ref, accumulator, nextPage.nextPageToken);\n    }\n}\n/**\n * List items (files) and prefixes (folders) under this storage reference.\n *\n * List API is only available for Firebase Rules Version 2.\n *\n * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\n * delimited folder structure.\n * Refer to GCS's List API if you want to learn more.\n *\n * To adhere to Firebase Rules's Semantics, Firebase Storage does not\n * support objects whose paths end with \"/\" or contain two consecutive\n * \"/\"s. Firebase Storage List API will filter these unsupported objects.\n * list() may fail if there are too many unsupported objects in the bucket.\n * @public\n *\n * @param ref - StorageReference to get list from.\n * @param options - See ListOptions for details.\n * @returns A Promise that resolves with the items and prefixes.\n *      `prefixes` contains references to sub-folders and `items`\n *      contains references to objects in this folder. `nextPageToken`\n *      can be used to get the rest of the results.\n */\nfunction list$1(ref, options) {\n    if (options != null) {\n        if (typeof options.maxResults === 'number') {\n            validateNumber('options.maxResults', \n            /* minValue= */ 1, \n            /* maxValue= */ 1000, options.maxResults);\n        }\n    }\n    const op = options || {};\n    const requestInfo = list$2(ref.storage, ref._location, \n    /*delimiter= */ '/', op.pageToken, op.maxResults);\n    return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n/**\n * A `Promise` that resolves with the metadata for this object. If this\n * object doesn't exist or metadata cannot be retrieved, the promise is\n * rejected.\n * @public\n * @param ref - StorageReference to get metadata from.\n */\nfunction getMetadata$1(ref) {\n    ref._throwIfRoot('getMetadata');\n    const requestInfo = getMetadata$2(ref.storage, ref._location, getMappings());\n    return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n/**\n * Updates the metadata for this object.\n * @public\n * @param ref - StorageReference to update metadata for.\n * @param metadata - The new metadata for the object.\n *     Only values that have been explicitly set will be changed. Explicitly\n *     setting a value to null will remove the metadata.\n * @returns A `Promise` that resolves\n *     with the new metadata for this object.\n *     See `firebaseStorage.Reference.prototype.getMetadata`\n */\nfunction updateMetadata$1(ref, metadata) {\n    ref._throwIfRoot('updateMetadata');\n    const requestInfo = updateMetadata$2(ref.storage, ref._location, metadata, getMappings());\n    return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n/**\n * Returns the download URL for the given Reference.\n * @public\n * @returns A `Promise` that resolves with the download\n *     URL for this object.\n */\nfunction getDownloadURL$1(ref) {\n    ref._throwIfRoot('getDownloadURL');\n    const requestInfo = getDownloadUrl(ref.storage, ref._location, getMappings());\n    return ref.storage\n        .makeRequestWithTokens(requestInfo, newTextConnection)\n        .then(url => {\n        if (url === null) {\n            throw noDownloadURL();\n        }\n        return url;\n    });\n}\n/**\n * Deletes the object at this location.\n * @public\n * @param ref - StorageReference for object to delete.\n * @returns A `Promise` that resolves if the deletion succeeds.\n */\nfunction deleteObject$1(ref) {\n    ref._throwIfRoot('deleteObject');\n    const requestInfo = deleteObject$2(ref.storage, ref._location);\n    return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n/**\n * Returns reference for object obtained by appending `childPath` to `ref`.\n *\n * @param ref - StorageReference to get child of.\n * @param childPath - Child path from provided ref.\n * @returns A reference to the object obtained by\n * appending childPath, removing any duplicate, beginning, or trailing\n * slashes.\n *\n */\nfunction _getChild$1(ref, childPath) {\n    const newPath = child(ref._location.path, childPath);\n    const location = new Location(ref._location.bucket, newPath);\n    return new Reference(ref.storage, location);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction isUrl(path) {\n    return /^[A-Za-z]+:\\/\\//.test(path);\n}\n/**\n * Returns a firebaseStorage.Reference for the given url.\n */\nfunction refFromURL(service, url) {\n    return new Reference(service, url);\n}\n/**\n * Returns a firebaseStorage.Reference for the given path in the default\n * bucket.\n */\nfunction refFromPath(ref, path) {\n    if (ref instanceof FirebaseStorageImpl) {\n        const service = ref;\n        if (service._bucket == null) {\n            throw noDefaultBucket();\n        }\n        const reference = new Reference(service, service._bucket);\n        if (path != null) {\n            return refFromPath(reference, path);\n        }\n        else {\n            return reference;\n        }\n    }\n    else {\n        // ref is a Reference\n        if (path !== undefined) {\n            return _getChild$1(ref, path);\n        }\n        else {\n            return ref;\n        }\n    }\n}\nfunction ref$1(serviceOrRef, pathOrUrl) {\n    if (pathOrUrl && isUrl(pathOrUrl)) {\n        if (serviceOrRef instanceof FirebaseStorageImpl) {\n            return refFromURL(serviceOrRef, pathOrUrl);\n        }\n        else {\n            throw invalidArgument('To use ref(service, url), the first argument must be a Storage instance.');\n        }\n    }\n    else {\n        return refFromPath(serviceOrRef, pathOrUrl);\n    }\n}\nfunction extractBucket(host, config) {\n    const bucketString = config === null || config === void 0 ? void 0 : config[CONFIG_STORAGE_BUCKET_KEY];\n    if (bucketString == null) {\n        return null;\n    }\n    return Location.makeFromBucketSpec(bucketString, host);\n}\nfunction connectStorageEmulator$1(storage, host, port, options = {}) {\n    storage.host = `${host}:${port}`;\n    const useSsl = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.isCloudWorkstation)(host);\n    // Workaround to get cookies in Firebase Studio\n    if (useSsl) {\n        void (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.pingServer)(`https://${storage.host}/b`);\n        (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.updateEmulatorBanner)('Storage', true);\n    }\n    storage._isUsingEmulator = true;\n    storage._protocol = useSsl ? 'https' : 'http';\n    const { mockUserToken } = options;\n    if (mockUserToken) {\n        storage._overrideAuthToken =\n            typeof mockUserToken === 'string'\n                ? mockUserToken\n                : (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.createMockUserToken)(mockUserToken, storage.app.options.projectId);\n    }\n}\n/**\n * A service that provides Firebase Storage Reference instances.\n * @param opt_url - gs:// url to a custom Storage Bucket\n *\n * @internal\n */\nclass FirebaseStorageImpl {\n    constructor(\n    /**\n     * FirebaseApp associated with this StorageService instance.\n     */\n    app, _authProvider, \n    /**\n     * @internal\n     */\n    _appCheckProvider, \n    /**\n     * @internal\n     */\n    _url, _firebaseVersion, _isUsingEmulator = false) {\n        this.app = app;\n        this._authProvider = _authProvider;\n        this._appCheckProvider = _appCheckProvider;\n        this._url = _url;\n        this._firebaseVersion = _firebaseVersion;\n        this._isUsingEmulator = _isUsingEmulator;\n        this._bucket = null;\n        /**\n         * This string can be in the formats:\n         * - host\n         * - host:port\n         */\n        this._host = DEFAULT_HOST;\n        this._protocol = 'https';\n        this._appId = null;\n        this._deleted = false;\n        this._maxOperationRetryTime = DEFAULT_MAX_OPERATION_RETRY_TIME;\n        this._maxUploadRetryTime = DEFAULT_MAX_UPLOAD_RETRY_TIME;\n        this._requests = new Set();\n        if (_url != null) {\n            this._bucket = Location.makeFromBucketSpec(_url, this._host);\n        }\n        else {\n            this._bucket = extractBucket(this._host, this.app.options);\n        }\n    }\n    /**\n     * The host string for this service, in the form of `host` or\n     * `host:port`.\n     */\n    get host() {\n        return this._host;\n    }\n    set host(host) {\n        this._host = host;\n        if (this._url != null) {\n            this._bucket = Location.makeFromBucketSpec(this._url, host);\n        }\n        else {\n            this._bucket = extractBucket(host, this.app.options);\n        }\n    }\n    /**\n     * The maximum time to retry uploads in milliseconds.\n     */\n    get maxUploadRetryTime() {\n        return this._maxUploadRetryTime;\n    }\n    set maxUploadRetryTime(time) {\n        validateNumber('time', \n        /* minValue=*/ 0, \n        /* maxValue= */ Number.POSITIVE_INFINITY, time);\n        this._maxUploadRetryTime = time;\n    }\n    /**\n     * The maximum time to retry operations other than uploads or downloads in\n     * milliseconds.\n     */\n    get maxOperationRetryTime() {\n        return this._maxOperationRetryTime;\n    }\n    set maxOperationRetryTime(time) {\n        validateNumber('time', \n        /* minValue=*/ 0, \n        /* maxValue= */ Number.POSITIVE_INFINITY, time);\n        this._maxOperationRetryTime = time;\n    }\n    async _getAuthToken() {\n        if (this._overrideAuthToken) {\n            return this._overrideAuthToken;\n        }\n        const auth = this._authProvider.getImmediate({ optional: true });\n        if (auth) {\n            const tokenData = await auth.getToken();\n            if (tokenData !== null) {\n                return tokenData.accessToken;\n            }\n        }\n        return null;\n    }\n    async _getAppCheckToken() {\n        if ((0,_firebase_app__WEBPACK_IMPORTED_MODULE_0__._isFirebaseServerApp)(this.app) && this.app.settings.appCheckToken) {\n            return this.app.settings.appCheckToken;\n        }\n        const appCheck = this._appCheckProvider.getImmediate({ optional: true });\n        if (appCheck) {\n            const result = await appCheck.getToken();\n            // TODO: What do we want to do if there is an error getting the token?\n            // Context: appCheck.getToken() will never throw even if an error happened. In the error case, a dummy token will be\n            // returned along with an error field describing the error. In general, we shouldn't care about the error condition and just use\n            // the token (actual or dummy) to send requests.\n            return result.token;\n        }\n        return null;\n    }\n    /**\n     * Stop running requests and prevent more from being created.\n     */\n    _delete() {\n        if (!this._deleted) {\n            this._deleted = true;\n            this._requests.forEach(request => request.cancel());\n            this._requests.clear();\n        }\n        return Promise.resolve();\n    }\n    /**\n     * Returns a new firebaseStorage.Reference object referencing this StorageService\n     * at the given Location.\n     */\n    _makeStorageReference(loc) {\n        return new Reference(this, loc);\n    }\n    /**\n     * @param requestInfo - HTTP RequestInfo object\n     * @param authToken - Firebase auth token\n     */\n    _makeRequest(requestInfo, requestFactory, authToken, appCheckToken, retry = true) {\n        if (!this._deleted) {\n            const request = makeRequest(requestInfo, this._appId, authToken, appCheckToken, requestFactory, this._firebaseVersion, retry, this._isUsingEmulator);\n            this._requests.add(request);\n            // Request removes itself from set when complete.\n            request.getPromise().then(() => this._requests.delete(request), () => this._requests.delete(request));\n            return request;\n        }\n        else {\n            return new FailRequest(appDeleted());\n        }\n    }\n    async makeRequestWithTokens(requestInfo, requestFactory) {\n        const [authToken, appCheckToken] = await Promise.all([\n            this._getAuthToken(),\n            this._getAppCheckToken()\n        ]);\n        return this._makeRequest(requestInfo, requestFactory, authToken, appCheckToken).getPromise();\n    }\n}\n\nconst name = \"@firebase/storage\";\nconst version = \"0.13.14\";\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Type constant for Firebase Storage.\n */\nconst STORAGE_TYPE = 'storage';\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Downloads the data at the object's location. Returns an error if the object\n * is not found.\n *\n * To use this functionality, you have to whitelist your app's origin in your\n * Cloud Storage bucket. See also\n * https://cloud.google.com/storage/docs/configuring-cors\n *\n * @public\n * @param ref - StorageReference where data should be downloaded.\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\n * retrieve.\n * @returns A Promise containing the object's bytes\n */\nfunction getBytes(ref, maxDownloadSizeBytes) {\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\n    return getBytesInternal(ref, maxDownloadSizeBytes);\n}\n/**\n * Uploads data to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - {@link StorageReference} where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the data to upload.\n * @returns A Promise containing an UploadResult\n */\nfunction uploadBytes(ref, data, metadata) {\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\n    return uploadBytes$1(ref, data, metadata);\n}\n/**\n * Uploads a string to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - {@link StorageReference} where string should be uploaded.\n * @param value - The string to upload.\n * @param format - The format of the string to upload.\n * @param metadata - Metadata for the string to upload.\n * @returns A Promise containing an UploadResult\n */\nfunction uploadString(ref, value, format, metadata) {\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\n    return uploadString$1(ref, value, format, metadata);\n}\n/**\n * Uploads data to this object's location.\n * The upload can be paused and resumed, and exposes progress updates.\n * @public\n * @param ref - {@link StorageReference} where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the data to upload.\n * @returns An UploadTask\n */\nfunction uploadBytesResumable(ref, data, metadata) {\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\n    return uploadBytesResumable$1(ref, data, metadata);\n}\n/**\n * A `Promise` that resolves with the metadata for this object. If this\n * object doesn't exist or metadata cannot be retrieved, the promise is\n * rejected.\n * @public\n * @param ref - {@link StorageReference} to get metadata from.\n */\nfunction getMetadata(ref) {\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\n    return getMetadata$1(ref);\n}\n/**\n * Updates the metadata for this object.\n * @public\n * @param ref - {@link StorageReference} to update metadata for.\n * @param metadata - The new metadata for the object.\n *     Only values that have been explicitly set will be changed. Explicitly\n *     setting a value to null will remove the metadata.\n * @returns A `Promise` that resolves with the new metadata for this object.\n */\nfunction updateMetadata(ref, metadata) {\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\n    return updateMetadata$1(ref, metadata);\n}\n/**\n * List items (files) and prefixes (folders) under this storage reference.\n *\n * List API is only available for Firebase Rules Version 2.\n *\n * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\n * delimited folder structure.\n * Refer to GCS's List API if you want to learn more.\n *\n * To adhere to Firebase Rules's Semantics, Firebase Storage does not\n * support objects whose paths end with \"/\" or contain two consecutive\n * \"/\"s. Firebase Storage List API will filter these unsupported objects.\n * list() may fail if there are too many unsupported objects in the bucket.\n * @public\n *\n * @param ref - {@link StorageReference} to get list from.\n * @param options - See {@link ListOptions} for details.\n * @returns A `Promise` that resolves with the items and prefixes.\n *      `prefixes` contains references to sub-folders and `items`\n *      contains references to objects in this folder. `nextPageToken`\n *      can be used to get the rest of the results.\n */\nfunction list(ref, options) {\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\n    return list$1(ref, options);\n}\n/**\n * List all items (files) and prefixes (folders) under this storage reference.\n *\n * This is a helper method for calling list() repeatedly until there are\n * no more results. The default pagination size is 1000.\n *\n * Note: The results may not be consistent if objects are changed while this\n * operation is running.\n *\n * Warning: `listAll` may potentially consume too many resources if there are\n * too many results.\n * @public\n * @param ref - {@link StorageReference} to get list from.\n *\n * @returns A `Promise` that resolves with all the items and prefixes under\n *      the current storage reference. `prefixes` contains references to\n *      sub-directories and `items` contains references to objects in this\n *      folder. `nextPageToken` is never returned.\n */\nfunction listAll(ref) {\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\n    return listAll$1(ref);\n}\n/**\n * Returns the download URL for the given {@link StorageReference}.\n * @public\n * @param ref - {@link StorageReference} to get the download URL for.\n * @returns A `Promise` that resolves with the download\n *     URL for this object.\n */\nfunction getDownloadURL(ref) {\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\n    return getDownloadURL$1(ref);\n}\n/**\n * Deletes the object at this location.\n * @public\n * @param ref - {@link StorageReference} for object to delete.\n * @returns A `Promise` that resolves if the deletion succeeds.\n */\nfunction deleteObject(ref) {\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\n    return deleteObject$1(ref);\n}\nfunction ref(serviceOrRef, pathOrUrl) {\n    serviceOrRef = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(serviceOrRef);\n    return ref$1(serviceOrRef, pathOrUrl);\n}\n/**\n * @internal\n */\nfunction _getChild(ref, childPath) {\n    return _getChild$1(ref, childPath);\n}\n/**\n * Gets a {@link FirebaseStorage} instance for the given Firebase app.\n * @public\n * @param app - Firebase app to get {@link FirebaseStorage} instance for.\n * @param bucketUrl - The gs:// url to your Firebase Storage Bucket.\n * If not passed, uses the app's default Storage Bucket.\n * @returns A {@link FirebaseStorage} instance.\n */\nfunction getStorage(app = (0,_firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApp)(), bucketUrl) {\n    app = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(app);\n    const storageProvider = (0,_firebase_app__WEBPACK_IMPORTED_MODULE_0__._getProvider)(app, STORAGE_TYPE);\n    const storageInstance = storageProvider.getImmediate({\n        identifier: bucketUrl\n    });\n    const emulator = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getDefaultEmulatorHostnameAndPort)('storage');\n    if (emulator) {\n        connectStorageEmulator(storageInstance, ...emulator);\n    }\n    return storageInstance;\n}\n/**\n * Modify this {@link FirebaseStorage} instance to communicate with the Cloud Storage emulator.\n *\n * @param storage - The {@link FirebaseStorage} instance\n * @param host - The emulator host (ex: localhost)\n * @param port - The emulator port (ex: 5001)\n * @param options - Emulator options. `options.mockUserToken` is the mock auth\n * token to use for unit testing Security Rules.\n * @public\n */\nfunction connectStorageEmulator(storage, host, port, options = {}) {\n    connectStorageEmulator$1(storage, host, port, options);\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Downloads the data at the object's location. Returns an error if the object\n * is not found.\n *\n * To use this functionality, you have to whitelist your app's origin in your\n * Cloud Storage bucket. See also\n * https://cloud.google.com/storage/docs/configuring-cors\n *\n * This API is not available in Node.\n *\n * @public\n * @param ref - StorageReference where data should be downloaded.\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\n * retrieve.\n * @returns A Promise that resolves with a Blob containing the object's bytes\n */\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction getBlob(ref, maxDownloadSizeBytes) {\n    throw new Error('getBlob() is only available in Browser-like environments');\n}\n/**\n * Downloads the data at the object's location. Raises an error event if the\n * object is not found.\n *\n * This API is only available in Node.\n *\n * @public\n * @param ref - StorageReference where data should be downloaded.\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\n * retrieve.\n * @returns A stream with the object's data as bytes\n */\nfunction getStream(ref, maxDownloadSizeBytes) {\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\n    return getStreamInternal(ref, maxDownloadSizeBytes);\n}\n\n/**\n * Cloud Storage for Firebase\n *\n * @packageDocumentation\n */\nfunction factory(container, { instanceIdentifier: url }) {\n    const app = container.getProvider('app').getImmediate();\n    const authProvider = container.getProvider('auth-internal');\n    const appCheckProvider = container.getProvider('app-check-internal');\n    return new FirebaseStorageImpl(app, authProvider, appCheckProvider, url, _firebase_app__WEBPACK_IMPORTED_MODULE_0__.SDK_VERSION);\n}\nfunction registerStorage() {\n    (0,_firebase_app__WEBPACK_IMPORTED_MODULE_0__._registerComponent)(new _firebase_component__WEBPACK_IMPORTED_MODULE_2__.Component(STORAGE_TYPE, factory, \"PUBLIC\" /* ComponentType.PUBLIC */).setMultipleInstances(true));\n    (0,_firebase_app__WEBPACK_IMPORTED_MODULE_0__.registerVersion)(name, version);\n}\nregisterStorage();\n\n\n//# sourceMappingURL=index.node.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@firebase+storage@0.13.14_@firebase+app@0.13.2/node_modules/@firebase/storage/dist/node-esm/index.node.esm.js\n");

/***/ })

};
;