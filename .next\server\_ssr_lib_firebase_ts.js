"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_lib_firebase_ts";
exports.ids = ["_ssr_lib_firebase_ts"];
exports.modules = {

/***/ "(ssr)/./lib/firebase.ts":
/*!*************************!*\
  !*** ./lib/firebase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   authService: () => (/* binding */ authService),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   mlResultsService: () => (/* binding */ mlResultsService),\n/* harmony export */   patientService: () => (/* binding */ patientService),\n/* harmony export */   scanService: () => (/* binding */ scanService),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(ssr)/./node_modules/.pnpm/firebase@11.10.0/node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/.pnpm/firebase@11.10.0/node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/.pnpm/firebase@11.10.0/node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(ssr)/./node_modules/.pnpm/firebase@11.10.0/node_modules/firebase/storage/dist/index.mjs\");\n// Firebase configuration and initialization\n\n\n\n\n// Firebase configuration\nconst firebaseConfig = {\n    apiKey: \"your-firebase-api-key\" || 0,\n    authDomain: \"your-project.firebaseapp.com\" || 0,\n    projectId: \"your-project-id\" || 0,\n    storageBucket: \"your-project.appspot.com\" || 0,\n    messagingSenderId: \"your-sender-id\" || 0,\n    appId: \"your-app-id\" || 0,\n    measurementId: \"your-measurement-id\" || 0\n};\n// Initialize Firebase\nlet app;\nlet db;\nlet auth;\nlet storage;\n// Check if we're in a browser environment before initializing Firebase\nif (false) {}\n// Authentication service\nconst authService = {\n    // Register a new user\n    async register (email, password, userData) {\n        if (!auth) return null;\n        const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.createUserWithEmailAndPassword)(auth, email, password);\n        const user = userCredential.user;\n        // Create user profile in Firestore\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, \"users\", user.uid), {\n            ...userData,\n            email,\n            createdAt: new Date(),\n            updatedAt: new Date()\n        });\n        return user;\n    },\n    // Login user\n    async login (email, password) {\n        if (!auth) return null;\n        const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithEmailAndPassword)(auth, email, password);\n        return userCredential.user;\n    },\n    // Logout user\n    async logout () {\n        if (!auth) return;\n        await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signOut)(auth);\n    },\n    // Get current user profile\n    async getCurrentUserProfile () {\n        if (!auth || !auth.currentUser) return null;\n        const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, \"users\", auth.currentUser.uid);\n        const docSnap = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(docRef);\n        if (docSnap.exists()) {\n            return {\n                id: docSnap.id,\n                ...docSnap.data()\n            };\n        } else {\n            return null;\n        }\n    }\n};\n// Patient service\nconst patientService = {\n    // Get all patients\n    async getAllPatients () {\n        if (!db) return [];\n        const patientsCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(db, \"patients\");\n        const patientsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(patientsCollection);\n        return patientsSnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data()\n            }));\n    },\n    // Get patient by ID\n    async getPatientById (patientId) {\n        if (!db) return null;\n        const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, \"patients\", patientId);\n        const docSnap = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(docRef);\n        if (docSnap.exists()) {\n            return {\n                id: docSnap.id,\n                ...docSnap.data()\n            };\n        } else {\n            return null;\n        }\n    },\n    // Create new patient\n    async createPatient (patientData) {\n        if (!db) return null;\n        const patientRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(db, \"patients\"));\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.setDoc)(patientRef, {\n            ...patientData,\n            createdAt: new Date(),\n            updatedAt: new Date()\n        });\n        return {\n            id: patientRef.id,\n            ...patientData\n        };\n    },\n    // Update patient\n    async updatePatient (patientId, patientData) {\n        if (!db) return null;\n        const patientRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, \"patients\", patientId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.setDoc)(patientRef, {\n            ...patientData,\n            updatedAt: new Date()\n        }, {\n            merge: true\n        });\n        return {\n            id: patientId,\n            ...patientData\n        };\n    }\n};\n// Scan service\nconst scanService = {\n    // Upload scan image\n    async uploadScanImage (file, patientId, scanType) {\n        if (!storage || !db) return null;\n        const fileExtension = file.name.split(\".\").pop();\n        const fileName = `${patientId}_${scanType}_${Date.now()}.${fileExtension}`;\n        const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(storage, `scans/${fileName}`);\n        await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(storageRef, file);\n        const downloadURL = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getDownloadURL)(storageRef);\n        // Create scan record in Firestore\n        const scanRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(db, \"scans\"));\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.setDoc)(scanRef, {\n            patientId,\n            scanType,\n            imageUrl: downloadURL,\n            fileName,\n            uploadedAt: new Date(),\n            status: \"pending_analysis\"\n        });\n        return {\n            id: scanRef.id,\n            patientId,\n            scanType,\n            imageUrl: downloadURL,\n            fileName,\n            uploadedAt: new Date(),\n            status: \"pending_analysis\"\n        };\n    },\n    // Get scans by patient ID\n    async getScansByPatientId (patientId) {\n        if (!db) return [];\n        const scansQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(db, \"scans\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)(\"patientId\", \"==\", patientId));\n        const scansSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(scansQuery);\n        return scansSnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data()\n            }));\n    },\n    // Get recent scans\n    async getRecentScans (limit = 10) {\n        if (!db) {\n            // Return mock data for demo purposes\n            return Array.from({\n                length: limit\n            }, (_, i)=>({\n                    id: `scan-${i}`,\n                    patientId: `patient-${i % 5}`,\n                    scanType: i % 2 === 0 ? \"MRI\" : \"CT\",\n                    imageUrl: \"/placeholder.svg?height=300&width=300\",\n                    fileName: `scan_${i}.dcm`,\n                    uploadedAt: new Date(Date.now() - i * 86400000),\n                    status: i % 3 === 0 ? \"pending_analysis\" : i % 3 === 1 ? \"analyzed\" : \"reviewed\"\n                }));\n        }\n        const scansCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(db, \"scans\");\n        const scansSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(scansCollection);\n        return scansSnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data()\n            })).slice(0, limit);\n    }\n};\n// ML results service\nconst mlResultsService = {\n    // Get diagnosis results\n    async getDiagnosisResults (scanId) {\n        if (!db) return null;\n        const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, \"diagnoses\", scanId);\n        const docSnap = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(docRef);\n        if (docSnap.exists()) {\n            return {\n                id: docSnap.id,\n                ...docSnap.data()\n            };\n        } else {\n            return null;\n        }\n    },\n    // Get prognosis results\n    async getPrognosisResults (patientId) {\n        if (!db) return null;\n        const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, \"prognoses\", patientId);\n        const docSnap = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(docRef);\n        if (docSnap.exists()) {\n            return {\n                id: docSnap.id,\n                ...docSnap.data()\n            };\n        } else {\n            return null;\n        }\n    },\n    // Get treatment plan\n    async getTreatmentPlan (patientId) {\n        if (!db) return null;\n        const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, \"treatments\", patientId);\n        const docSnap = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(docRef);\n        if (docSnap.exists()) {\n            return {\n                id: docSnap.id,\n                ...docSnap.data()\n            };\n        } else {\n            return null;\n        }\n    },\n    // Get recent insights\n    async getRecentInsights (limit = 5) {\n        if (!db) {\n            // Return mock data for demo purposes\n            return Array.from({\n                length: limit\n            }, (_, i)=>({\n                    id: `insight-${i}`,\n                    title: [\n                        \"Treatment Resistance Detected\",\n                        \"Potential Clinical Trial Match\",\n                        \"Anomaly in Tumor Growth Pattern\",\n                        \"Genetic Marker Identified\",\n                        \"Immunotherapy Response Predicted\"\n                    ][i],\n                    description: [\n                        \"Patient is showing signs of resistance to the current treatment regimen. Consider adjusting the protocol based on the latest genomic analysis.\",\n                        \"Patient matches the criteria for the new quantum-enhanced radiotherapy trial. Genomic profile indicates high likelihood of positive response.\",\n                        \"Unusual growth pattern detected in patient's latest scan. The peripheral region shows unexpected metabolic activity that differs from typical progression.\",\n                        \"New genetic marker identified that may indicate sensitivity to targeted therapy. Consider genomic consultation.\",\n                        \"Based on immune profile, patient is likely to respond well to immunotherapy. Consider as adjuvant to current treatment.\"\n                    ][i],\n                    confidence: 0.7 + i * 0.05,\n                    timestamp: new Date(Date.now() - i * 3600000),\n                    references: [\n                        {\n                            title: \"Recent study on treatment resistance in glioblastoma\",\n                            url: \"#\"\n                        },\n                        {\n                            title: \"Similar case study from Memorial Cancer Institute\",\n                            url: \"#\"\n                        }\n                    ]\n                }));\n        }\n        const insightsCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(db, \"insights\");\n        const insightsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(insightsCollection);\n        return insightsSnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data()\n            })).slice(0, limit);\n    },\n    // Get treatment efficacy data\n    async getTreatmentEfficacyData () {\n        // Return mock data for demo purposes\n        return [\n            {\n                name: \"Standard Chemo\",\n                metrics: {\n                    tumor_reduction: 65,\n                    side_effects: 70,\n                    quality_of_life: 50,\n                    cost_effectiveness: 75,\n                    long_term_outlook: 60\n                }\n            },\n            {\n                name: \"Targeted Therapy\",\n                metrics: {\n                    tumor_reduction: 80,\n                    side_effects: 40,\n                    quality_of_life: 75,\n                    cost_effectiveness: 50,\n                    long_term_outlook: 85\n                }\n            },\n            {\n                name: \"Quantum-Enhanced\",\n                metrics: {\n                    tumor_reduction: 90,\n                    side_effects: 30,\n                    quality_of_life: 85,\n                    cost_effectiveness: 60,\n                    long_term_outlook: 95\n                }\n            }\n        ];\n    },\n    // Get survival data\n    async getSurvivalData () {\n        // Return mock data for demo purposes\n        return [\n            {\n                name: \"Patient Prediction\",\n                values: Array.from({\n                    length: 60\n                }, (_, i)=>({\n                        time: i,\n                        probability: Math.exp(-0.015 * i) * (1 - 0.2 * Math.sin(i / 10)),\n                        lowerBound: Math.exp(-0.02 * i) * (1 - 0.2 * Math.sin(i / 10) - 0.05),\n                        upperBound: Math.exp(-0.01 * i) * (1 - 0.2 * Math.sin(i / 10) + 0.05)\n                    })),\n                baseline: Array.from({\n                    length: 60\n                }, (_, i)=>({\n                        time: i,\n                        probability: Math.exp(-0.02 * i)\n                    }))\n            },\n            {\n                name: \"With Treatment\",\n                values: Array.from({\n                    length: 60\n                }, (_, i)=>({\n                        time: i,\n                        probability: Math.exp(-0.008 * i) * (1 - 0.1 * Math.sin(i / 15)),\n                        lowerBound: Math.exp(-0.012 * i) * (1 - 0.1 * Math.sin(i / 15) - 0.05),\n                        upperBound: Math.exp(-0.005 * i) * (1 - 0.1 * Math.sin(i / 15) + 0.05)\n                    }))\n            }\n        ];\n    }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/firebase.ts\n");

/***/ })

};
;